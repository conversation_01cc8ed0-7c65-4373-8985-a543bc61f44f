const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // 菜单列表配置 - 统一为二维数组格式
    // 单行布局：[[menu1, menu2, menu3]] - 长度为1的二维数组
    // 分组布局：[[leftMenus], [rightMenus]] - 长度为2的二维数组
    menuList: {
      type: Array,
      value: [],
    },
    // 选中状态数据
    selectData: {
      type: Object,
      value: {},
    },
    // 当前展开的菜单key
    activeExpanded: {
      type: String,
      value: "",
    },
    // 外部样式类
    customClass: {
      type: String,
      value: "",
    },
    showPopupFilterMenu: {
      type: Boolean,
      value: false,
    },
    // Tab类型，用于区分不同Tab的地区缓存
    tabType: {
      type: String,
      value: "announcement", // 默认为公告Tab
    },
    // 是否隐藏地区列表
    hideRegionList: {
      type: Boolean,
      value: false,
    },
    // 是否显示筛选按钮
    isShowFilter: {
      type: Boolean,
      value: true,
    },
    isExam: {
      type: Boolean,
      value: false,
    },
    isLogin: {
      type: Boolean,
      value: false,
    },
    activeApplyExpanded: {
      type: String,
      value: "",
    },
    examPopu: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 选中的地区列表
    selectedRegions: [],
    showCacelGrouponDialog: false,
  },

  /**
   * 组件的计算属性
   */
  computed: {
    // 是否为分组布局（二维数组长度为2）
    isGroupLayout() {
      const { menuList } = this.data
      return Array.isArray(menuList) && menuList.length === 2
    },

    // 左侧菜单列表（单行布局时为全部菜单，分组布局时为第一组）
    leftMenuList() {
      const { menuList } = this.data
      if (!Array.isArray(menuList) || menuList.length === 0) return []
      return menuList[0] || []
    },

    // 右侧菜单列表（仅分组布局时使用）
    rightMenuList() {
      const { menuList } = this.data
      if (!this.isGroupLayout()) return []
      return menuList[1] || []
    },
  },

  /**
   * 数据观察器
   */
  observers: {
    // 监听tabType变化，重新加载对应的地区数据
    tabType: function (newTabType, oldTabType) {
      if (newTabType && newTabType !== oldTabType) {
        console.log("TabType变化:", oldTabType, "->", newTabType)
      }
    },
  },

  /**
   * 组件生命周期
   */
  pageLifetimes: {
    // 组件所在的页面被展示时执行
    show() {
      console.log("组件showl")
    },
  },
  lifetimes: {
    attached() {},
    detached() {
      // 清理资源
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async getInfo() {
      return await UTIL.request(API.getResumeInfo)
    },
    async handleLoginClick(e) {
      console.log(e)
      const { type, key } = e.detail.payload
      const data = {
        currentTarget: {
          dataset: {
            type,
            key,
          },
        },
      }
      // this.handleMenuClick(e)
      if (key === "fit_me") {
        const resumeInfo = await APP.getResume()
        console.log(resumeInfo)
        const isImprove = APP.isImprove(resumeInfo.info)
        console.log(isImprove, "=------------")
        if (!isImprove) {
          this.setData({
            showCacelGrouponDialog: true,
          })
          return
        }
      }
      this.handleMenuClick(data)
    },
    handleMenuClick(e) {
      const { type, key } = e.currentTarget.dataset
      const { menuList } = this.data

      let currentItem = null

      // 从所有菜单组中查找菜单项
      if (Array.isArray(menuList)) {
        for (const menuGroup of menuList) {
          if (Array.isArray(menuGroup)) {
            currentItem = menuGroup.find((item) => item.key === key)
            if (currentItem) break
          }
        }
      }
      // if (key === "fit_me") {
      //   if (true) {
      //     this.setData({
      //       showCacelGrouponDialog: true,
      //     })
      //     return
      //   }
      // }
      // 将点击事件抛出给父组件处理
      this.triggerEvent("menuClick", {
        type: type,
        key: key,
        currentItem: currentItem,
        menuList: menuList,
      })
    },
    handleCacelGroupon() {
      this.setData({
        showCacelGrouponDialog: false,
      })
    },
    hideCacelGrouponDialog() {
      ROUTER.navigateTo({
        path: "/pages/my/resume/index",
      })
      this.setData({
        showCacelGrouponDialog: false,
      })
    },
  },
})
