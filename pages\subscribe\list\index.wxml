<block wx:if="{{!pageLoading && subscribedData}}">
  <common-header id="commonHeader" title="订阅" show_white="{{show_white}}">
    <view slot="left" class="lefts">
      <image class="left-arrow" wx:if="{{show_white}}" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
      <image class="left-arrow" wx:else catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_left_white.png"></image>
    </view>
  </common-header>

  <block wx:if="{{isSelectListLength}}">
    <view class="main-content">
      <view class="exam-scope-card">
        <image class="bg-img" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_exam_card_bg.png"></image>
        <view class="pr-box">
          <view class="top-box">
            <view class="title">已订阅考试范围</view>
            <button-authorize wx:if="{{selectList.length< subscribedData.exam_type_limit }}" isBindPhone="{{isLogin}}" bind:onAuthorize="handleAddSubscribe">
              <view class="add-btn">
                <image class="btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_white.png"></image>
                新增
              </view>
            </button-authorize>
          </view>
          <view class="scope-list">
            <block wx:if="{{selectList.length>0}}">
              <view class="scope-list-item" bindtap="editExam" wx:for="{{selectList}}" wx:key="index" data-item="{{item}}">
                <view class="text">{{item.text}}</view>
                <view class="close-box" catchtap="handleDeleteSubscribe" data-exam-type="{{item.exam_type}}">
                  <image class="close" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_exam_close.png"></image>
                </view>
              </view>
            </block>
            <button-authorize wx:else isBindPhone="{{isLogin}}" bind:onAuthorize="handleAddSubscribe">
              <view class="scope-list-item" style="padding:24rpx 32rpx">
                <view class="text" style="color: rgba(194, 197, 204, 1);">去添加关注考试范围</view>
              </view>
            </button-authorize>
          </view>
        </view>
      </view>
      <view class="fllow-card" wx:if="{{subscribedData.is_subscribe==0}}">
        <view class="text">{{subscribedData.subscribe_icons.follow_wx_public.title}}</view>
        <view class="tip-xin-list">
          <view class="tip-xin" wx:for="{{subscribedData.subscribe_icons.follow_wx_public.list}}" wx:key="index">
            <image class="img" src="{{item.icon}}"></image>
            {{item.text}}
          </view>
        </view>
        <view class="fllow-box" bindtap="tapMenuItem">去关注公众号
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <!-- 选择考试方向 -->
    <view class="scope-card">
      <block wx:if="{{activeIndex==0}}">
        <view class="pr-box">
          <view class="text-top">
            <view class="title">请选择关注的考试方向</view>
            <view class="label">选择后将推送相关考试公告</view>
          </view>
          <view class="asd">
            <view class="exam-list">
              <view class="exam-list-item {{selectedExamTypeId === item.id ? 'active' : ''}}" wx:for="{{examTypeList}}" wx:key="id" data-index="{{index}}" bindtap="handleExamTypeSelect">
                {{item.type_name}}
              </view>
            </view>
          </view>
        </view>
        <view class="action-bar-box" wx:if="{{activeIndex==0}}">
          <view class="action-bar container flex-justify_between">
            <view class="confirm-btn {{canNext ? '' : 'disabled'}}" style="opacity: {{canNext ? 1 : 0.5}}" bindtap="changeNext">下一步</view>
          </view>
        </view>
      </block>
      <block wx:if="{{activeIndex==1}}">
        <view class="pr-box">
          <view class="text-top border-bottom">
            <view class="title">请选择报考地区</view>
            <view class="label">选择后将推送相关考试公告</view>
          </view>
          <region-select-box style="flex: 1;min-height: 0;position: relative;z-index: 4;" subscribedData="{{subscribedData}}" isSubscribe noPadding show="{{showExamRegionPopup}}" filterKey="apply_region" isShowBg="{{false}}" popupHeight="100%" selectedRegions="{{examPopuSelectForTemplate.apply_region}}" inPopup="{{true}}" selectLevel="{{3}}" maxSelectCount="{{subscribedData.area_limit}}" bind:confirmSelection="handlePopuRegionSelection" bind:stepSelection="stepSelection" />
        </view>

      </block>
    </view>
  </block>
</block>
<!-- <van-overlay show="{{ overlayShow }}" z-index="999" bind:click="onClickHide">
  <view class="wrapper">
    <view class="subscribe-overlay">
      <image mode="widthFix" class="top-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_popu_img.png"></image>
      <view class="content-box">
        <view class="title-top">
          <image class="yuan" mode="widthFix" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_yuan_g.png"></image>
          订阅成功
        </view>
      </view>
    </view>
  </view>
</van-overlay>
<van-popup show="{{ show }}" custom-style="height: 80vh" round z-index="999" position="bottom" bind:close="onClose">
  <view class="popu-box">
    <view class="popu-box-top">
      <view class="item-text active">
        <view class="yuan">1</view>
        <view class="text">考试方向</view>
      </view>
      <view class="line"></view>
      <view class="item-text">
        <view class="yuan">2</view>
        <view class="text">报考地区</view>
      </view>
    </view>
    <view class="popu-box-content">
      <block wx:if="{{activeIndex==0}}">
        <view class="text-top">
          <view class="title">请选择关注的考试方向</view>
          <view class="label">选择后会推荐相关考试公告</view>
        </view>
        <view class="exam-list">
          <view class="exam-list-item {{selectedExamTypeId === item.id ? 'active' : ''}}" wx:for="{{examTypeList}}" wx:key="id" data-index="{{index}}" bindtap="handleExamTypeSelect">
            {{item.type_name}}
          </view>
        </view>
      </block>
      <block wx:if="{{activeIndex==1}}">
        <view class="text-top" style="padding-bottom: 30rpx;">
          <view class="title">请选择报考地区</view>
          <view class="label">选择地区会让推荐更精准</view>
        </view>
        <region-select-box style="flex: 1;min-height: 0;" isSubscribe noPadding show="{{showExamRegionPopup}}" filterKey="apply_region" isShowBg="{{false}}" popupHeight="100%" selectedRegions="{{examPopuSelectForTemplate.apply_region}}" inPopup="{{true}}" selectLevel="{{2}}" maxSelectCount="{{3}}" bind:confirmSelection="handlePopuRegionSelection" bind:stepSelection="stepSelection" />
      </block>
    </view>
    
  </view>
</van-popup> -->