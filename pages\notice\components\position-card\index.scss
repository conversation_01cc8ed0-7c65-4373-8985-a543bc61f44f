.position-list {
  // padding: 32rpx;
  .position-list-item {
    background: #fff;
    padding: 30rpx;
    border-radius: 16rpx;
    position: relative;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .label-img {
      width: 136rpx;
      height: 54rpx;
      position: absolute;
      right: 32rpx;
      top: 0;
    }

    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      margin-bottom: 22rpx;
      font-weight: 600;
    }

    .label-item {
      display: flex;
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-bottom: 16rpx;

      .num {
        margin-left: 32rpx;
        flex: 1;
        .number {
          font-size: 24rpx;
          color: rgba(60, 61, 66, 1);
          margin: 0 5rpx;
        }
      }
    }
  }
}
