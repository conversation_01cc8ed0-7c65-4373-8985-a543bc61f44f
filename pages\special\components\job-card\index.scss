.job-card {
  width: 100%;
  padding: 32rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  background: #ffffff;

  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    .name {
      font-size: 32rpx;
      color: #22242e;
      font-weight: 600;
    }
    .right-btn {
      border-radius: 8rpx;
      border: 2rpx solid #ebecf0;
      background: #ffffff;
      width: 136rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .icon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 8rpx;
      }
      .text {
        font-size: 24rpx;
        color: #919499;
      }
    }
    .red {
      border: 2rpx solid rgba(230, 0, 3, 0.5);
      .text {
        color: #e60003;
      }
    }
  }
  .data-area {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .list-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      box-sizing: border-box;
      &:nth-child(odd) {
        width: 60%;
        padding-right: 0.213rem; // 16rpx 右边距
      }

      &:nth-child(even) {
        width: 40%;
      }
      .title {
        width: 120rpx;
        flex-shrink: 0;
        text-align: left;
        font-size: 24rpx;
        color: #919499;
      }
      .content {
        color: #3c3d42;
        font-weight: 400;
        font-size: 24rpx;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .bold {
        font-weight: bold !important;
      }
      .red {
        font-size: 24rpx;
        color: #e60003;
        font-weight: 600;
      }
    }
  }
  .mb0 {
    margin-bottom: 0 !important;
  }
  .w100 {
    width: 100% !important;
  }
}
