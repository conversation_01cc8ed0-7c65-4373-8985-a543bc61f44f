// components/tabbar/home-tabbar/index.js
import { getBaseCampusCache } from "@/utils/cache/baseCache"
const ROUTER = require("@/services/mpRouter")
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    active: {
      type: String,
      value: "home",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLoad: false,
    list: [
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_home.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_home_select.png",
        pagePath: "pages/home/<USER>/index",
        pathKey: "home",
        text: "公告",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_job.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_job_select.png",
        pagePath: "pages/job/list/index",
        pathKey: "job",
        text: "职位",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_bigdata.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_bigdata_select.png",
        pagePath: "pages/special/big-data/index",
        pathKey: "big_data",
        text: "报考大数据",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_my.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_my_select.png",
        pagePath: "pages/my/home/<USER>",
        pathKey: "my",
        text: "我的",
      },
    ],

    menus: [],
    activeCampusId: null,
  },
  pageLifetimes: {
    show() {
      // this.loadMenus()
    },
    ready() {
      this.loadMenus()
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    loadMenus: async function () {
      try {
        if (!this.data.isLoad) {
          await APP.checkLoadRequest()
        }
      } catch (error) {}

      this.setData({
        menus: APP.globalData.serverConfig.menus_list,
        isLoad: true,
      })
    },
    onChange(event) {
      const { pagePath, pathKey, param } = event.currentTarget.dataset.item
      console.log(pagePath)
      if (pathKey === "big_data") {
        ROUTER.navigateTo({ path: "/" + pagePath, query: param })
        return
      }
      wx.switchTab({
        url: "/" + pagePath,
        success: function (res) {},
        fail: function (res) {
          console.log(res)
        },
      })
    },
  },
})
