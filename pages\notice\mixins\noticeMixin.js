/**
 * 公告-合辑详情  Mixin
 * 提供通用的地区选择功能，包括省市区三级联动、缓存管理等
 * 使用方式：在页面的 JS 文件中通过 Object.assign 混入
 */

const UTIL = require("@/utils/util")
const API = require("@/config/api")
const ROUTER = require("@/services/mpRouter")
const APP = getApp()
const { processMenuList } = require("@/services/menuServices")
const {
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  setJobDetailPopuSelectForTemplateCache,
  getJobDetailPopuSelectForTemplateCache,
  setOfficialNewsCache,
  getOfficialNewsCache,
} = require("@/utils/cache/filterCache")

const { handleMultiSelect } = require("@/services/selectionService")

const noticeMixin = {
  noticeData: {
    // 静态tab配置（所有可能的tab）
    allTabsConfig: [
      { key: "detail", title: "公告详情", alwaysShow: true },
      { key: "position", title: "职位列表", conditionField: "job_num" },
      { key: "official", title: "官方动态", conditionField: "notice_num" },
    ],
    // 动态生成的tab列表
    tabList: ["公告详情"],
    // 当前激活的tab索引
    activeIndex: 0,
    // tab键值到索引的映射
    tabKeyToIndex: { detail: 0 },
    // 索引到tab键值的映射
    indexToTabKey: { 0: "detail" },
    isExpanded: false,
    showToggle: false,
    maxHeight: 2500, // 默认收起状态的最大高度(px)
    actualHeight: 0, // 内容实际高度
    // 分页相关状态
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    jobList: [],
    officialList: [],
    activeExpanded: "",
    examPopuSelectForTemplate: {},
    examMenuData: {},
    examList: [],
    // van-popup弹窗内的地区选择弹窗控制
    showExamRegionPopup: false,
    show: false,
    activeExamExpanded: "",
    examRegionPopupTop: 0,
    // 新增字段
    contentBoxTopHeight: 120, // content-box-top高度
    selectBoxTop: 220, // select-box-content的top值
    selectBoxSticky: false, // select-box 是否处于吸顶状态
    selectBoxPopupTop: 0, // select-box 弹窗的位置
    savedScrollTop: 0, // 保存的滚动位置
    isOpenedInStickyMode: false, // 是否在吸顶状态下打开的弹窗
    showPopupExam: false,
    hasPageIcon: false,

    // 公众号关注弹窗状态
    followShow: false,

    // select-box 弹窗相关数据
    showSelectBoxPopup: false,
    popuShow: false,
    // 专业弹窗
    majorShow: false,
    isfirstOpenExam: false,
    // 新增：考试列表弹窗状态管理
    hasUserClosedApplyStatus: false, // 用户是否手动关闭过apply_status
    shouldKeepApplyStatusExpanded: false, // 是否应该保持apply_status展开状态
    // 考试列表弹窗中强制选中的菜单项
    examForcedSelectedKeys: [],
    activeApplyExpanded: "",
    educationId: "",
  },
  noticeMethods: {
    /**
     * 初始化动态菜单
     * @param {Array} serverMenuList 服务器返回的菜单列表
     */
    async initDynamicMenu(serverMenuList) {
      if (serverMenuList && serverMenuList.length) {
        // 使用提取后的纯函数处理菜单
        const menuList = processMenuList(serverMenuList)
        console.log(menuList, "得到的的的的的的的的的饿的额的的的的饿的额的")
        if (this.data.pageType == "collection") {
          const collectionMenuData = {}
          serverMenuList.forEach((item) => {
            collectionMenuData[item.filter_key] = item
          })
          this.setData({
            collectionMenuData,
            menuList,
            collectionSelectForTemplate: this.getNoticeSelectFromMenuData(
              serverMenuList
            ),
          })
        } else {
          const hasPageIcon = menuList.some((menuGroup) =>
            menuGroup.some((item) => item.type === "page_icon")
          )
          const detailMenuData = {}
          serverMenuList.forEach((item) => {
            detailMenuData[item.filter_key] = item
          })

          this.setData({
            detailMenuData,
            menuList,
            jobDetailSelectForTemplate: this.getNoticeSelectFromMenuData(
              serverMenuList
            ),
            hasPageIcon,
          })
        }
      }
      const resumeInfo = await APP.getResume()
      console.log(resumeInfo, "简历信息")
      if (resumeInfo?.info?.education_record_list?.length > 0) {
        this.setData({
          educationId: resumeInfo?.info?.education_record_list[0].degree,
        })
        console.log(this.data.educationId)
      }
    },
    // 处理接口请求参数
    buildApiParams(selectedData) {
      console.log(selectedData, "拿到的")
      const apiParams = {}

      Object.keys(selectedData).forEach((keyName) => {
        const data =
          selectedData[keyName] || (keyName !== "filter_list" ? [] : {})
        if (keyName === "fit_me" || keyName === "has_tenure") {
          apiParams[keyName] = data[0] || null
        }
        if (keyName === "apply_region") {
          const regionData = data
            .map((region) => {
              // 如果已经是字符串格式，直接使用
              return region.key
            })
            .filter((regionCode) => regionCode) // 过滤掉空值
          console.log(regionData, "---------------")
          apiParams["region"] = regionData
        }
        if (keyName === "article_list") {
          const regionData = data.map((region) => {
            // 如果已经是字符串格式，直接使用
            return region.id
          })
          console.log(regionData, "---------------")
          apiParams["article_list"] = regionData
        }
        if (keyName === "filter_list") {
          Object.keys(data).forEach((filterKey) => {
            const filterValue = data[filterKey] || []

            // 招录人数 - 转换为数字
            if (filterKey === "need_num") {
              const numValue = Number(filterValue[0])
              const val = !isNaN(numValue) ? numValue : null
              apiParams[filterKey] = val
            } else {
              apiParams[filterKey] = filterValue
            }
          })
        }
        if (keyName === "tmp_major") {
          console.log(data, "111111111111111111")
          // 添加更严格的空值检查
          if (
            data &&
            Array.isArray(data.selectedMajorIds) &&
            data.selectedMajorIds.length > 0
          ) {
            apiParams[keyName] =
              data.selectedMajorIds[data.selectedMajorIds.length - 1]
          } else {
            apiParams[keyName] = null
          }
        } else if (
          keyName !== "fit_me" &&
          keyName !== "has_tenure" &&
          keyName !== "apply_region" &&
          keyName !== "article_list" &&
          keyName !== "filter_list"
        ) {
          // 只处理其他未特殊处理的字段
          apiParams[keyName] = data
        }
      })
      delete apiParams?.filter_list
      console.log("得到的参数", apiParams)
      return UTIL.convertArraysToString(apiParams)
    },

    // 获取职位列表
    async getJobList(filterConditions = {}, isLoadMore = false) {
      // 如果正在加载或没有更多数据，直接返回
      if (
        this.data.isLoading ||
        (!isLoadMore && !this.data.hasMore && this.data.page > 1)
      ) {
        return
      }

      try {
        // 确保传递的参数不为空，至少是一个空对象
        const requestParams = {
          page: this.data.page,
          ...filterConditions,
          id: this.PAGE_OPTIONS.id,
        }

        this.setData({
          isLoading: true,
        })

        const res = await UTIL.request(API.getJobList, requestParams)

        if (res && res.error && res.error.code === 0 && res.data) {
          console.log("获取职位列表成功:", res.data)

          // 处理职位列表数据
          const newList = res.data.list || []

          // 根据是否为加载更多来决定如何处理数据
          let updatedJobList
          if (isLoadMore) {
            // 分页加载：追加到现有数据
            updatedJobList = [...this.data.jobList, ...newList]
          } else {
            // 首次加载或筛选：直接使用新数据
            updatedJobList = newList
          }

          // 更新页面数据
          this.setData({
            jobList: updatedJobList,
            hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
            isLoading: false,
            isRequest: true,
          })

          return res.data
        } else {
          console.error("获取职位列表失败:", res)
          this.setData({
            isLoading: false,
            hasMore: false,
          })
          return null
        }
      } catch (error) {
        console.error("请求职位列表异常:", error)
        this.setData({
          isLoading: false,
        })
        return null
      }
    },
    // 获取文章列表
    async getArticleNoticeList() {
      let requestParams = {
        id: this.PAGE_OPTIONS.id,
      }
      const res = await UTIL.request(API.getArticleNoticeList, requestParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        const resData = res.data
        this.setData({
          officialList: resData.list,
        })
        console.log(resData, "------------")
      }
    },
    // 筛选菜单点击
    handleMenuClick(e) {
      const { type, currentItem } = e.detail || e.currentTarget.dataset
      const { showPopupFilterMenu, jobDetailSelectForTemplate } = this.data
      const filterKey = currentItem.filter_key
      console.log(currentItem, "----------------------------")
      if (type == "page" && filterKey == "filter_list") {
        this.hidePopupMenuFilter()
        ROUTER.navigateTo({
          path: "/pages/select/select-job/index",
          query: {
            article_id: this.PAGE_OPTIONS.id,
          },
        })
        return
      }
      const currentMenuSelected = jobDetailSelectForTemplate[filterKey]
      if (type == "dialog_major") {
        this.setData({
          majorShow: true,
        })
        return
      }

      if (type !== "apply_region") {
        this.setData({
          showRegionList: false,
        })
      }

      if (type === "check" || type === "apply_region") {
        this.hidePopupMenuFilter()
      } else if (
        showPopupFilterMenu === true &&
        this.data.activeExpanded === filterKey
      ) {
        this.hidePopupMenuFilter()
      } else {
        this.showPopupMenuFilter()
      }

      this.setData({
        activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
      })

      // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
      if (filterKey === "apply_region") {
        // 切换地区列表显示状态
        this.setData({
          showRegionList: !this.data.showRegionList,
        })
        return
      }

      // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
      if (type === "check") {
        console.log(currentMenuSelected, currentItem.value, "12312312321")
        const updatedValue = handleMultiSelect(
          currentMenuSelected || [],
          currentItem.value
        )

        this.setData({
          [`jobDetailSelectForTemplate.${filterKey}`]: updatedValue,
        })

        // 检查并同步filter_list中对应的键
        if (
          this.data.jobDetailSelectForTemplate?.filter_list?.[filterKey] !==
          undefined
        ) {
          console.log(`同步 filter_list.${filterKey} 的值:`, updatedValue)
          this.setData({
            [`jobDetailSelectForTemplate.filter_list.${filterKey}`]: updatedValue,
          })
        }

        setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
        this.applyFilter()
        return
      }
    },
    // async applyFilter() {
    //   // 重置分页状态，但不清空jobList，避免缺省图闪现
    //   this.setData({
    //     page: 1,
    //     hasMore: true,
    //   })
    //   console.log(this.data.jobDetailSelectForTemplate, "12312312")
    //   const apiParams = this.buildApiParams(
    //     this.data.jobDetailSelectForTemplate
    //   )
    //   console.log("拿到的参数", apiParams)
    //   await this.getJobList(apiParams, false)
    // },
    async applyFilter() {
      // 重置分页状态，但不清空articleList，避免缺省图闪现
      this.setData({
        page: 1,
        hasMore: true,
        // articleList: [] // 注释掉，避免缺省图闪现
      })
      if (this.data.pageType == "collection") {
        const apiParams = this.buildApiParams(
          this.data.collectionSelectForTemplate
        )
        console.log("拿到的参数", apiParams)
        await this.getArticleChildList(apiParams, false)
      } else {
        const apiParams = this.buildApiParams(
          this.data.jobDetailSelectForTemplate
        )
        console.log("拿到的参数", apiParams)
        await this.getJobList(apiParams, false)
      }
    },
    // 筛选菜单确认
    handleJobMenuFilterConfirm(e) {
      const { filterKey, tempSelected } = e.detail
      // 清空展开状态
      this.setData({
        [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      this.hidePopupMenuFilter()
      this.closeSelectBoxPopup()
      this.applyFilter()
    },

    updateJobSelectForTemplateFromCache() {
      const jobDetailSelectForTemplate = this.getJobDetailSelect()
      this.changeJobDetail(jobDetailSelectForTemplate)
    },
    getJobDetailSelect() {
      const jobDetailSelectForTemplate = this.data.jobDetailSelectForTemplate
      const cacheJobSelectForTemplate = getJobDetailSelectForTemplateCache()
      for (const key in jobDetailSelectForTemplate) {
        if (cacheJobSelectForTemplate[key]) {
          jobDetailSelectForTemplate[key] = cacheJobSelectForTemplate[key]
        }
      }
      return jobDetailSelectForTemplate
    },
    changeJobDetail(jobDetailSelectForTemplate) {
      this.setData({
        jobDetailSelectForTemplate,
      })
    },

    // 关注公告
    changeCollect() {
      this.setFollows()
    },
    async setFollows() {
      const is_follow = this.data?.noticeData?.is_follow
      let params = {
        item_type: "article",
        item_no: [this.PAGE_OPTIONS.id],
        type: is_follow == 0 ? "follow" : "unfollow",
      }
      const res = await UTIL.request(API.setFollows, params)
      if (res.error.code === 0) {
        this.setData({
          ["noticeData.is_follow"]: is_follow == 0 ? 1 : 0,
        })
        if (
          res.data.wx_public_notice.is_show === 1 &&
          this.data.noticeData.is_follow == 1
        ) {
          // 代表没有关注公众号，检查是否需要显示弹窗
          const shouldShow = APP.shouldShowFollowPopup()
          if (shouldShow) {
            this.setData({
              followShow: true,
            })
            console.log("显示公众号关注弹窗")
          } else {
            wx.showToast({
              title: "关注成功",
              icon: "none",
              duration: 2000,
            })
            console.log("3天内已关闭过公众号关注弹窗，不再显示")
          }
          return
        }
        wx.showToast({
          title: "已取消关注",
          icon: "none",
          duration: 2000,
        })
      }
      console.log(res, "123123123123")
    },
    // 打开公告弹窗
    async openSelectBoxPopup() {
      console.log("打开选择公告弹窗")

      // 如果弹窗已经打开，则关闭弹窗
      if (this.data.showSelectBoxPopup) {
        this.closeSelectBoxPopup()
        return
      }

      // 检查是否处于吸顶状态
      // const isSticky = await this.checkIfSticky()
      await this.calculateSelectBoxPopupPosition()
      this.disablePageScroll()
      this.setData({
        showSelectBoxPopup: true,
        showPopupFilterMenu: false,
        // pageScrollDisabled: true,
        // isOpenedInStickyMode: true, // 标记是在吸顶状态下打开的
      })

      // if (isSticky) {
      //   // 如果处于吸顶状态，不保存滚动位置，直接打开弹窗
      //   console.log("当前处于吸顶状态，直接打开弹窗，不保存滚动位置")
      //   this.disablePageScroll()
      //   this.setData({
      //     showSelectBoxPopup: true,
      //     showPopupFilterMenu: false,
      //     pageScrollDisabled: true,
      //     isOpenedInStickyMode: true, // 标记是在吸顶状态下打开的
      //   })

      //   // this.showPopupMenuFilter()

      //   // 延迟计算弹窗位置
      //   setTimeout(() => {
      //     this.calculateSelectBoxPopupPosition()
      //   }, 50)
      // } else {
      //   // 非吸顶状态，保存当前滚动位置
      //   wx.createSelectorQuery()
      //     .selectViewport()
      //     .scrollOffset((res) => {
      //       const currentScrollTop = res.scrollTop
      //       console.log("非吸顶状态，保存当前滚动位置:", currentScrollTop)

      //       this.setData({
      //         savedScrollTop: currentScrollTop,
      //         showSelectBoxPopup: true,
      //         showPopupFilterMenu: false,
      //         pageScrollDisabled: true,
      //         isOpenedInStickyMode: false, // 标记不是在吸顶状态下打开的
      //       })

      //       // 延迟计算弹窗位置
      //       setTimeout(() => {
      //         this.calculateSelectBoxPopupPosition()
      //       }, 50)

      //       console.log("弹窗已打开，当前滚动位置已保存:", currentScrollTop)
      //     })
      //     .exec()
      // }
    },

    // 检查是否处于吸顶状态
    checkIfSticky() {
      // 获取当前滚动位置
      const query = wx.createSelectorQuery()
      return new Promise((resolve) => {
        query
          .selectViewport()
          .scrollOffset((res) => {
            const currentScrollTop = res.scrollTop
            const { selectBoxTop, headerHeight } = this.data

            // 判断是否已经达到吸顶临界点
            const isSticky = currentScrollTop >= selectBoxTop - headerHeight
            console.log("吸顶状态检查:", {
              currentScrollTop,
              selectBoxTop,
              headerHeight,
              threshold: selectBoxTop - headerHeight,
              isSticky,
            })

            resolve(isSticky)
          })
          .exec()
      })
    },

    /**
     * 滚动到页面顶部
     */
    scrollToTop() {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      })
    },
    /**
     * 判断是否需要刷新职位列表
     * @param {boolean} hasStateChanged 状态是否发生变化
     * @returns {boolean} 是否需要刷新
     */
    shouldRefreshJobList(hasStateChanged) {
      const isPositionTab =
        this.data.indexToTabKey[this.data.activeIndex] === "position"
      return isPositionTab && hasStateChanged
    },

    // 关闭公告弹窗
    closeSelectBoxPopup() {
      console.log("关闭选择公告弹窗")
      this.enablePageScroll()
      const { savedScrollTop, isOpenedInStickyMode } = this.data

      this.setData({
        showSelectBoxPopup: false,
        pageScrollDisabled: false,
        savedScrollTop: 0,
        isOpenedInStickyMode: false,
      })

      // 只有在非吸顶状态下打开的弹窗才需要恢复滚动位置
      // if (!isOpenedInStickyMode && savedScrollTop > 0) {
      //   setTimeout(() => {
      //     wx.pageScrollTo({
      //       scrollTop: savedScrollTop,
      //       duration: 0,
      //     })
      //     console.log("已恢复到原滚动位置:", savedScrollTop)
      //   }, 50)
      // } else {
      //   console.log("吸顶状态下关闭弹窗，不恢复滚动位置")
      // }
    },
    // 选择公告项
    handleSelectBoxItemClick(e) {
      const { id, title } = e.currentTarget.dataset
      console.log("选择公告项:", { id, title })

      // 更新选中状态
      const updatedList = this.data.selectBoxList.map((item) => ({
        ...item,
        selected: item.id === id,
      }))

      // 更新当前选中的标题
      this.setData({
        selectBoxList: updatedList,
        currentSelectBoxTitle: title,
        showSelectBoxPopup: false,
        pageScrollDisabled: false, // 恢复页面滚动
      })
    },
    // 选项确认公用逻辑
    handlePopuMenuFilterConfirm(e) {
      const { filterKey, tempSelected } = e.detail
      console.log(
        filterKey,
        tempSelected,
        "数据你呢的啊放大手动阀沙发舒服阿萨发晒发"
      )
      // 清空展开状态
      this.setData({
        [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
        activeExamExpanded: "",
      })
      setJobDetailPopuSelectForTemplateCache(
        this.data.examPopuSelectForTemplate
      )
      this.hidePopupMenu()
      this.applyPopuFilter()
    },

    // 考试列表全部职位点击
    goPosition(e) {
      this.setData({
        show: false,
      })
      const { id } = e.currentTarget.dataset.item
      console.log(id, PAGE_OPTIONS.id)
      if (id == PAGE_OPTIONS.id) {
        this.setActiveTab("position")
      } else {
        ROUTER.navigateTo({
          path: "/pages/notice/detail/index",
          query: {
            id,
          },
        })
      }
    },
    // // 计算公告弹窗筛选弹窗弹出位置
    // calculateSelectBoxPopupPosition() {
    //   const query = this.createSelectorQuery()
    //   query
    //     .select(".select-box")
    //     .boundingClientRect((rect) => {
    //       if (rect) {
    //         // 计算弹窗应该出现的位置
    //         const selectBoxPopupTop = rect.bottom + 10 // 在 select-box 下方 10px 处
    //         this.setData({
    //           selectBoxPopupTop,
    //         })
    //       }
    //     })
    //     .exec()
    // },
    // 计算公告弹窗筛选弹窗弹出位置
    calculateSelectBoxPopupPosition() {
      console.log("开始计算选择框弹窗位置...")
      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery().in(this)

        // 同时获取选择框位置和页面滚动位置
        query.select(".select-box").boundingClientRect()
        query.selectViewport().scrollOffset()

        query.exec((res) => {
          const rect = res[0] // 选择框的位置信息
          const scrollInfo = res[1] // 页面滚动信息

          console.log("select-box 元素信息:", rect)
          console.log("页面滚动信息:", scrollInfo)

          if (rect) {
            const currentScrollTop = scrollInfo.scrollTop

            // 对于 CSS sticky 元素，直接使用 rect.bottom
            // 因为 sticky 元素的 boundingClientRect 已经反映了其当前的实际位置
            // 无论是在正常文档流中还是在吸顶状态下
            let selectBoxPopupTop = rect.bottom + 10

            console.log("CSS sticky 元素位置计算:", {
              rectTop: rect.top,
              rectBottom: rect.bottom,
              rectHeight: rect.height,
              currentScrollTop,
              calculatedTop: selectBoxPopupTop,
              note: "CSS sticky 元素的 boundingClientRect 已经包含了吸顶位置",
            })

            // 可选：添加边界检查，确保弹窗不会超出屏幕
            const systemInfo = wx.getSystemInfoSync()
            const windowHeight = systemInfo.windowHeight

            if (selectBoxPopupTop > windowHeight - 200) {
              // 如果计算出的位置太靠下，可以调整到选择框上方
              selectBoxPopupTop = rect.top - 10
              console.log("弹窗位置调整到上方:", selectBoxPopupTop)
            }

            console.log("最终弹窗位置:", selectBoxPopupTop)

            this.setData({
              selectBoxPopupTop,
            })
            resolve(selectBoxPopupTop)
          } else {
            reject(new Error(errorMsg))
            console.warn("未找到 .select-box 元素")
          }
        })
      })
    },
    // 计算考试列表弹窗筛选弹窗弹出位置
    calculateExamPopupMenuPosition() {
      setTimeout(() => {
        console.log("开始计算exam popup位置...")
        const query = this.createSelectorQuery()

        // 获取popu-box和popu-menu .top的位置信息
        query.select(".popu-box").boundingClientRect()
        query.select(".popu-box .popu-menu .top").boundingClientRect()

        query.exec((res) => {
          const popuBoxRect = res[0]
          const topRect = res[1]

          console.log("popu-box元素信息:", popuBoxRect)
          console.log("popu-menu内部top元素信息:", topRect)

          if (popuBoxRect && topRect) {
            // 获取屏幕高度，计算20vh的像素值
            const systemInfo = wx.getSystemInfoSync()
            const screenHeight = systemInfo.windowHeight
            const vh20 = (screenHeight * 20) / 100 // 20vh转换为像素

            // 计算top元素相对于popu-box的位置
            const topRelativeToPopuBox = topRect.top - popuBoxRect.top

            // 地区选择弹窗应该出现的位置：top元素在popu-box内的位置 + top元素高度 + 20vh + 10px间距
            const examRegionPopupTop =
              topRelativeToPopuBox + topRect.height + vh20 + 10

            console.log("位置计算信息:", {
              popuBoxTop: popuBoxRect.top,
              topElementTop: topRect.top,
              topRelativeToPopuBox: topRelativeToPopuBox,
              topHeight: topRect.height,
              screenHeight: screenHeight,
              vh20: vh20,
              calculatedTop: examRegionPopupTop,
            })

            this.setData({
              examRegionPopupTop,
            })
          } else {
            console.log("未找到.popu-box或.popu-box .popu-menu .top元素")
            console.log("popuBoxRect:", popuBoxRect)
            console.log("topRect:", topRect)

            // 降级方案：使用相对安全的默认值
            const systemInfo = wx.getSystemInfoSync()
            const screenHeight = systemInfo.windowHeight
            const vh20 = (screenHeight * 20) / 100
            const fallbackTop = 100 + vh20 + 10 // 估算的相对位置

            console.log("使用降级方案，设置默认位置:", fallbackTop)
            this.setData({
              examRegionPopupTop: fallbackTop,
            })
          }
        })
      }, 300) // 确保van-popup已完全显示并渲染
    },
    // 关闭popu弹窗
    onClose() {
      this.setData({
        show: false,
        showExamRegionPopup: false,
      })
      // 注意：不重置hasUserClosedApplyStatus和shouldKeepApplyStatusExpanded
      // 这样用户的偏好会被保持到下次打开弹窗
    },
    // 关闭考试列表popu
    closeExamRegionPopup() {
      this.setData({
        showExamRegionPopup: false,
        activeExamExpanded: "",
      })
    },
    // 考试弹窗
    // getNoticeSelectFromMenuData(serverMenuList) {
    //   const result = {}

    //   serverMenuList.forEach((menu) => {
    //     const filterKey = menu.filter_key

    //     if (filterKey === "filter_list") {
    //       result[filterKey] = {}

    //       menu.data.forEach((category) => {
    //         if (category.list) {
    //           category.list.forEach((filterGroup) => {
    //             const groupFilterKey = filterGroup.filter_key
    //             if (groupFilterKey) {
    //               // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
    //               result[filterKey][groupFilterKey] = []
    //             }
    //           })
    //         }
    //       })
    //     } else if (filterKey) {
    //       result[filterKey] = []
    //     }
    //   })
    //   if (this.data.noticeData.detail.child_article_list.length > 0) {
    //     result["article_list"] = [
    //       this.data.noticeData.detail.child_article_list[0],
    //     ]
    //   }

    //   return result
    // },
    getNoticeSelectFromMenuData(serverMenuList) {
      const result = {}
      serverMenuList.forEach((menu) => {
        const filterKey = menu.filter_key

        if (filterKey === "filter_list") {
          result[filterKey] = {}

          menu.data.forEach((category) => {
            if (category.list) {
              category.list.forEach((filterGroup) => {
                const groupFilterKey = filterGroup.filter_key
                if (groupFilterKey) {
                  // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                  result[filterKey][groupFilterKey] = []
                }
              })
            }
          })
        } else if (filterKey === "filter_list") {
        } else if (filterKey === "sort_list") {
          result[filterKey] = ["1"]
        }
        // if (
        //   this.data?.noticeData?.detail?.child_article_list?.length > 0 &&
        //   this.data.pageType == "detail"
        // ) {
        //   result["article_list"] = [
        //     this.data.noticeData.detail.child_article_list[0],
        //   ]
        // }
      })
      console.log(result, "12222222222222")
      return result
    },
    async openExam() {
      await this.updateJobPopuSelectForTemplateFromCache()
      this.applyPopuFilter()

      // 判断showApplyStatus的状态
      let showApplyStatus = false
      let activeExamExpanded = ""

      console.log("考试列表弹窗状态:", {
        isfirstOpenExam: this.data.isfirstOpenExam,
        hasUserClosedApplyStatus: this.data.hasUserClosedApplyStatus,
        shouldKeepApplyStatusExpanded: this.data.shouldKeepApplyStatusExpanded,
      })

      if (!this.data.isfirstOpenExam) {
        // 第一次打开弹窗
        if (!this.data.hasUserClosedApplyStatus) {
          // 用户从未手动关闭过，默认打开
          showApplyStatus = true
          activeExamExpanded = "apply_status"
          this.setData({
            shouldKeepApplyStatusExpanded: true,
          })
          console.log("第一次打开，设置showApplyStatus为true")
        } else {
          console.log("第一次打开，但用户之前手动关闭过，保持关闭状态")
        }
      } else {
        // 非第一次打开弹窗
        if (
          !this.data.hasUserClosedApplyStatus &&
          this.data.shouldKeepApplyStatusExpanded
        ) {
          // 用户没有手动关闭过，且之前是展开状态，保持展开
          showApplyStatus = true
          activeExamExpanded = "apply_status"
          console.log("非第一次打开，保持showApplyStatus为true")
        } else {
          console.log("非第一次打开，保持showApplyStatus为false")
        }
      }

      console.log("最终设置:", { showApplyStatus, activeExamExpanded })

      this.setData(
        {
          show: true,
          showApplyStatus: showApplyStatus,
          activeExamExpanded: activeExamExpanded,
          activeApplyExpanded: activeExamExpanded,
          isfirstOpenExam: true,
        },
        () => {
          // 在setData回调中执行，确保DOM已更新
          // 获取 popu-menu 的位置信息
          this.calculateExamPopupMenuPosition()
        }
      )
    },
    // 获取考试列表弹窗相关逻辑
    async getChildListForDetail(filterConditions = {}) {
      const requestParams = {
        ...filterConditions,
        article_id: this.PAGE_OPTIONS.id,
      }
      const res = await UTIL.request(API.getChildListForDetail, requestParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        this.setData({
          examData: res.data,
        })
        if (res.data.filter_menu.length) {
          this.initexamPopuMenu(res.data.filter_menu)
        }
      }
    },
    // 考试列表弹窗筛选相关逻辑
    async initexamPopuMenu(serverMenuList) {
      if (serverMenuList && serverMenuList.length) {
        // 使用提取后的纯函数处理菜单
        const menuList = processMenuList(serverMenuList)
        const examMenuData = {}
        serverMenuList.forEach((item) => {
          examMenuData[item.filter_key] = item
        })

        this.setData({
          examMenuData,
          examList: menuList,
          examPopuSelectForTemplate:
            this.data?.examPopuSelectForTemplate?.apply_status?.length > 0
              ? this.data.examPopuSelectForTemplate
              : this.getExamPopuSelectFromMenuData(serverMenuList),
        })
        await this.updateJobPopuSelectForTemplateFromCache()
      }
    },
    // 初始化考试列表弹窗筛选状态
    getExamPopuSelectFromMenuData(serverMenuList) {
      const result = {}
      serverMenuList.forEach((menu) => {
        const filterKey = menu.filter_key
        console.log(filterKey, "---------------------------------------------")
        if (filterKey === "filter_list") {
          result[filterKey] = {}

          menu.data.forEach((category) => {
            if (category.list) {
              category.list.forEach((filterGroup) => {
                const groupFilterKey = filterGroup.filter_key
                if (groupFilterKey) {
                  // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                  result[filterKey][groupFilterKey] = []
                }
              })
            }
          })
        } else if (filterKey) {
          result[filterKey] = []
        }
        if (filterKey == "apply_status") {
          result[filterKey] = []
        }
      })
      return result
    },
    // 考试列表弹窗筛选项点击
    async handleExamMenuClick(e) {
      const { type, currentItem } = e.detail || e.currentTarget.dataset
      const { showExamRegionPopup, examPopuSelectForTemplate } = this.data
      const filterKey = currentItem.filter_key
      console.log(currentItem, "----------------------------")
      const currentMenuSelected = examPopuSelectForTemplate[filterKey]

      if (
        type === "apply_region" ||
        type == "apply_status" ||
        type == "sort_time"
      ) {
        this.hidePopupMenu()
      } else if (
        showExamRegionPopup === true &&
        this.data.activeExamExpanded === filterKey
      ) {
        this.hidePopupMenu()
      } else {
        console.log("进的这里？")
        this.setData({
          showExamRegionPopup: true,
        })
      }

      // 处理activeExamExpanded的设置
      let newActiveExamExpanded =
        this.data.activeExamExpanded === filterKey ? "" : filterKey

      // 特殊处理apply_status
      if (filterKey === "apply_status") {
        console.log("点击apply_status，当前状态:", {
          shouldKeepApplyStatusExpanded: this.data
            .shouldKeepApplyStatusExpanded,
          activeExamExpanded: this.data.activeExamExpanded,
          showApplyStatus: this.data.showApplyStatus,
        })

        if (
          this.data.shouldKeepApplyStatusExpanded &&
          this.data.activeExamExpanded === "apply_status"
        ) {
          // 用户点击了apply_status，但是应该保持展开状态，这里记录用户手动关闭
          console.log("用户手动关闭apply_status，记录状态")
          this.setData({
            hasUserClosedApplyStatus: true,
            shouldKeepApplyStatusExpanded: false,
            showApplyStatus: false,
          })
          newActiveExamExpanded = ""
        } else {
          // 正常的apply_status切换逻辑
          console.log("正常切换apply_status状态")
          this.setData({
            showApplyStatus: !this.data.showApplyStatus,
          })
        }

        this.setData({
          activeExamExpanded: newActiveExamExpanded,
          activeApplyExpanded: this.data.showApplyStatus ? "apply_status" : "",
        })

        return
      }

      // 特殊逻辑：当shouldKeepApplyStatusExpanded为true且当前展开的是apply_status时
      // 点击其他菜单项不应该关闭apply_status的展开状态
      if (
        this.data.shouldKeepApplyStatusExpanded &&
        this.data.activeExamExpanded === "apply_status" &&
        filterKey !== "apply_status"
      ) {
        console.log(
          "特殊处理：保持apply_status展开状态，点击其他菜单:",
          filterKey
        )
        // 保持apply_status的展开状态，但允许其他菜单项的展开/收起
        if (this.data.activeExamExpanded === filterKey) {
          // 如果点击的是已展开的其他菜单项，则收起它，但保持apply_status展开
          newActiveExamExpanded = "apply_status"
        } else {
          // 如果点击的是其他菜单项，展开它，但这不影响apply_status（通过特殊处理实现）
          newActiveExamExpanded = filterKey
        }
      }

      this.setData({
        activeExamExpanded: newActiveExamExpanded,
      })

      // 处理sort_time类型：单选，不打开弹窗，点击选中，再次点击取消
      if (type === "sort_time") {
        const currentValue = currentMenuSelected
        console.log("进来了拿 啊大飒飒的撒旦as", currentValue)
        let newValue = this.sortTime(currentValue)
        console.log("最终设置的值:", newValue, filterKey)

        this.setData({
          [`examPopuSelectForTemplate.${filterKey}`]: newValue,
        })
        console.log(
          this.data.examPopuSelectForTemplate,
          "-----------------------------"
        )
        setJobDetailPopuSelectForTemplateCache(
          this.data.examPopuSelectForTemplate
        )
        const apiParams = this.buildApiParams(
          this.data.examPopuSelectForTemplate
        )
        console.log("拿到的参数", apiParams)
        await this.getChildListForDetail(apiParams)
      }
    },
    sortTime(currentValue) {
      console.log(currentValue, "传进来的")
      if (!currentValue || currentValue.length === 0) {
        // 没有值，设置为2
        return [2]
      } else if (Number(currentValue[0]) === 1) {
        // 值为1，设置为2（使用Number()确保数字比较）
        return [2]
      } else if (Number(currentValue[0]) === 2) {
        // 值为2，设置为1（使用Number()确保数字比较）
        return [1]
      } else {
        // 其他情况，默认清空
        return []
      }
    },
    // 获取考试列表弹窗筛选缓存
    updateJobPopuSelectForTemplateFromCache() {
      const examPopuSelectForTemplate = getJobDetailPopuSelectForTemplateCache()
      console.log(examPopuSelectForTemplate)
      this.setData({
        examPopuSelectForTemplate,
      })
    },

    // 考试列表弹窗应用筛选请求
    async applyPopuFilter() {
      const apiParams = this.buildApiParams(this.data.examPopuSelectForTemplate)
      console.log("拿到的参数", apiParams)
      await this.getChildListForDetail(apiParams)
    },
    // 考试列表弹窗地区筛选确认弹窗
    handlePopuRegionSelection(e) {
      const { filterKey, tempSelected } = e.detail
      console.log(filterKey, tempSelected)
      this.setData({
        [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
        activeExamExpanded: "",
      })
      setJobDetailPopuSelectForTemplateCache(
        this.data.examPopuSelectForTemplate
      )
      this.applyPopuFilter()
      this.hidePopupMenu()
    },
    // 关闭考试列表里面筛选项的弹窗
    hidePopupMenu() {
      this.setData({
        showExamRegionPopup: false,
      })
    },

    // 详情tab初始化
    generateDynamicTabs() {
      const { noticeData } = this.data
      if (!noticeData || !noticeData.detail) {
        console.log("公告数据未加载，使用默认tab配置")
        return
      }

      const detail = noticeData.detail
      const dynamicTabs = []
      const tabKeyToIndex = {}
      const indexToTabKey = {}

      this.data?.allTabsConfig.forEach((tabConfig) => {
        const { key, title, alwaysShow, conditionField } = tabConfig

        // 总是显示的tab或满足条件的tab
        if (alwaysShow || (conditionField && detail[conditionField] > 0)) {
          const index = dynamicTabs.length
          dynamicTabs.push(title)
          tabKeyToIndex[key] = index
          indexToTabKey[index] = key
        }
      })

      console.log("动态生成的tabs:", {
        tabs: dynamicTabs,
        keyToIndex: tabKeyToIndex,
        indexToKey: indexToTabKey,
        conditions: {
          job_num: detail.job_num,
          notice_num: detail.notice_num,
        },
      })

      this.setData({
        tabList: dynamicTabs,
        tabKeyToIndex,
        indexToTabKey,
      })
    },
    // 切换tab
    changeTab(e) {
      console.log(e.currentTarget.dataset.index, e, "-----------------")
      const { index, item } = e.currentTarget.dataset
      // 如果有弹窗打开，先关闭弹窗
      if (this.data.showPopupFilterMenu) {
        this.hidePopupMenuFilter()
        this.setData({
          activeExpanded: "",
        })
      }
      this.setData({
        activeIndex: index,
      })

      this.scrollService.restoreScrollPosition(index, { duration: 0 })

      if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
        this.applyFilter()
      } else if (this.data.indexToTabKey[this.data.activeIndex] == "official") {
        // 记录点击官方动态的时间
        this.recordOfficialTabClickTime()
        this.getArticleNoticeList()
      }
    },
    // 记录官方动态tab点击时间
    recordOfficialTabClickTime() {
      console.log(123123123123, "进来没得")
      const noticeId = this.data.noticeData?.detail?.id
      if (!noticeId) return

      // 获取当前时间戳
      const currentTime = Date.now()

      // 从缓存获取已有的记录对象
      let officialClickRecord = getOfficialNewsCache() || {}

      // 更新或添加当前公告的点击时间（相同键直接覆盖）
      officialClickRecord[noticeId] = currentTime

      // 保存回缓存
      setOfficialNewsCache(officialClickRecord)

      console.log("记录官方动态点击时间:", {
        noticeId,
        currentTime,
        allRecords: officialClickRecord,
      })
    },
    // 获取富文本高度
    checkContentHeight() {
      console.log("进来没得")
      // 延迟执行，确保rich-text内容已经渲染
      let retryCount = 0
      const maxRetries = 3
      this.checkHeight(retryCount, maxRetries)
    },
    checkHeight(retryCount, maxRetries) {
      const query = wx.createSelectorQuery().in(this)
      query.select("#richTextContent").boundingClientRect()
      query.exec((res) => {
        console.log("进来没得1")
        if (res && res[0] && res[0].height > 0) {
          const contentHeight = res[0].height
          const needToggle = contentHeight > this.data.maxHeight

          console.log("Rich-text content height:", contentHeight)
          console.log("Max height:", this.data.maxHeight)
          console.log("Need toggle:", needToggle)

          this.setData({
            actualHeight: contentHeight,
            showToggle: needToggle,
            // 如果内容高度小于等于maxHeight，默认展开
            isExpanded: !needToggle,
          })
        } else if (retryCount < maxRetries) {
          // // 如果高度获取失败，进行重试
          // retryCount++
          // console.log(
          //   `Rich-text高度检测失败，正在重试 ${retryCount}/${maxRetries}`
          // )
          // setTimeout(checkHeight, 300)
        } else {
          // 重试失败，设置默认状态
          console.warn("Rich-text高度检测失败，使用默认状态")
          this.setData({
            showToggle: false,
            isExpanded: true,
          })
        }
      })
    },
    // 点击附件
    tapAttachment(e) {
      const val = e.currentTarget.dataset.item
      const url = val.url
      const name = val.name
      wx.showLoading({
        title: "",
        mask: true,
      })
      APP.openFile(url, { fileName: name })
        .then((res) => {
          console.log("文件打开成功", url)
          wx.hideLoading()
        })
        .catch((res) => {
          console.log(res)
          wx.hideLoading()
          // wx.navigateTo({
          //   url: `/pages/file-preview/index?filePath=${res.filePath}&fileName=${res.fileName}`,
          // })
        })
    },
    tapCustomerService(e) {
      const { cmd_json } = e.currentTarget.dataset.item
      console.log(e, cmd_json)
      APP.toCmdUnitKey(cmd_json)
    },
    // 复制链接
    copyUrl(e) {
      const link = e.currentTarget.dataset.item

      // 调用微信API复制链接到剪贴板
      wx.setClipboardData({
        data: link,
        success: () => {
          wx.showToast({
            title: "复制成功",
            duration: 2000,
            icon: "none",
          })
        },
      })
    },
    // 去简历
    goResume() {
      ROUTER.navigateTo({
        path: "/pages/my/resume/index",
      })
    },
    // 页面返回方法
    backPage() {
      if (getCurrentPages().length <= 1) {
        wx.reLaunch({
          url: "/pages/home/<USER>/index",
        })
        console.log("回首页")
        return false
      }
      console.log("触发返回")
      wx.navigateBack({
        delta: 1,
      })
    },
    // 去职位筛选
    goSelect() {
      ROUTER.navigateTo({
        path: "/pages/select/select-job/index",
        query: {
          type: "detail",
          article_id: this.PAGE_OPTIONS.id,
        },
      })
    },
    // 去搜索页
    goSearch() {
      ROUTER.navigateTo({
        path: "/pages/search/index",
        query: {
          type: 1,
          placeholder: "搜索职位职位代码为关键词",
        },
      })
    },
    // 展开收起
    toggleText() {
      // 只支持展开功能，不支持收起
      if (!this.data.isExpanded) {
        this.setData({
          isExpanded: true,
          showToggle: false, // 展开后隐藏切换按钮
        })
      }
    },

    toNewsDetail(e) {
      const { id } = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/pages/notice/dynamics/index",
        query: {
          id,
        },
      })
    },
    // 切换tab
    changeActiveIndex(e) {
      const targetTabKey = e.currentTarget.dataset.item

      // 兼容旧的数字格式和新的字符串格式
      if (typeof targetTabKey === "string" && !isNaN(targetTabKey)) {
        // 如果是数字字符串，转换为数字
        const index = parseInt(targetTabKey)
        this.setData({ activeIndex: index })
      } else if (typeof targetTabKey === "string") {
        // 如果是tab类型字符串，使用新的映射方法
        this.setActiveTab(targetTabKey)
      } else if (typeof targetTabKey === "number") {
        // 如果是数字，直接设置
        this.setData({ activeIndex: targetTabKey })
      } else {
        console.warn("未知的tab类型:", targetTabKey)
      }
    },
    setActiveTab(tabKey) {
      const { tabKeyToIndex } = this.data
      const targetIndex = tabKeyToIndex[tabKey]
      if (targetIndex !== undefined) {
        this.setData({ activeIndex: targetIndex })
        if (tabKey == "position") {
          this.applyFilter()
        }
        return true
      }

      return false
    },
    // 获取吸顶高度
    setMenuStickyTop() {
      const query = wx.createSelectorQuery()
      query
        .select("#commonHeader")
        .boundingClientRect((headerRect) => {
          let headerHeight = 100 // 默认高度
          console.log("获取到的header信息:", headerRect)

          if (headerRect) {
            headerHeight = headerRect.height
            console.log("成功获取导航栏高度:", headerHeight)
          } else {
            // 降级方案：通过系统信息计算
            const systemInfo = wx.getSystemInfoSync()
            const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
            headerHeight = menuButtonInfo.bottom
          }

          // 获取content-box-top的高度
          const contentTopQuery = wx.createSelectorQuery()
          contentTopQuery
            .select(".content-box-top")
            .boundingClientRect((contentTopRect) => {
              let contentTopHeight = 120 // 默认高度

              if (contentTopRect) {
                contentTopHeight = contentTopRect.height
                console.log("成功获取content-box-top高度:", contentTopHeight)
              } else {
                console.log("无法获取content-box-top高度，使用默认值")
              }

              // 计算select-box-content的top值
              const selectBoxTop = headerHeight + contentTopHeight
              console.log("计算得出的select-box-content top值:", selectBoxTop)
              this.scrollService?.setStickyTop(contentTopRect.top)
              // 设置数据
              this.setData({
                headerHeight: headerHeight - 15,
                contentBoxTopHeight: contentTopHeight,
                selectBoxTop: selectBoxTop - 15,
              })

              console.log("导航栏高度:", headerHeight)
              console.log("content-box-top高度:", contentTopHeight)
              console.log("select-box-content top值:", selectBoxTop)
            })
            .exec()
        })
        .exec()
    },
    handlePopupClose() {
      console.log("弹窗关闭事件")
      // 清空展开状态
      this.setData({
        activeExpanded: "",
      })
      // 调用hidePopupMenuFilter真正关闭弹窗（包含恢复备份的逻辑）
      this.hidePopupMenuFilter()
    },
    onPopuClose() {
      this.setData({
        popuShow: false,
      })
    },
    onConfirm(e) {
      this.onPopuClose()
      const data = e.detail.value
      const path = "/pages/notice/collection/index"
      ROUTER.navigateTo({
        path,
        query: {
          id: data.id,
        },
      })
    },
    openPopu() {
      this.setData({
        popuShow: true,
      })
    },
    majorConfirm(e) {
      console.log(e.detail, "-------------------")
      const selected = {
        educationId: e.detail.educationId,
        isSync: e.detail.isSync,
        selectedMajorIds: e.detail.selectedMajorIds,
      }
      this.setData({
        [`jobDetailSelectForTemplate.tmp_major`]: selected,
        majorShow: false,
        activeExpanded: "",
      })
      if (selected.isSync) {
        const param = {
          degree: selected.educationId,
          major_id:
            selected.selectedMajorIds[selected.selectedMajorIds.length - 1],
        }
        this.syncEdcucation(param)
      }
      console.log(this.data.jobDetailSelectForTemplate, "1231233333333333")
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      this.hidePopupMenuFilter()
      this.closeSelectBoxPopup()
      this.applyFilter()
      // const { filterKey, tempSelected } = e.detail
      // // 清空展开状态
      // this.setData({
      //   [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
      //   activeExpanded: "",
      // })
      // setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      // this.hidePopupMenuFilter()
      // this.closeSelectBoxPopup()
      // this.applyFilter()
    },
    async syncEdcucation(e) {
      const res = await UTIL.request(API.syncEducationRecord, e)
      if (res && res.error && res.error.code === 0 && res.data) {
      } else {
        wx.showToast({
          title: "同步失败",
          icon: "none",
        })
      }
    },
    majorClose() {
      this.setData({
        majorShow: false,
      })
    },

    /**
     * 同步报名状态显示状态
     * 确保 activeApplyExpanded 和 showApplyStatus 状态一致
     */
    syncApplyStatusState() {
      const { activeApplyExpanded } = this.data
      console.log("同步报名状态显示状态:", { activeApplyExpanded })

      // 如果 activeApplyExpanded 有值，showApplyStatus 应该为 true
      // 如果 activeApplyExpanded 没值，showApplyStatus 应该为 false
      const shouldShowApplyStatus = !!activeApplyExpanded

      this.setData({
        showApplyStatus: shouldShowApplyStatus,
      })

      console.log("同步后的状态:", {
        activeApplyExpanded: this.data.activeApplyExpanded,
        showApplyStatus: this.data.showApplyStatus,
      })
    },

    /**
     * 处理公众号关注弹窗 - 去开启
     */
    handleFollowPopupConfirm() {
      // 关闭弹窗
      this.setData({
        followShow: false,
      })
      ROUTER.navigateTo({
        path: "/pages/subscribe/list/index", // 示例路径，需要根据实际情况修改
        query: {},
      })
    },

    /**
     * 处理公众号关注弹窗 - 以后再说
     */
    handleFollowPopupCancel() {
      // 记录关闭时间
      APP.setFollowPopupCloseTime()

      // 关闭弹窗
      this.setData({
        followShow: false,
      })
    },
  },

  /**
   * 初始化 Mixin
   * 在页面的 onLoad 或组件的 attached 中调用
   */
  initNoticeMixin() {
    // 将地区数据合并到页面数据中
    const currentData = this.data || {}
    this.setData({
      ...currentData,
      ...this.noticeData,
    })

    // 将地区方法合并到页面方法中
    Object.assign(this, noticeMixin.noticeMethods)

    console.log("noticeMixin 初始化完成")
  },

  /**
   * 使用示例：
   *
   * 1. 在页面中导入并混入 mixin：
   *    const RegionSelectMixin = require("@/services/regionSelectMixin")
   *    const pageConfig = Object.assign({}, RegionSelectMixin, {
   *      // 页面配置
   *    })
   *
   * 2. 在 onLoad 中初始化：
   *    this.initRegionSelectMixin()
   *
   * 3. 初始化地区数据：
   *    this.initRegionData('announcement', 'regionPopupData') // 公告Tab
   *    this.initRegionData('news', 'newsRegionPopupData')     // 考试动态Tab
   *
   * 4. 创建事件处理器：
   *    const handlers = this.createRegionHandlers('regionPopupData', 'announcement', (regions) => {
   *      // 确认回调
   *      this.updateRegionSelectState(regions, 'noticeSelectForTemplate', 'apply_region')
   *    })
   *
   * 5. 在页面方法中使用：
   *    handleRegionProvinceClick(e) {
   *      this.handleProvinceClick(e, 'regionPopupData')
   *    }
   */
}

module.exports = noticeMixin
