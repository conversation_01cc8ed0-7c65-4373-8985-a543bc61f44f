{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "选岗助手", "setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "bigPackageSizeSupport": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.9.0", "condition": {"miniprogram": {"list": [{"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "id=101", "scene": null, "launchMode": "default"}, {"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "id=135", "launchMode": "default", "scene": null}, {"name": "pages/notice/detail/index", "pathName": "pages/notice/detail/index", "query": "id=107", "launchMode": "default", "scene": null}, {"name": "pages/notice/dynamics/index", "pathName": "pages/notice/dynamics/index", "query": "id=568", "launchMode": "default", "scene": null}, {"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "id=3", "launchMode": "default", "scene": null}, {"name": "pages/notice/detail/index", "pathName": "pages/notice/detail/index", "query": "id=6", "launchMode": "default", "scene": null}, {"name": "pages/select/select-job/index", "pathName": "pages/select/select-job/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "id=7", "launchMode": "default", "scene": null}, {"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "id=7", "launchMode": "default", "scene": null}, {"name": "pages/select/select-region/index", "pathName": "pages/select/select-region/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/search/index", "pathName": "pages/search/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/select/select-job/index", "pathName": "pages/select/select-job/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/home/<USER>/index", "pathName": "pages/home/<USER>/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/notice/collection/index", "pathName": "pages/notice/collection/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/table-demo/table-demo", "pathName": "pages/table-demo/table-demo", "query": ""}, {"name": "pages/notice/detail/index", "pathName": "pages/notice/detail/index", "query": "", "launchMode": "default", "scene": null}]}}}