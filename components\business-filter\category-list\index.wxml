<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>
<view class="popu-content-c">
  <scroll-view class="exam-category-list" scroll-y show-scrollbar="{{false}}" enhanced style="max-height: 300px;">
    <view class="exam-category-item {{filterUtils.isFilterItemOptionSelected(item.value, tempSelected) ? 'active' : ''}}" wx:for="{{list[0].list}}" wx:key="key" bindtap="handleExamSelect" data-value="{{item.value}}">
      {{item.name}}
    </view>
  </scroll-view>
</view>
<popu-bottom bind:reset="handleReset" isEducation="{{filterKey=='education'?true:false}}" bind:confirm="handleConfirm" />