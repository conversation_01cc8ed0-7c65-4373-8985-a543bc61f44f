/**
 * 富文本内容展示组件，支持图片点击预览
 */
Component({
  properties: {
    nodes: {
      type: [String, Array, Object],
      value: "",
    },
  },
  methods: {
    /**
     * 图片点击预览
     * @param {Object} e - 事件对象
     */
    handleImageTap(e) {
      const current = e.target.dataset.src
      // 获取所有图片链接
      let imgList = []
      // 递归提取所有图片链接
      function extractImgs(nodes) {
        if (!nodes) return
        if (typeof nodes === "string") return
        if (Array.isArray(nodes)) {
          nodes.forEach(extractImgs)
        } else if (nodes.type === "image" && nodes.attrs && nodes.attrs.src) {
          imgList.push(nodes.attrs.src)
        } else if (nodes.children) {
          extractImgs(nodes.children)
        }
      }
      extractImgs(this.data.nodes)
      console.log(11111)
      wx.previewImage({
        current,
        urls: imgList.length ? imgList : [current],
      })
    },
  },
})
