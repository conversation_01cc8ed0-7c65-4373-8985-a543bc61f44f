page {
  background: #f2f4f7;
}

.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: 600;
}
.footprint {
  height: calc(100vh - 176rpx);
  display: flex;
  flex-direction: column;

  .scroll-list {
    flex: 1;
    height: 0;
  }

  .pd-style {
    padding: 0 80rpx;
  }
  .tab-list {
    background: #fff;
    padding-bottom: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    &-item {
      flex: 1;
      font-size: 28rpx;
      padding: 32rpx 0;
      color: #666666;
      font-weight: 400;
      text-align: center;
      &.active {
        font-weight: 600;
        font-size: 32rpx;
        color: #22242e;
        .text {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: -20rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 6rpx;
            background: #e60003;
            border-radius: 4rpx;
          }
        }
      }
    }
  }
  .tips-box {
    width: 100%;
    background: #ffffff;
    .contain-area {
      width: 100%;
      padding: 16rpx 24rpx 16rpx 32rpx;
      box-sizing: border-box;
      background: rgba(255, 106, 77, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .text {
      color: #ff6a4d;
      font-size: 24rpx;
    }
    .close-img {
      width: 24rpx;
      height: 24rpx;
    }
  }
  .list-area {
    padding: 0 32rpx 60rpx 32rpx;
    box-sizing: border-box;
    .list-item {
      margin-top: 32rpx;
      .time-text {
        color: #919499;
        font-size: 24rpx;
        margin-bottom: 32rpx;
      }
    }
  }
}
