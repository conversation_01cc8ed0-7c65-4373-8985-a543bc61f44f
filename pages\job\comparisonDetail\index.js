/**
 * 多表格双向滚动页面
 * 功能：支持横向和竖向双向滚动，固定表头和首列，支持吸顶
 * 滚动同步：使用view组件配合触摸事件（bindtouchstart、bindtouchmove、bindtouchend）实现实时横向滚动同步
 * 计算方式：采用增量计算方式，每次触摸移动时计算与上一次位置的差值，避免累积误差
 * 优势：相比scroll-view组件，触摸事件响应更快，无延迟，滚动更流畅，更精确
 * 作者：WeChat Mini Program Developer
 * 创建时间：2025-06-17
 * 更新时间：2025-06-18
 */
const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
Page({
  /**
   * 触摸位置信息
   */
  lastTouchX: 0, // 上一次触摸位置，用于增量计算
  maxScrollLeft: 0,

  /**
   * 页面的初始数据
   */
  data: {
    // 职位列表（顶部显示的职位信息）
    jobList: [],
    // 表格数据列表
    tableList: [],
    // 原始数据备份
    originalJobList: [],
    originalTableList: [],
    // 钉住的职位ID数组（按钉住顺序）
    pinnedJobIds: [],
    // 首列宽度（rpx）
    firstColumnWidth: 160,
    // 其他列宽度（rpx）
    columnWidth: 240,
    // 行高（rpx）
    rowHeight: 80,
    // 可滚动区域宽度
    scrollableWidth: 0,
    // 首列高度二维数组（用于动态设置高度）[tableIndex][rowIndex]
    firstColumnHeights: [],
    // 原始高度数据备份（对应原始数据结构）
    originalFirstColumnHeights: [],
    isHideSameTab: false,
    isLogin: false,
    jobIds: [],
    routerIds: [],
    oldJobIds: [], // 新增
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从路由参数中获取职位ID列表
    const ids = options.ids ? options.ids.split(",") : []
    this.setData({
      routerIds: ids,
    })
  },
  async onShow() {
    await APP.checkLoadRequest()
    await this.getIsLogin()
    this.getInfo()
  },

  // 判断是否登录
  getIsLogin() {
    const isLogin = !!APP.globalData?.userInfo?.token
    this.setData({
      isLogin: isLogin,
    })
  },

  async getInfo(ids) {
    // 优先读取正在对比的职位ids缓存
    const comparingJobIds = wx.getStorageSync("comparingJobIds")

    let jobIds
    if (comparingJobIds && comparingJobIds.length > 0) {
      jobIds = comparingJobIds
    } else if (this.data.routerIds && this.data.routerIds.length > 0) {
      jobIds = this.data.routerIds
    } else {
      jobIds = wx.getStorageSync("pkListIds") || []
    }

    this.setData({
      jobIds,
    })

    try {
      const param = {
        ids: this.data.jobIds,
      }
      const res = await UTIL.request(API.getCompareDetail, param)
      if (res && res?.error?.code == 0) {
        this.setData({
          isComplete: true,
        })
        this.processApiData(res.data)
      } else {
        wx.showToast({
          title: "获取数据失败",
          icon: "none",
        })
      }
    } catch (error) {
      console.error("获取对比数据失败:", error)
      wx.showToast({
        title: "获取数据失败",
        icon: "none",
      })
    }
  },

  /**
   * 处理接口数据，转换为表格需要的格式
   */
  processApiData(apiData) {
    const { default_data, data } = apiData

    // 1. 构建 jobList（职位列表）
    const jobList = data.map((item, index) => ({
      id: item.job_id,
      name: item.job_name,
      isFocus: item.is_follows == 1,
      isTop: index == 0, // 第一个职位默认置顶
    }))

    // 2. 构建 tableList（表格数据）
    const tableList = default_data.map((section, sectionIndex) => {
      const { title, list } = section
      const fields = list.map((item) => ({
        name: item.title, // 用于显示
        key: item.key, // 用于匹配 tips_list
      }))

      // 构建每个职位的行数据
      const rows = data.map((jobData) => {
        const row = {}
        list.forEach((field) => {
          row[field.title] = jobData[field.key] || "-"
        })
        return row
      })

      // 高亮数据：布尔二维数组 isRed[fieldIndex][jobIndex]
      const isRed = list.map((field) => {
        return data.map((jobData) => {
          return (
            jobData.tips_list &&
            Array.isArray(jobData.tips_list) &&
            jobData.tips_list.includes(field.key)
          )
        })
      })

      return {
        id: sectionIndex + 1,
        title,
        fields,
        isRed,
        rows,
      }
    })

    // 标记相同项和不同项
    const processedTableList = this.markSameAndDifferentRows(tableList)

    // 保存原始数据
    this.setData({
      originalJobList: JSON.parse(JSON.stringify(jobList)),
      originalTableList: JSON.parse(JSON.stringify(processedTableList)),
      isComplete: true,
    })

    // 初始化展示数据
    this.refreshDisplayData()

    // 计算可滚动区域宽度
    this.calculateScrollableWidth()
  },

  onChange({ detail }) {
    this.setData({ isHideSameTab: detail })
    // 重新计算展示数据
    this.refreshDisplayData()
  },

  /**
   * 关注按钮点击事件
   */
  onFocusClick(e) {
    const { index } = e.currentTarget.dataset
    const { jobList, originalJobList } = this.data
    let isFocus = jobList[index].isFocus
    const jobId = jobList[index].id

    // 更新界面状态
    jobList[index].isFocus = !isFocus
    originalJobList[index].isFocus = !isFocus

    this.setData({
      jobList: jobList,
      originalJobList: originalJobList,
    })
    // 这里可以调用接口更新关注状态
    this.updateJobFollowStatus(jobId, isFocus)
  },

  /**
   * 更新职位关注状态
   */
  async updateJobFollowStatus(jobId, isFollow) {
    try {
      // 这里可以调用关注/取消关注的接口
      const param = {
        item_type: "job",
        item_no: [jobId],
        type: isFollow ? "unfollow" : "follow",
      }
      const res = await UTIL.request(API.setFollows, param)
      if (res) {
        wx.showToast({
          title: isFollow ? "已取消关注" : "关注成功",
          icon: "none",
        })
      }

      // console.log(`职位 ${jobId} ${isFollow ? "关注" : "取消关注"} 成功`)
    } catch (error) {
      console.error("更新关注状态失败:", error)
    }
  },

  /**
   * 钉住按钮点击事件
   */
  onPinClick(e) {
    const { index } = e.currentTarget.dataset
    const { jobList, pinnedJobIds } = this.data
    const jobId = jobList[index].id

    // 切换钉住状态
    jobList[index].isTop = !jobList[index].isTop

    if (jobList[index].isTop) {
      // 钉住：添加到钉住列表
      if (!pinnedJobIds.includes(jobId)) {
        pinnedJobIds.push(jobId)
      }
    } else {
      // 取消钉住：从钉住列表移除
      const pinIndex = pinnedJobIds.indexOf(jobId)
      if (pinIndex > -1) {
        pinnedJobIds.splice(pinIndex, 1)
      }
    }

    this.setData({
      jobList: jobList,
      pinnedJobIds: pinnedJobIds,
    })

    // 重新计算展示数据
    this.refreshDisplayData()
  },

  /**
   * 删除职位
   */
  onDeleteJob(e) {
    const { index } = e.currentTarget.dataset
    const { originalJobList, originalTableList, pinnedJobIds } = this.data

    if (originalJobList.length <= 2) {
      wx.showToast({
        title: "至少保留两个职位",
        icon: "none",
      })
      return
    }

    // 保存删除前的 jobId 列表
    const oldJobIds = originalJobList.map((j) => j.id)

    const jobId = originalJobList[index].id

    // 同步删除 comparingJobIds 缓存中的对应 id
    let comparingJobIds = wx.getStorageSync("comparingJobIds") || []
    comparingJobIds = comparingJobIds.filter((id) => id != jobId)
    wx.setStorageSync("comparingJobIds", comparingJobIds)

    // 从原始数据中删除职位
    originalJobList.splice(index, 1)

    // 从所有表格数据中删除对应的行
    originalTableList.forEach((table) => {
      table.rows.splice(index, 1)
    })

    // 从钉住列表中移除
    const pinIndex = pinnedJobIds.indexOf(jobId)
    if (pinIndex > -1) {
      pinnedJobIds.splice(pinIndex, 1)
    }

    // 重新标记相同项和不同项
    const processedTableList = this.markSameAndDifferentRows(originalTableList)

    // 更新原始数据
    this.setData({
      originalJobList: originalJobList,
      originalTableList: processedTableList,
      pinnedJobIds: pinnedJobIds,
      oldJobIds: oldJobIds, // 新增
    })

    // 重新计算展示数据和宽度
    this.refreshDisplayData()
    this.calculateScrollableWidth()

    // 重新计算高度
    setTimeout(() => {
      this.syncCellHeights()
    }, 100)
  },

  /**
   * 添加职位
   */
  onAddJob() {
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
      query: {
        fromDetail: true,
      },
    })
  },

  /**
   * 标记相同项和不同项
   * @param {Array} tableList 表格数据
   * @returns {Array} 处理后的表格数据
   */
  markSameAndDifferentRows(tableList) {
    return tableList.map((table) => {
      const processedTable = { ...table }

      // 为每个字段标记是否为相同项，确保格式统一
      processedTable.fields = table.fields.map((field) => {
        const fieldName = typeof field === "string" ? field : field.name
        const values = table.rows.map((row) => row[fieldName])
        const uniqueValues = [...new Set(values)]

        return {
          name: fieldName,
          isSameRow: uniqueValues.length === 1, // 如果只有一个唯一值，说明是相同项
        }
      })

      return processedTable
    })
  },

  /**
   * 公共方法：刷新展示数据
   * 处理过滤和排序逻辑
   */
  refreshDisplayData() {
    const {
      originalJobList,
      originalTableList,
      isHideSameTab,
      pinnedJobIds,
      originalFirstColumnHeights,
      oldJobIds, // 新增
    } = this.data

    // 深拷贝原始数据
    let jobList = JSON.parse(JSON.stringify(originalJobList))
    let tableList = JSON.parse(JSON.stringify(originalTableList))

    // 1. 根据钉住规则重新排序职位
    jobList = this.sortJobsByPinned(jobList, pinnedJobIds)

    // 2. 重新排序表格数据以匹配职位顺序
    tableList = this.reorderTableData(tableList, jobList)

    // --- 高亮同步修复 start ---
    // 用 oldJobIds 做高亮同步，防止 undefined
    const jobIdListForRed =
      Array.isArray(oldJobIds) && oldJobIds.length > 0
        ? oldJobIds
        : (originalJobList || []).map((j) => j.id)
    if (tableList.length && jobList.length) {
      const jobIdToNewIndex = {}
      jobList.forEach((job, idx) => {
        jobIdToNewIndex[job.id] = idx
      })
      tableList.forEach((table) => {
        if (table.isRed) {
          table.isRed = table.isRed.map((fieldRedArr) => {
            const newArr = []
            fieldRedArr.forEach((isRed, oldJobIdx) => {
              const jobId = jobIdListForRed[oldJobIdx]
              if (!jobId) return
              const newJobIdx = jobIdToNewIndex[jobId]
              if (typeof newJobIdx === "number") {
                newArr[newJobIdx] = isRed
              }
            })
            for (let i = 0; i < jobList.length; i++) {
              if (typeof newArr[i] === "undefined") newArr[i] = false
            }
            return newArr
          })
        }
      })
    }
    // --- 高亮同步修复 end ---

    // 3. 如果开启隐藏相同项，过滤掉相同行
    let newFirstColumnHeights = []
    if (isHideSameTab) {
      const originalTableListCopy = JSON.parse(
        JSON.stringify(originalTableList)
      )
      tableList = this.filterSameRows(tableList)
      // 根据过滤后的数据结构映射高度
      newFirstColumnHeights = this.mapHeightsForFilteredData(
        originalTableListCopy,
        tableList,
        originalFirstColumnHeights
      )
    } else {
      // 不过滤时，直接使用原始高度数据
      newFirstColumnHeights =
        originalFirstColumnHeights.length > 0
          ? JSON.parse(JSON.stringify(originalFirstColumnHeights))
          : []
    }

    // 更新展示数据
    this.setData({
      jobList: jobList,
      tableList: tableList,
      firstColumnHeights: newFirstColumnHeights,
    })

    // 清理 oldJobIds
    if (this.data.oldJobIds) {
      this.setData({ oldJobIds: undefined })
    }

    // 如果没有高度数据，才重新计算
    if (newFirstColumnHeights.length === 0) {
      setTimeout(() => {
        this.syncCellHeights()
      }, 100)
    }
  },

  /**
   * 根据钉住规则排序职位
   */
  sortJobsByPinned(jobList, pinnedJobIds) {
    const pinnedJobs = []
    const unpinnedJobs = []

    // 分离钉住和未钉住的职位
    jobList.forEach((job) => {
      if (pinnedJobIds.includes(job.id)) {
        job.isTop = true
        pinnedJobs.push(job)
      } else {
        job.isTop = false
        unpinnedJobs.push(job)
      }
    })

    // 钉住的职位按照原始顺序排序
    pinnedJobs.sort((a, b) => {
      const aIndex = jobList.findIndex((job) => job.id === a.id)
      const bIndex = jobList.findIndex((job) => job.id === b.id)
      return aIndex - bIndex
    })

    // 未钉住的职位按照原始顺序排序
    unpinnedJobs.sort((a, b) => {
      const aIndex = jobList.findIndex((job) => job.id === a.id)
      const bIndex = jobList.findIndex((job) => job.id === b.id)
      return aIndex - bIndex
    })

    // 钉住的职位在前，未钉住的在后
    return [...pinnedJobs, ...unpinnedJobs]
  },

  /**
   * 重新排序表格数据以匹配职位顺序
   */
  reorderTableData(tableList, sortedJobList) {
    const originalJobList = this.data.originalJobList

    return tableList.map((table) => {
      const newRows = []

      // 按照新的职位顺序重新排列rows
      sortedJobList.forEach((sortedJob) => {
        const originalIndex = originalJobList.findIndex(
          (job) => job.id === sortedJob.id
        )
        if (originalIndex !== -1 && table.rows[originalIndex]) {
          newRows.push(table.rows[originalIndex])
        }
      })

      return {
        ...table,
        rows: newRows,
      }
    })
  },

  /**
   * 过滤掉相同的行
   */
  filterSameRows(tableList) {
    return tableList
      .map((table) => {
        const filteredFields = []
        const filteredData = []

        // 使用预计算的结果过滤字段
        table.fields.forEach((fieldObj, fieldIndex) => {
          // 如果不是相同项，保留这个字段
          if (!fieldObj.isSameRow) {
            filteredFields.push(fieldObj)

            // 如果这是第一个保留的字段，初始化数据数组
            if (filteredData.length === 0) {
              table.rows.forEach(() => filteredData.push({}))
            }

            // 添加这个字段的数据
            table.rows.forEach((row, rowIndex) => {
              filteredData[rowIndex][fieldObj.name] = row[fieldObj.name]
            })
          }
        })

        return {
          ...table,
          fields: filteredFields,
          rows: filteredData,
          // 添加标记，表示这个表格是否应该被隐藏
          isHidden: filteredFields.length === 0,
        }
      })
      .filter((table) => !table.isHidden) // 过滤掉没有内容的表格
  },

  /**
   * 为过滤后的数据映射对应的高度
   * @param {Array} originalTableList 原始表格数据
   * @param {Array} filteredTableList 过滤后的表格数据
   * @param {Array} originalHeights 原始高度数据
   * @returns {Array} 映射后的高度数据
   */
  mapHeightsForFilteredData(
    originalTableList,
    filteredTableList,
    originalHeights
  ) {
    if (!originalHeights || originalHeights.length === 0) {
      return []
    }

    const mappedHeights = []

    filteredTableList.forEach((filteredTable) => {
      // 通过表格ID找到对应的原始表格索引
      const originalTableIndex = originalTableList.findIndex(
        (originalTable) => originalTable.id === filteredTable.id
      )

      if (originalTableIndex === -1) {
        // 如果找不到对应的原始表格，跳过
        return
      }

      const originalTable = originalTableList[originalTableIndex]
      const originalTableHeights = originalHeights[originalTableIndex] || []
      const filteredTableHeights = []

      filteredTable.fields.forEach((filteredField) => {
        // 在原始表格中找到对应的字段索引
        const originalFieldIndex = originalTable.fields.findIndex(
          (originalField) => originalField.name === filteredField.name
        )

        // 如果找到对应的字段，使用原始高度，否则使用默认高度
        const height =
          originalFieldIndex >= 0
            ? originalTableHeights[originalFieldIndex]
            : 80
        filteredTableHeights.push(height)
      })

      mappedHeights.push(filteredTableHeights)
    })

    return mappedHeights
  },

  /**
   * 计算可滚动区域宽度和最大滚动距离
   */
  calculateScrollableWidth() {
    const { tableList, columnWidth, firstColumnWidth, jobList } = this.data

    if (
      !tableList ||
      !Array.isArray(tableList) ||
      tableList.length === 0 ||
      !jobList
    ) {
      return
    }

    // 职位数量就是列数
    const maxColumns = jobList.length

    // 计算可滚动区域宽度（所有职位列，rpx）
    const scrollableWidthRpx = maxColumns * columnWidth

    // 获取屏幕信息
    const systemInfo = wx.getSystemInfoSync()
    const screenWidthPx = systemInfo.screenWidth

    // 将rpx转换为px进行计算
    const scrollableWidthPx = (scrollableWidthRpx * screenWidthPx) / 750
    const firstColumnWidthPx = (firstColumnWidth * screenWidthPx) / 750
    const paddingPx = (40 * screenWidthPx) / 750 // 40rpx边距转px

    // 计算可视区域宽度（像素）
    const visibleWidthPx = screenWidthPx - firstColumnWidthPx - paddingPx

    // 计算最大滚动距离（像素）
    this.maxScrollLeft = Math.max(0, scrollableWidthPx - visibleWidthPx)

    this.setData({
      scrollableWidth: scrollableWidthRpx, // 保持rpx用于样式
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后，同步第一列和第二列的高度
    this.syncCellHeights()
  },

  /**
   * 同步第一列和数据行高度
   * 确保同行的第一列和数据行高度保持一致
   */
  syncCellHeights() {
    // 使用 setTimeout 确保 DOM 完全渲染完成，增加延迟时间
    setTimeout(() => {
      this.performHeightSync()
    }, 300)
  },

  /**
   * 执行高度同步逻辑 - 恢复原始简单逻辑
   */
  performHeightSync() {
    const query = wx.createSelectorQuery().in(this)
    const { tableList } = this.data

    if (!tableList || tableList.length === 0) {
      return
    }

    // 获取所有第一列单元格的高度
    query.selectAll(".first-column-cell").boundingClientRect()

    // 获取所有数据行的高度（排除顶部职位头部）
    query.selectAll(".table-scroll-section .data-row").boundingClientRect()

    query.exec((res) => {
      if (!res || res.length < 2) {
        return
      }

      const firstColumnCells = res[0] || []
      const dataRows = res[1] || []

      const newHeights = [] // 二维数组 [tableIndex][fieldIndex]
      let firstColumnIndex = 0
      let dataRowIndex = 0

      // 遍历每个表格
      tableList.forEach((table, tableIndex) => {
        const tableHeights = []
        const fieldCount = table.fields.length

        // 遍历当前表格的每个字段(每一行)
        for (let fieldIndex = 0; fieldIndex < fieldCount; fieldIndex++) {
          const firstCellHeight =
            firstColumnCells[firstColumnIndex]?.height || 0
          const dataRowHeight = dataRows[dataRowIndex]?.height || 0

          // 取两者中较高的高度
          const maxHeight = Math.max(firstCellHeight, dataRowHeight)
          tableHeights.push(maxHeight)

          firstColumnIndex++
          dataRowIndex++
        }

        newHeights.push(tableHeights)
      })

      // 更新数据，触发重新渲染
      this.setData({
        firstColumnHeights: newHeights,
        originalFirstColumnHeights: JSON.parse(JSON.stringify(newHeights)),
      })
    })
  },
  goJobDetail(e) {
    const { id } = e.currentTarget.dataset
    ROUTER.navigateTo({
      path: "/pages/job/detail/index",
      query: {
        id,
      },
    })
  },
  onLogin() {
    this.getInfo(this.data.jobIds)
  },
})
