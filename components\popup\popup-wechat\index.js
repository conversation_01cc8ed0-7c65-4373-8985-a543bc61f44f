// components/popup/popup-wechat/index.js
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    weichatShow: {
      type: Boolean,
      value: false,
    },
    img: {
      type: String,
      value:
        "https://webresource-1253756937.cos.ap-guangzhou.myqcloud.com/pc/common/qr_code.png",
    },
    info: {
      type: Object,
      value: null,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClickHide() {
      this.triggerEvent("onClickHide", false)
    },
  },
})
