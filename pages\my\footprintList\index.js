// pages/my/footprintList/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeIndex: 1,
    tipsShow: true,
    tabList: [
      {
        title: "公告",
        id: 1,
      },
      {
        title: "职位",
        id: 2,
      },
    ],
    dataList: [
      {
        time: "2025年6月16",
        list: ["1", "2", "3"],
      },
      {
        time: "2025年6月12",
        list: ["1", "2", "3"],
      },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      activeIndex: index,
    })
    // let activeIndex = this.data.tabList.findIndex(
    //   (item) => item.id == this.data.activeIndex
    // )
    // if (!this.data.tabList[activeIndex].dataList.length) {
    //   this.getList(1)
    // }
  },
  closetip() {
    this.setData({
      tipsShow: false,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
