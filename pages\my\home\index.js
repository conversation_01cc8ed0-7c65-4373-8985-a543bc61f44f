const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const scrollService = require("@/services/scrollService")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isComplete: false,
    navigationBarHeight: "",
    stickyTop: "",
    isLogin: true,
    resumeProgress: 0, // 简历完善度
    activeTabIndex: 1,
    subActiveTabIndex: 0,
    tipsShow: true,
    isSticky: false,
    baseInfo: null,
    tabList: [
      {
        title: "关注公告",
        subList: [],
      },
      {
        title: "关注职位",
        subList: [],
      },
      {
        title: "浏览足迹",
        subList: [
          {
            title: "公告",
          },
          {
            title: "职位",
          },
        ],
      },
    ],
    dataList: [],
    // 弹窗相关
    showOptionsPopup: false, // 显示操作选项弹窗
    showTagPopup: false, // 显示添加标签弹窗
    currentJobInfo: null, // 当前操作的职位信息
    // 一级弹窗选项数据
    optionsData: [],
    // 二级弹窗选项数据
    tagOptionsData: [],
    // 全局配置
    size: 20,
    isLoading: false, // 是否正在加载
    tips: "",
    pkListIds: [],
    isEndJobExpanded: false, // 已结束职位列表是否展开
    // 数据缓存 - 为每个tab缓存数据，避免重复请求
    tabDataCache: {
      0: { list: [], hasMore: false, page: 1 }, // 关注公告 - 不需要分页
      1: { list: [], hasMore: false, page: 1 }, // 关注职位 - 不需要分页
      "2-0": { list: [], hasMore: true, page: 1 }, // 浏览足迹-公告
      "2-1": { list: [], hasMore: true, page: 1 }, // 浏览足迹-职位
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.setData({
      baseInfo: APP.globalData.userInfo,
    })
    await this.loadTabData()
    this.getHeight()
  },
  async getResumeInfo() {
    const info = await APP.getResume()
    console.log(info, "111111111111111111111")
    this.setData({
      resumeProgress: info.complete_progress.progress,
    })
    console.log(this.data.resumeProgress, "==============")
  },
  getConfig() {
    const data = APP.globalData?.serverConfig?.job_tag || []
    let tagOptionsData = []
    const follows_tag = this.data.currentJobInfo.follows_tag
    if (data.length) {
      tagOptionsData = data.map((item) => {
        if (follows_tag == item.key) {
          return {
            name: "取消" + item.name,
            value: item.key,
          }
        } else {
          return {
            name: "加入" + item.name,
            value: item.key,
          }
        }
      })
    }
    this.setData({
      tagOptionsData,
    })
  },
  /**
   * 获取列表数据 - 支持分页加载
   * @param {boolean} isLoadMore - 是否为加载更多（分页）
   */
  async getList(isLoadMore = false) {
    const currentTabKey = this.getCurrentTabKey()
    const currentCache = this.getCurrentTabCache()

    // 如果正在加载，直接返回
    if (this.data.isLoading) {
      return
    }

    // 如果是加载更多但没有更多数据，直接返回
    if (isLoadMore && !currentCache.hasMore) {
      return
    }

    try {
      // 设置加载状态
      this.setData({
        isLoading: true,
      })

      // 构建请求参数
      const requestParams = this.buildRequestParams(
        isLoadMore ? currentCache.page : 1
      )

      // 发起请求
      const res = await UTIL.request(requestParams.url, requestParams.params)

      if (res && res.error && res.error.code === 0) {
        this.handleListResponse(res, isLoadMore, currentTabKey)
      } else {
        this.handleRequestError()
      }
      return res
    } catch (error) {
      console.error("获取列表数据异常:", error)
      this.handleRequestError()
    } finally {
      this.setData({
        isLoading: false,
        isComplete: true,
      })
    }
  },

  /**
   * 构建请求参数
   * @param {number} page - 页码
   */
  buildRequestParams(page) {
    const { activeTabIndex, subActiveTabIndex } = this.data

    // 确定API URL
    const url = activeTabIndex === 2 ? API.getFootPrintList : API.getFollowsList

    // 确定item_type
    let item_type
    if (activeTabIndex === 0) {
      item_type = "article"
    } else if (activeTabIndex === 1) {
      item_type = "job"
    } else {
      // activeTabIndex === 2 (浏览足迹)
      item_type = subActiveTabIndex === 0 ? "article" : "job"
    }

    // 关注公告和关注职位不需要分页，使用大的size值获取所有数据
    const requestSize =
      activeTabIndex === 0 || activeTabIndex === 1 ? 9999 : this.data.size

    return {
      url,
      params: {
        item_type,
        page,
        size: requestSize,
      },
    }
  },

  /**
   * 处理列表响应数据
   * @param {Object} res - 响应数据
   * @param {boolean} isLoadMore - 是否为加载更多
   * @param {string} currentTabKey - 当前tab标识
   */
  handleListResponse(res, isLoadMore, currentTabKey) {
    const resData = res?.data
    let newList = []
    let tips = ""

    // 根据不同的tab处理数据结构
    if (this.data.activeTabIndex === 2) {
      // 浏览足迹返回的是 { list: [], tips: "" }
      newList = resData?.list || []
      tips = resData?.tips || ""
    } else if (this.data.activeTabIndex === 0) {
      // 关注公告直接返回数组
      newList = resData || []
    } else {
      // 关注职位返回的是嵌套结构，需要保持原始结构用于显示
      newList = resData || []
    }

    // 获取当前缓存
    const currentCache = this.getCurrentTabCache()

    // 更新数据
    let updatedList
    if (isLoadMore) {
      // 分页加载：追加到现有数据
      updatedList = [...currentCache.list, ...newList]
    } else {
      // 首次加载或刷新：直接使用新数据
      updatedList = newList
    }

    // 判断是否还有更多数据
    // 关注公告和关注职位不需要分页，浏览足迹保持原有分页逻辑
    let hasMore
    if (this.data.activeTabIndex === 0 || this.data.activeTabIndex === 1) {
      hasMore = false // 关注公告和关注职位不需要分页
    } else {
      hasMore = newList.length >= this.data.size // 浏览足迹保持分页
    }

    // 更新缓存
    const updatedCache = {
      ...this.data.tabDataCache,
      [currentTabKey]: {
        list: updatedList,
        hasMore,
        page: isLoadMore ? currentCache.page + 1 : 2, // 下次请求的页码
      },
    }

    // 更新页面数据
    this.setData({
      dataList: updatedList,
      tips,
      tabDataCache: updatedCache,
    })
  },

  /**
   * 处理请求错误
   */
  handleRequestError() {
    const currentTabKey = this.getCurrentTabKey()

    this.setData({
      [`tabDataCache.${currentTabKey}.hasMore`]: false,
    })

    wx.showToast({
      title: "加载失败，请重试",
      icon: "none",
    })
  },
  async getHeight() {
    try {
      // 使用scrollService获取tab-area元素位置信息
      const rect = await scrollService.getElementRect(".tab-area")
      const stickyTop = rect.top

      this.setData({
        stickyTop,
      })
      scrollService.setStickyTop(this.data.stickyTop)
    } catch (error) {
      console.error("获取tab-area高度失败:", error)
    }

    let menuInfo = wx.getMenuButtonBoundingClientRect()
    this.setData({
      navigationBarHeight: menuInfo.top + 32,
    })
  },

  // 获取当前tab的标识
  getCurrentTabKey() {
    const { activeTabIndex, subActiveTabIndex } = this.data
    if (activeTabIndex === 2) {
      return `${activeTabIndex}-${subActiveTabIndex}`
    }
    return activeTabIndex.toString()
  },

  // 获取当前tab的缓存状态
  getCurrentTabCache() {
    const currentTabKey = this.getCurrentTabKey()
    return this.data.tabDataCache[currentTabKey]
  },

  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const currentTabKey = this.getCurrentTabKey()
    const stickyTop = this.data.stickyTop - this.data.navigationBarHeight

    scrollService.setScrollPosition(currentTabKey, scrollTop)

    if (scrollTop > stickyTop && !this.data.isSticky) {
      this.setData({
        isSticky: true,
      })
    }
    if (scrollTop < stickyTop && this.data.isSticky) {
      this.setData({
        isSticky: false,
      })
    }
  },
  // 处理职位卡片的三个点点击事件
  onJobOptionsClick(e) {
    let optionsData = [
      { name: "添加标签", value: "addTag" },
      { name: "加入对比", value: "addToCompare" },
      { name: "取消关注", value: "unfollow" },
    ]
    const { jobId, job_name, follows_id, follows_tag } = e.detail
    if (follows_tag) {
      optionsData[0].name = "修改标签"
    }
    if (this.data.pkListIds.length && this.data.pkListIds.includes(jobId)) {
      optionsData = [
        { name: "添加标签", value: "addTag" },
        { name: "取消关注", value: "unfollow" },
      ]
    }
    this.setData({
      currentJobInfo: {
        jobId: jobId,
        job_name: job_name,
        follows_id: follows_id,
        follows_tag: follows_tag,
      },
      optionsData,
      showOptionsPopup: true,
    })
  },
  goResume() {
    ROUTER.navigateTo({
      path: "/pages/my/resume/index",
    })
  },
  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    if (index == this.data.activeTabIndex) {
      return
    }
    this.setData({
      activeTabIndex: index,
    })
    this.loadTabData()
    const currentTabKey = this.getCurrentTabKey()
    scrollService.restoreScrollPosition(currentTabKey, { duration: 0 })
  },
  changeSubIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      subActiveTabIndex: index,
    })
    this.loadTabData()
    const currentTabKey = this.getCurrentTabKey()
    scrollService.restoreScrollPosition(currentTabKey, { duration: 0 })
  },

  /**
   * 加载tab数据 - 优先使用缓存
   */
  async loadTabData() {
    const currentCache = this.getCurrentTabCache()

    // 如果有缓存数据，直接使用
    if (currentCache.list.length > 0) {
      this.setData({
        dataList: currentCache.list,
        isLoading: false,
      })
    } else {
      // 没有缓存数据，重新请求
      return await this.getList(false)
    }
  },

  /**
   * 从当前列表中删除指定元素
   * @param {string|number} itemId - 要删除的元素ID
   * @param {string} idField - ID字段名，默认为'id'
   */
  removeItemFromCurrentList(itemId, idField = "id") {
    const currentTabKey = this.getCurrentTabKey()
    const currentCache = this.getCurrentTabCache()

    let updatedList

    // 根据不同tab处理不同的数据结构
    if (this.data.activeTabIndex === 1) {
      // 关注职位：嵌套结构 [{ title: "公告", list: [职位...] }]
      updatedList = currentCache.list
        .map((group) => ({
          ...group,
          list: group.list
            ? group.list.filter((item) => item[idField] != itemId)
            : [],
        }))
        .filter((group) => group.list.length > 0) // 移除空的分组
    } else {
      // 关注公告和浏览足迹：扁平结构 [项目...]
      updatedList = currentCache.list.filter((item) => item[idField] != itemId)
    }

    // 更新缓存和页面数据
    const updatedCache = {
      ...this.data.tabDataCache,
      [currentTabKey]: {
        ...currentCache,
        list: updatedList,
      },
    }

    this.setData({
      dataList: updatedList,
      tabDataCache: updatedCache,
    })
  },

  /**
   * 更新当前列表中指定元素的标签
   * @param {string|number} itemId - 要更新的元素ID
   * @param {string} newTag - 新的标签值
   * @param {string} idField - ID字段名，默认为'id'
   */
  updateItemTag(itemId, newTag, idField = "id") {
    const currentTabKey = this.getCurrentTabKey()
    const currentCache = this.getCurrentTabCache()

    let updatedList

    // 根据不同tab处理不同的数据结构
    if (this.data.activeTabIndex === 1) {
      // 关注职位：嵌套结构 [{ title: "公告", list: [职位...] }]
      updatedList = currentCache.list.map((group) => ({
        ...group,
        list: group.list
          ? group.list.map((item) => {
              if (item[idField] == itemId) {
                return { ...item, follows_tag: newTag }
              }
              return item
            })
          : [],
      }))
    } else {
      // 关注公告和浏览足迹：扁平结构 [项目...]
      updatedList = currentCache.list.map((item) => {
        if (item[idField] == itemId) {
          return { ...item, follows_tag: newTag }
        }
        return item
      })
    }

    // 更新缓存和页面数据
    const updatedCache = {
      ...this.data.tabDataCache,
      [currentTabKey]: {
        ...currentCache,
        list: updatedList,
      },
    }

    this.setData({
      dataList: updatedList,
      tabDataCache: updatedCache,
    })
  },

  closetip() {
    this.setData({
      tipsShow: false,
    })
  },

  // 关闭操作选项弹窗
  closeOptionsPopup() {
    this.setData({
      showOptionsPopup: false,
    })
  },

  // 关闭添加标签弹窗
  closeTagPopup() {
    this.setData({
      showTagPopup: false,
    })
  },

  // 处理一级弹窗选择事件
  onOptionsSelect(e) {
    const { value } = e.detail
    this.setData({
      showOptionsPopup: false,
    })

    switch (value) {
      case "addTag":
        this.getConfig()
        this.setData({
          showTagPopup: true,
        })
        break
      case "addToCompare":
        this.addToCompare()
        break
      case "unfollow":
        wx.showModal({
          title: "确认取消关注",
          content: `确定要取消关注"${this.data.currentJobInfo?.job_name}"吗？`,
          success: (res) => {
            if (res.confirm) {
              UTIL.request(API.setFollows, {
                item_type: "job",
                item_no: [this.data.currentJobInfo.jobId],
                type: "unfollow",
              }).then((res) => {
                if (res) {
                  wx.showToast({
                    title: "已取消关注",
                    icon: "none",
                  })
                  // 只删除当前元素，不清除整个缓存
                  this.removeItemFromCurrentList(this.data.currentJobInfo.jobId)
                }
              })
            }
          },
        })
        break
    }
  },

  // 处理二级弹窗选择事件
  async onTagSelect(e) {
    const { value } = e.detail
    const param = {
      follows_id: this.data.currentJobInfo.follows_id,
      tag: value,
    }
    const res = await UTIL.request(API.setFollowTag, param)
    if (res) {
      wx.showToast({
        title:
          value == this.data.currentJobInfo.follows_tag
            ? "取消成功"
            : "加入成功",
        icon: "none",
      })
      this.setData({
        showTagPopup: false,
      })
      // 标签修改成功后，更新当前元素的标签信息
      this.updateItemTag(this.data.currentJobInfo.jobId, value)
    }

    let toastTitle = ""
    switch (value) {
      case "addToApplication":
        toastTitle = "已加入报考岗位"
        break
      case "addToIntention":
        toastTitle = "已加入意向岗位"
        break
    }

    if (toastTitle) {
      wx.showToast({
        title: toastTitle,
        icon: "none",
      })
    }
  },

  addToCompare() {
    const newPkListIds = this.data.pkListIds
    if (!newPkListIds.includes(this.data.currentJobInfo.jobId)) {
      newPkListIds.push(this.data.currentJobInfo.jobId)
    }
    wx.setStorageSync("pkListIds", newPkListIds)
    this.setData({
      pkListIds: newPkListIds,
    })
    wx.showToast({
      title: "已加入对比",
      icon: "none",
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPkListIds()
    this.getResumeInfo()
    if (this.data.isComplete) {
      this.loadTabData()
    }
  },

  getPkListIds() {
    const pkListIds = wx.getStorageSync("pkListIds") || []
    this.setData({
      pkListIds,
    })
  },
  goComparison() {
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
    })
  },
  toggleEndJob() {
    this.setData({
      isEndJobExpanded: !this.data.isEndJobExpanded,
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log("触底了")
    // 从当前tab缓存中获取状态
    const currentCache = this.getCurrentTabCache()

    // 只有浏览足迹需要分页加载，关注公告和关注职位不需要分页
    if (
      this.data.activeTabIndex === 2 &&
      currentCache.hasMore &&
      !this.data.isLoading
    ) {
      // 加载下一页数据
      this.getList(true)
    }
  },
  goSubscribe() {
    ROUTER.navigateTo({
      path: "/pages/subscribe/list/index",
    })
  },
})
