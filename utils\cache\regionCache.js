import { setCache, getCache } from "./core/core"

/**
 * 设置选中的地区缓存
 * @param {Array} regions - 选中的地区数组
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "exam")
 */
export function setSelectedRegionsCache(regions, pageType = "announcement") {
  const cacheKey = `${pageType}_region_selected`
  return setCache(cacheKey, regions)
}

/**
 * 获取选中的地区缓存
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "exam")
 * @returns {Array} 选中的地区数组
 */
export function getSelectedRegionsCache(pageType = "announcement") {
  const cacheKey = `${pageType}_region_selected`
  return getCache(cacheKey) || []
}
