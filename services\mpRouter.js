const BASE_CACHE = require("@/utils/cache/baseCache")
export function createPathWithParams(path, params) {
  if (!params || typeof params !== "object") return path
  const query = convertPathQuery(params, convertQueryParamValue)
  return query ? `${path}?${query}` : path
}

/**
 * 转换单个参数值，使其适合URL查询字符串。
 * @param {string|object} val - 参数的值。
 * @returns {string} - 转换后的值。
 */
function convertQueryParamValue(val) {
  if (typeof val === "object") {
    return JSON.stringify(val)
  } else if (typeof val === "string" && val?.indexOf("://") >= 0) {
    return encodeURIComponent(val)
  }
  return val
}

/**
 * 遍历参数对象，并使用convertQueryParamValue函数转换每个值。
 * @param {Object} params - 包含查询参数的对象。
 * @returns {string} - 转换后的查询字符串。
 */
export function convertPathQuery(params, call) {
  return Object.keys(params)
    .map((key) => {
      if (typeof call === "function") {
        return `${key}=${call(params[key])}`
      }
      return `${key}=${params[key]}`
    })
    .join("&")
}
export function mergeUrlParams(url, params) {
  // 拆分URL为路径和查询部分
  const [path, queryString] = url.split("?")

  // 解析原有的查询参数
  const searchParams = {}
  if (queryString) {
    queryString.split("&").forEach((pair) => {
      const [key, value] = pair.split("=")
      if (key) {
        // 解码键和值
        const decodedKey = decodeURIComponent(key)
        const decodedValue = decodeURIComponent(value || "")
        searchParams[decodedKey] = decodedValue
      }
    })
  }

  // 过滤掉params中值为undefined或null的参数
  const filteredParams = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== undefined && value !== null) {
      filteredParams[key] = value
    }
  })

  // 合并参数，后者覆盖前者
  const mergedParams = { ...searchParams, ...filteredParams }

  // 将参数转换为查询字符串
  const queryArray = Object.keys(mergedParams).map((key) => {
    // 编码键和值
    const encodedKey = encodeURIComponent(key)
    const encodedValue = encodeURIComponent(mergedParams[key])
    return `${encodedKey}=${encodedValue}`
  })

  const newQueryString = queryArray.join("&")

  // 拼接路径和查询字符串
  return newQueryString ? `${path}?${newQueryString}` : path
}

function navigate({ api, data = {} }) {
  const { path, query } = data

  // 检查路径是否有效
  if (!path && api !== "navigateBack") throw new Error("路径传参有误")
  const defaultQuery = {}
  const baseCache = BASE_CACHE.getBaseCache()
  if (baseCache.campus?.id) {
    defaultQuery.campus_id = baseCache.campus.id
  }
  if (baseCache.province?.key) {
    defaultQuery.province = baseCache.province.key
  }

  let newQuery = query
  if (!query?.province && !Number(query?.campus_id)) {
    newQuery = Object.assign(defaultQuery, query || {})
  }

  // 构建完整的URL
  const url = createPathWithParams(path, newQuery)

  return wx[api]({ url })
}

export function navigateTo(data = {}) {
  return navigate({ api: "navigateTo", data })
}

export function redirectTo(data = {}) {
  return navigate({ api: "redirectTo", data })
}

export function switchTab(data = {}) {
  return navigate({ api: "switchTab", data })
}

export function navigateBack(data = {}) {
  if (getCurrentPages().length <= 1) {
    reLaunch({ path: "/pages/index/index" })
    return
  }
  return navigate({ api: "navigateBack", data })
}

export function reLaunch(data = {}) {
  return navigate({ api: "reLaunch", data })
}
