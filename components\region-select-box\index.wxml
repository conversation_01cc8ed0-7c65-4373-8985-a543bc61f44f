<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>
<view class="select-box {{inPopup ? 'popup-mode' : ''}}" data-select-level="{{selectLevel}}" style="{{inPopup ? 'height: ' + popupHeight : ''}}">
  <!-- 已选择的地区标签列表 -->
  <view class="select-top {{isShowBg?'bg':''}} {{noPadding?'pd0':''}}">
    <block wx:if="{{tempSelected.length}}">
      <scroll-view scroll-x class="select-list" show-scrollbar="{{false}}" enhanced>

        <view class="select-list-item" wx:for="{{tempSelected}}" wx:key="id" data-index="{{index}}" catchtap="removeSelectedRegion">
          {{item.short_name||item.area_name || item.text}}
          <image class="close" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_close_red.png"></image>
        </view>

      </scroll-view>
      <view class="clear-box" catchtap="clearAllSelected">
        <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_delete.png"></image>
        清空
      </view>
    </block>
    <block wx:else>
      <view class="disable-box">
        <view class="no-text">
          请选择报考地区
        </view>
        <view class="clear-box hui">
          <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_delete_hui.png"></image>
          清空
        </view>
      </view>
    </block>


  </view>

  <!-- 三级联动选择区域 -->
  <view class="select-center">


    <!-- parseRegion -->
    <!-- 省份列表 -->
    <scroll-view scroll-y style="background: rgba(247, 248, 250, 1);" class="select-center-item" show-scrollbar="{{false}}" enhanced>
      <view class="province {{item.key===currentProvince.key ? 'active' : ''}}" wx:for="{{provinceList}}" wx:key="code" data-index="{{index}}" catchtap="handleProvinceClick">
        {{item.short_name||item.text}}
        <view class="num {{item.key===currentProvince.key ? 'active' : ''}}" wx:if="{{filterUtils.countMatchingByLevel(tempSelected,item.id,1)}}">{{filterUtils.countMatchingByLevel(tempSelected,item.id,1)}}</view>
      </view>
    </scroll-view>
    <!-- 城市列表 -->
    <scroll-view scroll-y class="select-center-item" show-scrollbar="{{false}}" enhanced>
      <view class="city {{item.key===currentCity.key ? 'active' : ''}}" wx:for="{{cityList}}" wx:key="code" data-index="{{index}}" catchtap="handleCityClick">
        {{item.area_name||item.text}}
        <!-- 二级选择时显示选中状态 -->
        <image class="img" wx:if="{{selectLevel === 2}}" src="{{filterUtils.isSelectOptionSelected(item.id,tempSelected) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_select.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_no.png'}}"> </image>
        <!-- 三级选择时的原有逻辑 -->
        <block wx:elif="{{selectLevel === 3}}">
          <image class="img" wx:if="{{item.single ==1}}" src="{{filterUtils.isSelectOptionSelected(item.id,tempSelected) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_select.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_no.png'}}"> </image>
          <block wx:else>
            <view class="num {{item.key===currentCity.key ? 'active' : ''}}" wx:if="{{filterUtils.countMatchingByLevel(tempSelected,item.id,2)}}">{{filterUtils.countMatchingByLevel(tempSelected,item.id,2)}} </view>
          </block>
        </block>
      </view>
      <!-- 空状态 -->
      <view wx:if="{{!loading.city && cityList.length === 0 && currentProvince}}" class="empty-text">暂无城市数据</view>
    </scroll-view>
    <!-- 区县列表 - 只在三级选择时显示 -->
    <scroll-view wx:if="{{selectLevel === 3}}" scroll-y class="select-center-item" show-scrollbar="{{false}}" enhanced>
      <block wx:if="{{districtList}}">
        <view class="region {{filterUtils.includesKey(tempSelected,item.key) ? 'active' : ''}}" wx:for="{{districtList}}" wx:key="code" data-index="{{index}}" catchtap="handleDistrictClick">
          {{item.area_name||item.text}}
          <image class="img" src="{{filterUtils.includesKey(tempSelected,item.key) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_select.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_area_no.png'}}">
          </image>
        </view>
      </block>
      <!-- 空状态 -->
      <view wx:if="{{!loading.district && districtList.length === 0 && currentCity}}" class="empty-text">暂无区县数据</view>
    </scroll-view>
  </view>

  <!-- 确认按钮 -->
  <view class="action-bar-box" wx:if="{{!isSubscribe}}">
    <view class="action-bar container flex-justify_between">
      <view class="confirm-btn" catchtap="confirmSelection">完成
        <block wx:if="{{tempSelected.length>0}}">
          （已选{{tempSelected.length}}）
        </block>
      </view>
    </view>
  </view>
  <!-- 确认按钮 -->
  <view class="action-bar-box" wx:else>
    <view class="action-bar container flex-justify_between">
      <view class="stepbtn" style="width: 50%;" catchtap="step">上一步</view>
      <view class="confirm-btn" style="width: 50%;" catchtap="confirmSelection">{{subscribedData.is_subscribe==0 ?'下一步':'保存'}}
        <block wx:if="{{tempSelected.length>0}}">
          （已选{{tempSelected.length}}）
        </block>
      </view>
    </view>
  </view>
</view>