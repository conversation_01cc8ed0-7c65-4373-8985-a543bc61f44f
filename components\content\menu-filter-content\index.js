/**
 * 菜单筛选内容组件（重构版）
 * 专门处理菜单筛选的具体内容和逻辑
 * 支持多种筛选类型：适合我、有编制、地区、考试、筛选
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 菜单类型：suitable(适合我)、establishment(有编制)、apply_region(地区)、exam_type(考试)、filter_list(筛选)
    menuType: {
      type: String,
      value: "suitable",
    },
    // 初始选中的选项
    initialSelectedOptions: {
      type: Object,
      value: {},
    },
    // 初始筛选条件
    initialFilterConditions: {
      type: Object,
      value: {
        education: "all",
        experience: "all",
      },
    },
    // 弹窗标题
    popupTitle: {
      type: String,
      value: "筛选",
    },
    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      value: false,
    },

    // 动画显示状态
    animationShow: {
      type: Boolean,
      value: false,
    },
    // 关闭按钮图标
    closeIcon: {
      type: String,
      value:
        "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/close_icon.png",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 选中的选项
    selectedOptions: {},
    // 筛选条件
    filterConditions: {
      education: "all",
      experience: "all",
    },
    // 菜单选项数据
    menuOptions: {
      suitable: [
        { value: "all", label: "全部职位" },
        { value: "match", label: "匹配度高" },
        { value: "recent", label: "最近发布" },
      ],
      establishment: [
        { value: "all", label: "全部" },
        { value: "career", label: "事业编制" },
        { value: "civil", label: "公务员" },
        { value: "contract", label: "合同制" },
      ],
      apply_region: [
        { value: "all", label: "全部地区" },
        { value: "beijing", label: "北京" },
        { value: "shanghai", label: "上海" },
        { value: "guangzhou", label: "广州" },
        { value: "shenzhen", label: "深圳" },
        { value: "chengdu", label: "成都" },
      ],
      exam_type: [
        { value: "all", label: "全部考试" },
        { value: "civil-service", label: "公务员考试" },
        { value: "institution", label: "事业单位" },
        { value: "teacher", label: "教师招聘" },
        { value: "selection", label: "选调生" },
      ],
    },
    // 筛选选项数据
    filterOptions: {
      education: [
        { value: "all", label: "不限" },
        { value: "bachelor", label: "本科" },
        { value: "master", label: "硕士" },
        { value: "doctor", label: "博士" },
      ],
      experience: [
        { value: "all", label: "不限" },
        { value: "fresh", label: "应届生" },
        { value: "1-3", label: "1-3年" },
        { value: "3+", label: "3年以上" },
      ],
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择选项
     * @param {Object} e 事件对象
     */
    selectOption(e) {
      const { value } = e.currentTarget.dataset
      const { menuType } = this.properties

      // 更新选中状态
      this.setData({
        [`selectedOptions.${menuType}`]: value,
      })

      // 触发选择事件
      this.triggerEvent(
        "select",
        {
          type: menuType,
          value: value,
        },
        {}
      )

      // 如果不是筛选类型，选择后自动关闭
      if (menuType !== "filter_list") {
        this.triggerEvent("autoClose", {}, {})
      }
    },

    /**
     * 选择筛选条件
     * @param {Object} e 事件对象
     */
    selectFilter(e) {
      const { type, value } = e.currentTarget.dataset

      this.setData({
        [`filterConditions.${type}`]: value,
      })

      // 触发筛选条件变化事件
      this.triggerEvent(
        "filterChange",
        {
          type: type,
          value: value,
          filterConditions: this.data.filterConditions,
        },
        {}
      )
    },

    /**
     * 重置筛选条件
     */
    reset() {
      const { menuType } = this.properties

      if (menuType === "filter_list") {
        // 重置筛选条件
        this.setData({
          filterConditions: {
            education: "all",
            experience: "all",
          },
        })
      } else {
        // 重置选中选项
        this.setData({
          [`selectedOptions.${menuType}`]: null,
        })
      }

      // 触发重置事件
      this.triggerEvent("reset", { type: menuType }, {})
    },

    /**
     * 确认筛选
     */
    confirm() {
      const { menuType } = this.properties
      const { selectedOptions, filterConditions } = this.data

      const result = {
        type: menuType,
        selectedOption: selectedOptions[menuType],
        filterConditions: filterConditions,
      }

      // 触发确认事件
      this.triggerEvent("confirm", result, {})
    },

    /**
     * 获取当前选择结果
     */
    getResult() {
      const { menuType } = this.properties
      const { selectedOptions, filterConditions } = this.data

      return {
        type: menuType,
        selectedOption: selectedOptions[menuType],
        filterConditions: filterConditions,
      }
    },

    /**
     * 设置选中状态
     * @param {Object} options 选中的选项
     */
    setSelectedOptions(options) {
      this.setData({
        selectedOptions: options || {},
      })
    },

    /**
     * 设置筛选条件
     * @param {Object} conditions 筛选条件
     */
    setFilterConditions(conditions) {
      this.setData({
        filterConditions: {
          ...this.data.filterConditions,
          ...conditions,
        },
      })
    },

    /**
     * 获取当前菜单选项
     */
    getCurrentMenuOptions() {
      const { menuType } = this.properties
      return this.data.menuOptions[menuType] || []
    },

    /**
     * 检查选项是否被选中
     */
    isOptionSelected(value) {
      const { menuType } = this.properties
      const { selectedOptions } = this.data
      return selectedOptions[menuType] === value
    },

    /**
     * 检查筛选条件是否被选中
     */
    isFilterSelected(type, value) {
      const { filterConditions } = this.data
      return filterConditions[type] === value
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.triggerEvent("close", {}, {})
    },

    /**
     * 重置筛选条件（弹窗底部按钮）
     */
    handleReset() {
      this.reset()
    },

    /**
     * 确认筛选（弹窗底部按钮）
     */
    handleConfirm() {
      const result = this.getResult()
      this.triggerEvent("confirm", result, {})
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
    },
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化数据
      this.setData({
        selectedOptions: this.properties.initialSelectedOptions || {},
        filterConditions: {
          ...this.data.filterConditions,
          ...this.properties.initialFilterConditions,
        },
      })
    },

    detached() {
      // 组件实例被从页面节点树移除时执行
    },
  },

  /**
   * 数据监听器
   */
  observers: {
    initialSelectedOptions: function (newVal) {
      if (newVal) {
        this.setData({
          selectedOptions: newVal,
        })
      }
    },

    initialFilterConditions: function (newVal) {
      if (newVal) {
        this.setData({
          filterConditions: {
            ...this.data.filterConditions,
            ...newVal,
          },
        })
      }
    },
  },
})
