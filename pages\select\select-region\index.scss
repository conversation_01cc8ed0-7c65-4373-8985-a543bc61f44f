page {
  background-color: #fff;
  height: 100vh;
}

.lefts {
  padding-left: 32rpx;
  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.rights {
  .collection-img {
    width: 40rpx;
    height: 40rpx;
    margin-left: 34rpx;
  }
}

.select-top {
  padding: 24rpx 160rpx 24rpx 32rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(235, 236, 240, 1);
  transform: rotateZ(360deg);
  .select-list {
    display: flex;
    align-items: center;
    white-space: nowrap;
    &-item {
      font-size: 22rpx;
      color: rgba(230, 0, 3, 1);
      background: rgba(230, 0, 3, 0.05);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16rpx 0 24rpx;
      height: 64rpx;
      border-radius: 12rpx;
      margin-right: 16rpx;
      .close {
        width: 24rpx;
        height: 24rpx;
        margin-left: 16rpx;
        transform: translateY(3rpx);
      }
    }
  }
  .clear-box {
    position: absolute;
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: rgba(60, 61, 66, 1);
    background: #fff;
    top: 0;
    right: 0;
    height: 100%;
    padding: 0 32rpx 0 44rpx;
    &::after {
      display: block;
      content: " ";
      width: 2rpx;
      height: 64rpx;
      background: rgba(235, 236, 240, 1);
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 22rpx;
    }
    .img {
      width: 32rpx;
      height: 32rpx;
      transform: translateY(1rpx);
      margin-right: 4rpx;
    }
  }
}

.main-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;
  box-sizing: border-box;
}

.action-bar .button-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .button {
    width: calc(50% - 7rpx);
    background-color: var(--main-color);
    font-size: 30rpx;
    color: #fff;
    height: 84rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .w100 {
    width: 100% !important;
  }
  .next-btn {
    background: #ffffff;
    border: 1rpx solid rgba(230, 0, 0, 0.6);
    transform: rotateZ(360deg);
    color: #e60000;
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  transform: rotateZ(360deg);
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.confirm-btn {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

// 通用样式
.container {
  max-width: 750rpx;
  margin: 0 auto;
}

.flex-justify_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
