const CACHE_NAME = "essayQuestion"

/**
 * 获取所有缓存数据
 * @returns {Object} 所有缓存数据
 */
function getAllCache() {
  try {
    return wx.getStorageSync(CACHE_NAME) || {}
  } catch (e) {
    console.error("获取缓存失败:", e)
    return {}
  }
}

/**
 * 保存所有缓存数据
 * @param {Object} data - 要保存的数据
 */
function saveAllCache(data) {
  try {
    wx.setStorageSync(CACHE_NAME, data)
  } catch (e) {
    console.error("保存缓存失败:", e)
  }
}

/**
 * 获取缓存的作答数据
 * @param {string} correction_project_no - 项目编号
 * @param {number} correction_rounds_id - 轮次ID
 * @returns {Object|null} 缓存的作答数据
 */
export function getEssayQuestionCache(
  correction_project_no,
  correction_rounds_id
) {
  try {
    const allCache = getAllCache()
    const key = `${correction_project_no}_${correction_rounds_id}`
    return allCache[key] || null
  } catch (e) {
    console.error("获取作答缓存失败:", e)
    return null
  }
}

/**
 * 更新作答缓存
 * @param {string} correction_project_no - 项目编号
 * @param {number} correction_rounds_id - 轮次ID
 * @param {Object} data - 要缓存的数据
 */
export function updateEssayQuestionCache(
  correction_project_no,
  correction_rounds_id,
  data
) {
  try {
    const allCache = getAllCache()
    const key = `${correction_project_no}_${correction_rounds_id}`
    allCache[key] = {
      ...data,
      updateTime: Date.now(),
    }
    saveAllCache(allCache)
  } catch (e) {
    console.error("更新作答缓存失败:", e)
  }
}

/**
 * 删除作答缓存
 * @param {string} correction_project_no - 项目编号
 * @param {number} correction_rounds_id - 轮次ID
 */
export function deleteEssayQuestionCache(
  correction_project_no,
  correction_rounds_id
) {
  try {
    const allCache = getAllCache()
    const key = `${correction_project_no}_${correction_rounds_id}`
    delete allCache[key]
    saveAllCache(allCache)
  } catch (e) {
    console.error("删除作答缓存失败:", e)
  }
}

/**
 * 清除所有缓存
 */
export function clearAllEssayQuestionCache() {
  try {
    wx.removeStorageSync(CACHE_NAME)
  } catch (e) {
    console.error("清除所有缓存失败:", e)
  }
}

/**
 * 获取所有作答缓存
 * @returns {Object} 所有作答缓存数据
 */
export function getAllEssayQuestionCache() {
  return getAllCache()
}
