<view class="work-info-card">
  <view class="title-area">
    <view class="title">工作经历</view>
    <view class="tips">{{incompleteTips}}</view>
  </view>
  <view class="form-item" wx:for="{{formList}}" wx:key="index" bindtap="onFormItemTap" data-field="{{item.field}}">
    <view class="left">
      {{item.title}}
      <text wx:if="{{item.required}}" class="cred">*</text>
    </view>
    <view class="right">
      <view class="text {{item.value ? 'has-value' : ''}}">{{item.text || '待完善'}}</view>
      <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
    </view>
  </view>

  <!-- 服务基层项目选择器 -->
  <van-popup show="{{ serviceShow }}" round position="bottom" z-index="9999" bind:close="onServiceClose">
    <van-picker id="servicePicker" show-toolbar title="请选择服务基层项目" columns="{{ serviceColumns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onServiceClose" bind:confirm="onServiceConfirm" />
  </van-popup>

  <!-- 基层工作经验选择器 -->
  <van-popup show="{{ experienceShow }}" round position="bottom" z-index="9999" bind:close="onExperienceClose">
    <van-picker id="experiencePicker" show-toolbar title="请选择基层工作经验" columns="{{ experienceColumns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onExperienceClose" bind:confirm="onExperienceConfirm" />
  </van-popup>
</view>