page {
  background: #f2f4f7;
}

.tab-title {
  font-size: 36rpx;
  color: #22242e;
  font-weight: 600;
}
.focus-list {
  height: calc(100vh - 176rpx);
  display: flex;
  flex-direction: column;

  .scroll-list {
    flex: 1;
    height: 0;
  }

  .pd-style {
    padding: 0 80rpx;
  }
  .tab-list {
    background: #fff;
    padding-bottom: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    &-item {
      flex: 1;
      font-size: 28rpx;
      padding: 32rpx 0;
      color: #666666;
      font-weight: 400;
      text-align: center;
      &.active {
        font-weight: 600;
        font-size: 32rpx;
        color: #22242e;
        .text {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: -20rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 6rpx;
            background: #e60003;
            border-radius: 4rpx;
          }
        }
      }
    }
  }
  .list-area {
    padding: 0 32rpx 60rpx 32rpx;
    box-sizing: border-box;
    .list-item {
      margin-top: 32rpx;
      .title-text {
        color: #919499;
        font-size: 24rpx;
        margin-bottom: 32rpx;
      }
    }
  }
}

// 职位列表容器
.job-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.bottom-btn {
  width: 100%;
  padding: 30rpx 0;
  text-align: center;
  color: #919499;
  font-size: 28rpx;
}
.edit-area {
  padding: 14rpx 32rpx;
  box-sizing: border-box;
  background: #fff;
  display: flex;
  align-items: center;
  .left {
    display: flex;
    align-items: center;
    margin-right: 48rpx;
    .icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }
    .text {
      font-size: 26rpx;
      color: #666666;
    }
  }
  .right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right-btn {
      background: #ec3e33;
      border-radius: 16rpx;
      width: calc(50% - 9rpx);
      height: 84rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 28rpx;
      font-weight: 500;
      // 禁用状态（未进行排序操作）
      &.disabled {
        opacity: 0.5;
      }

      // 激活状态（已进行排序操作）
      &.active {
        opacity: 1;

        &:active {
          opacity: 0.8;
        }
      }
    }
    .cancel-btn {
      background: rgba(235, 236, 240, 0.5) !important;
      color: #666666 !important;
    }
  }
}

// 排序保存按钮样式
.sort-area {
  padding: 14rpx 32rpx;
  box-sizing: border-box;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btn {
    width: calc(50% - 12rpx);
    height: 88rpx;
    background: #e60003;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;

    // 禁用状态（未进行排序操作）
    &.disabled {
      opacity: 0.5;
    }

    // 激活状态（已进行排序操作）
    &.active {
      opacity: 1;

      &:active {
        opacity: 0.8;
      }
    }
  }
  .cancel-btn {
    background: rgba(235, 236, 240, 0.5) !important;
    color: #666666 !important;
  }
}
.van-action-sheet__item {
  padding: 30rpx !important;
  font-size: 28rpx !important;
  line-height: 44rpx !important;
  border-bottom: 1rpx solid #f1f1f1 !important;
  transform: rotateZ(360deg);
  color: #3c3d42 !important;
  position: relative;
}

/* 最后一个选项不显示下边框 */
.van-action-sheet__item:last-child {
  border-bottom: none !important;
}

/* 选项点击态 */
.van-action-sheet__item:active {
  background-color: #f5f5f5 !important;
}
/* 为选项添加分割线样式 */
:host {
  --van-action-sheet-item-line-height: 48rpx;
}

.compare-ball {
  position: fixed;
  bottom: 230rpx;
  right: 0;
  width: 104rpx;
  height: 92rpx;
  border-radius: 46rpx 0 0 46rpx;
  background: #ffffff;
  box-shadow: 0 8rpx 24rpx 2rpx rgba(34, 36, 46, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .img-area {
    position: relative;
    width: 40rpx;
    height: 40rpx;
    .img {
      width: 100%;
      height: 100%;
    }
    .message {
      position: absolute;
      top: -8rpx;
      right: -34rpx;
      font-weight: 600;
      font-size: 20rpx;
      line-height: 28rpx;
      color: #ffffff;
      background: #e60003;
      border-radius: 18rpx;
      padding: 2rpx 14rpx;
      box-sizing: border-box;
      border: 2rpx solid #ffffff;
    }
  }
  .text {
    color: #919499;
    font-size: 20rpx;
  }
}
.mt32 {
  margin-top: 32rpx;
}
