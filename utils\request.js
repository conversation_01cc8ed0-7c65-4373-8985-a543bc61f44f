const {
  miniProgram: { envVersion },
} = wx.getAccountInfoSync()
const { getBaseCache } = require("@/utils/cache/baseCache")
const { version } = require("../config/config")
const { API_BASE_URL } = require("@/config/api")

/**
 * 封封微信的的request
 */
function request(url, data = {}, method = "POST", header = {}) {
  return new Promise(function (resolve, reject) {
    const userInfo = getApp()?.globalData?.userInfo || {}
    const { campus, examDirection, province } = getBaseCache() || {}
    let adsource = getApp()?.globalData.adsource
    let token = userInfo.token
    if (token) {
      header.Authorization = "Bearer " + token
      // header.Authorization =
      //   "Bearer " +
      //   "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.pwt15aA_FllTsDDDonUOXiRwJuspVYUIsHHwG86wvqk"
    }
    header.version = version
    header.brand = "skb"
    header.province = "chongqing"
    header.bn = "A103000133"

    if (province?.key) {
      header.province = province?.key
      // header.province = "chongqing"
    }
    if (campus?.id) {
      header["campus-id"] = campus?.id
    }
    if (examDirection?.key) {
      header["exam-direction"] = examDirection?.key
    }

    if (adsource) {
      header.adsource = adsource
    }

    // 线上打印
    if (envVersion !== "develop") {
      console.log(["请求开始：" + url.replace(API_BASE_URL, ""), data, header])
    }
    wx.request({
      url,
      data,
      method,
      header,
      success: function (res) {
        // 线上打印
        if (envVersion !== "develop") {
          console.log(["请求结束：" + url.replace(API_BASE_URL, ""), res.data])
        }
        resolve(res.data)
      },
      fail: function (err) {
        reject(err)
      },
    })
  })
}

module.exports = {
  request,
}
