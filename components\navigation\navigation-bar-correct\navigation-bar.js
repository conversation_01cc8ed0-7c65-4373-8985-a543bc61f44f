Component({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: "",
    },
    title: {
      type: String,
      value: "",
    },
    background: {
      type: String,
      value: "",
    },
    color: {
      type: String,
      value: "",
    },

    loading: {
      type: Boolean,
      value: false,
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true,
    },
    isResult: {
      // 练题报告页返回按钮
      type: Boolean,
      value: false,
    },
    isSticky: {
      type: Boolean,
      value: false,
    },
    showWhite: {
      type: Boolean,
      value: false,
    },
    // show: {
    //   // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在
    //   type: Boolean,
    //   value: true,
    //   observer: '_showChange'
    // },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1,
    },
    hasLeftSolt: {
      type: Boolean,
      value: false,
    },
    // back: {
    //   type: Boolean,
    //   value: true,
    // },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: "",
    // configImage: getApp().globalData.CONFIG,
    // IMAGE_PREFIX: getApp().globalData.CONFIG.IMAGE_PREFIX,
  },
  lifetimes: {
    attached() {
      let rect = wx.getMenuButtonBoundingClientRect()
      this.setData({
        back: getCurrentPages().length >= 1,
      })
      console.log(getCurrentPages().length, "页面栈")
      wx.getSystemInfo({
        success: (res) => {
          this.setData({
            innerPaddingRight: `padding-right: ${
              res.windowWidth - rect.left
            }px`,
            leftWidth: `width: ${res.windowWidth - rect.left}px`,
            safeAreaTop: `height: calc(var(--height) + ${
              rect.top - 4
            }px); padding-top: ${rect.top - 4}px`,
          })
        },
      })
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    _showChange(show) {
      const animated = this.data.animated
      let displayStyle = ""
      if (animated) {
        displayStyle = `opacity: ${show ? "1" : "0"};transition:opacity 0.5s;`
      } else {
        displayStyle = `display: ${show ? "" : "none"}`
      }
      this.setData({
        displayStyle,
      })
    },
    back() {
      const data = this.data
      if (getCurrentPages().length <= 1) {
        wx.reLaunch({
          url: "/pages/home/<USER>/index",
        })
        console.log("回首页")
        return false
      }
      console.log("触发返回")
      wx.navigateBack({
        delta: data.delta,
      })

      this.triggerEvent(
        "back",
        {
          delta: data.delta,
        },
        {}
      )
    },
  },
})
