// components/nav-tab/index.js
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多 slot 支持
  },
  properties: {
    tabsList: {
      type: Array,
      value: [],
    },
    tabsIndex: {
      type: String,
      value: "",
    },
    isScroll: {
      type: Boolean,
      value: false,
    },
    isTry: {
      type: Boolean,
      value: false,
    },
    isCenter: {
      type: Boolean,
      value: false,
    },
  },
  observers: {
    tabsIndex: function (key) {
      this.updateTabsItemOffset(key)
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
    tabsItemOffset: null,
    scrollLeft: 0,
    renderNumber: 0,
    boxLeft: undefined, // 初始化 boxLeft
    mrShow: false,
  },
  /**
   * 组件的方法列表
   */
  methods: {
    updateTabsItemOffset(key) {
      let _self = this
      let query = wx.createSelectorQuery().in(this)
      query.select("#" + key).boundingClientRect()
      query.selectViewport().scrollOffset()
      query.exec((res) => {
        if (res[0]) {
          _self.computeTabsItemOffset(res[0])
        }
      })
    },
    computeTabsItemOffset(itemOffset) {
      let _self = this,
        renderNumber = _self.data.renderNumber

      if (this.data.boxLeft !== undefined) {
        itemOffset.left = itemOffset.left - this.data.boxLeft
        _self.setData({
          tabsItemOffset: itemOffset,
          scrollLeft: itemOffset.left,
          renderNumber: 1,
        })
      } else {
        this.getBoxLeft((boxLeft) => {
          _self.setData(
            {
              boxLeft: boxLeft,
            },
            () => {
              _self.computeTabsItemOffset(itemOffset)
            }
          )
        })
      }
    },
    getBoxLeft(callback) {
      let query = wx.createSelectorQuery().in(this)
      query.select(".tab-nav-box").boundingClientRect()
      query.selectViewport().scrollOffset()
      query.exec((res) => {
        if (res && res[0]) {
          callback(res[0].left)
        } else {
          console.error("Failed to get boxLeft:", res)
          callback(0) // 提供一个默认值
        }
      })
    },
    // tabs nav 点击事件
    clickTabsNav: function (e) {
      let data = e.currentTarget.dataset.data
      this.triggerEvent("click", {
        data,
      })
    },
    // tab nav 对象工厂
    tabNavFactory(name, key) {
      return {
        name,
        key,
      }
    },
  },
})
