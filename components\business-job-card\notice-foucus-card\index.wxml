<view class="notice-list-item {{noticeData.statusType}} {{noticeData.hasBackground ? 'has-bg' : ''}}" bind:tap="onCardTap">
  <!-- 背景样式 -->
  <view wx:if="{{noticeData.hasBackground}}" class="bg-style" style="background: url('https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_card_bg.png'); background-size: 100% 100%;"></view>

  <!-- 选中状态 -->
  <block wx:if="{{isEdit}}">
    <image wx:if="{{noticeData.isSelected}}" class="select-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_notice_selected.png" mode="" />
    <image wx:else class="select-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_notice_select.png" mode="" />
  </block>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 标题 -->
    <view class="title">{{noticeData.title}}</view>

    <!-- 底部信息 -->
    <view class="bottom">
      <view class="flex-v">
        <!-- 状态和基本信息 -->
        <block wx:if="{{noticeData.apply_status.text}}">
          <text class="status blue" style="color:{{noticeData.apply_status.color}}">{{noticeData.apply_status.text}}</text>
          <view class="line">｜</view>
        </block>

        <!-- 招聘信息 -->
        <view class="item-text mr16" wx:if="{{noticeData.needed_num}}">共招<text>{{noticeData.needed_num}}</text>人</view>
        <view class="item-text" wx:if="{{noticeData.job_num}}"><text>{{noticeData.job_num}}</text>个职位</view>
      </view>

      <!-- 右侧信息 -->
      <view class="right-info" wx:if="{{!isEdit}}">
        <!-- 适合职位数 -->
        <view wx:if="{{noticeData.suit_job_num}}" class="item-text">
          适合职位<text class="num">{{noticeData.suit_job_num}}</text>个
        </view>

        <!-- 日期 -->
        <view wx:else class="time">
          {{noticeData.release_time}}
        </view>
      </view>
    </view>
  </view>
</view>