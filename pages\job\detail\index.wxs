/**
 * 判断数组中是否包含指定值
 * @param {Array} arr 数组
 * @param {Number} value 要查找的值
 * @returns {Boolean} 是否包含
 */
function isActive(arr, value) {
  if (!arr || arr.length === 0) {
    return false
  }

  for (var i = 0; i < arr.length; i++) {
    if (arr[i] === value) {
      return true
    }
  }
  return false
}

function getValueNum(detailData) {
  // 严格检查数据类型，确保基础数据有效
  if (
    detailData === null ||
    detailData === undefined ||
    typeof detailData !== "object"
  ) {
    return 0
  }

  // 检查job_data_record是否存在且为对象
  var jobData = detailData.job_data_record
  if (
    jobData === null ||
    jobData === undefined ||
    typeof jobData !== "object"
  ) {
    return 0
  }

  var fields = ["apply_num", "approved_num", "pay_num", "min_score"]
  var count = 0

  for (var i = 0; i < fields.length; i++) {
    var field = fields[i]
    // 先判断属性是否存在（不是undefined），再判断值是否大于0
    if (jobData[field] !== undefined && jobData[field] > 0) {
      count++
    }
  }

  return count
}

module.exports = {
  isActive: isActive,
  getValueNum: getValueNum,
}
