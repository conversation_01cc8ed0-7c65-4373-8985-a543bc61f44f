page {
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_subscribe.png);
  height: 100vh;
}
.lefts {
  padding-left: 32rpx;

  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.rights {
  .collection-img {
    width: 40rpx;
    height: 40rpx;
    margin-left: 34rpx;
  }
}

.main-content {
  flex: 1;
  min-height: 0;
  padding: 0 32rpx;
  padding-bottom: 64rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .exam-scope-card {
    padding: 40rpx 32rpx;
    background: #fff;
    border-radius: 16rpx;
    position: relative;
    margin-top: 32rpx;
    box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(34, 36, 46, 0.08);
    .bg-img {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }
    .pr-box {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0;
      position: relative;
      z-index: 2;
      .top-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          font-size: 32rpx;
          color: rgba(60, 61, 66, 1);
        }
        .add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(230, 0, 3, 0.9);
          width: 120rpx;
          height: 58rpx;
          box-sizing: border-box;
          border-radius: 12rpx;
          font-size: 24rpx;
          color: #fff;
          &.disable {
            opacity: 0.5;
          }
          .btn {
            width: 24rpx;
            height: 24rpx;
            margin-right: 4rpx;
          }
        }
      }
    }
    .scope-list {
      margin-top: 32rpx;
      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(247, 248, 250, 1);
        padding: 12rpx 32rpx;
        border-radius: 16rpx;
        margin-bottom: 16rpx;
        box-sizing: border-box;
        width: 100%;
        &:last-child {
          margin-bottom: 0;
        }
        .close-box {
          width: 64rpx;
          height: 64rpx;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .close {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }

  .fllow-card {
    background: rgba(255, 255, 255, 1);
    padding: 32rpx;
    margin-top: 32rpx;
    border-radius: 16rpx;
    box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(34, 36, 46, 0.08);
    .text {
      font-size: 28rpx;
      line-height: 42rpx;
      color: #666666;
    }
    .tip-xin-list {
      margin-top: 32rpx;
      padding-bottom: 60rpx;
      border-bottom: 1rpx solid rgba(235, 236, 240, 1);
      .tip-xin {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #3c3d42;
        margin-bottom: 32rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .img {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }
      }
    }
    .fllow-box {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 84rpx;
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);
      background: #e60003;
      color: #fff;
      border-radius: 16rpx;
      .arrow {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

.popu-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  .popu-box-top {
    padding: 0 80rpx;
    padding-top: 48rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .item-text {
      display: flex;
      align-items: center;
      .yuan {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        font-size: 28rpx;
        font-weight: 600;
        font-family: "DinBOLD";
        color: rgba(145, 148, 153, 1);
        background: rgba(247, 248, 250, 0.8);
        border-radius: 50%;
        margin-right: 16rpx;
        border: 2rpx solid rgba(235, 236, 240, 1);
      }
      .text {
        font-size: 26rpx;
        color: rgba(145, 148, 153, 1);
      }
      &.active {
        .yuan {
          background: rgba(230, 0, 3, 1);
          color: #fff;
          border-color: rgba(230, 0, 3, 1);
        }
      }
    }
    .line {
      width: 190rpx;
      height: 2rpx;
      background: rgba(235, 236, 240, 1);
    }
  }
  .popu-box-content {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    margin-top: 64rpx;
    .text-top {
      padding: 0 32rpx;
    }
    .title {
      font-size: 36rpx;
      color: rgba(60, 61, 66, 1);
    }
    .label {
      font-size: 26rpx;
      color: rgba(194, 197, 204, 1);
      margin-top: 8rpx;
    }
  }
}

.exam-list {
  // padding: 40rpx 32rpx 0 32rpx;
  flex: 1;
  min-height: 0;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;

  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 212rpx;
    height: 72rpx;
    font-size: 26rpx;
    background: rgba(235, 236, 240, 0.6);
    border-radius: 12rpx;
    // margin-right: 24rpx;
    // margin-bottom: 24rpx;
    cursor: pointer;

    // &:nth-child(3n) {
    //   margin-right: 0;
    // }

    &.active {
      color: rgba(230, 0, 3, 1);
      background-color: rgba(230, 0, 3, 0.05);
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;
  border-top: 1rpx solid rgba(235, 236, 240, 1);
}

.action-bar {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.confirm-btn {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.van-popup--bottom.van-popup--safe {
  padding-bottom: 0 !important;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}

.subscribe-overlay {
  position: relative;
  width: 622rpx;
  background: #fff;
  border-radius: 24rpx;
  .top-img {
    width: 100%;
    border-radius: 24rpx 24rpx 0 0;
    position: absolute;
    z-index: 1;
  }
  .content-box {
    box-sizing: border-box;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2;
    padding: 64rpx 48rpx 32rpx 48rpx;
    .title-top {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      color: #3c3d42;
      .yuan {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
      }
    }
  }
}

.text-top {
  padding: 40rpx 32rpx 40rpx 32rpx;
}
.title {
  font-size: 36rpx;
  color: rgba(60, 61, 66, 1);
}

.asd {
  padding: 0 32rpx;
}

.label {
  font-size: 26rpx;
  color: rgba(194, 197, 204, 1);
  margin-top: 8rpx;
}

.scope-card {
  z-index: 2;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  // padding: 40rpx 32rpx;
  background: #fff;
  border-radius: 16rpx;
  position: relative;
  margin-top: 32rpx;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(34, 36, 46, 0.08);
  .bg-img {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
  .pr-box {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    position: relative;
    z-index: 2;
    box-sizing: border-box;
    .top-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 32rpx;
        color: rgba(60, 61, 66, 1);
      }
      .add-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(230, 0, 3, 0.9);
        width: 120rpx;
        height: 58rpx;
        box-sizing: border-box;
        border-radius: 12rpx;
        font-size: 24rpx;
        color: #fff;
        &.disable {
          opacity: 0.5;
        }
        .btn {
          width: 24rpx;
          height: 24rpx;
          margin-right: 4rpx;
        }
      }
    }
  }
  .scope-list {
    margin-top: 32rpx;
    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: rgba(247, 248, 250, 1);
      padding: 24rpx 32rpx;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .close {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}

.border-bottom {
  position: relative;
  &::after {
    position: absolute;
    content: " ";
    display: block;
    height: 1rpx;
    width: 100%;
    left: 0;
    background-color: #ebecf0;
    bottom: 0;
    z-index: 222;
  }
}
