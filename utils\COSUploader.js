const COS = require("@/static/plugins/cos-wx-sdk-v5.min.js")

class COSUploader {
  /**
   * COS 上传器构造函数
   * @param {Object} config - 全局配置
   * @param {string} config.Bucket - 存储桶名称
   * @param {string} config.Region - 存储桶地域
   * @param {string} config.Directory - 存储桶目录
   * @param {number} [config.SliceSize=10485760] - 分块大小（默认10MB）
   * @param {Function} config.requestMethod - 密钥请求方法
   */
  constructor({
    Bucket,
    Region,
    Directory,
    SliceSize = 1024 * 1024 * 10,
    requestMethod,
  }) {
    this.cosConfig = {
      Bucket,
      Region,
      Directory,
      SliceSize,
      SimpleUploadMethod: "putObject",
    }

    this.requestMethod = requestMethod
    this.cosInstance = this.initCOSInstance()
  }

  /**
   * 初始化 COS 实例
   * @private
   */
  initCOSInstance() {
    return new COS({
      SimpleUploadMethod: this.cosConfig.SimpleUploadMethod,
      getAuthorization: this.getAuthorization.bind(this),
    })
  }

  /**
   * 获取临时密钥（可扩展为自动刷新机制）
   * @private
   */
  async getAuthorization(options, callback) {
    try {
      const { data } = await this.requestMethod()
      if (!data?.credentials) {
        throw new Error("无效的凭证响应")
      }
      callback({
        TmpSecretId: data.credentials.tmpSecretId,
        TmpSecretKey: data.credentials.tmpSecretKey,
        SecurityToken: data.credentials.sessionToken,
        StartTime: data.startTime,
        ExpiredTime: data.expiredTime,
      })
    } catch (error) {
      console.error("获取COS授权失败:", error)
      callback({ error }) // 传递错误给COS SDK
    }
  }

  /**
   * 生成文件配置
   * @private
   */
  generateFileConfig(file) {
    if (!file?.tempFilePath) {
      throw new Error("无效的文件对象")
    }

    return {
      FilePath: file.tempFilePath,
      FileSize: file.size,
      Bucket: this.cosConfig.Bucket,
      Region: this.cosConfig.Region,
      Key: `${this.cosConfig.Directory}${this.extractFileNameAndExtension(
        file.tempFilePath
      )}`,
      onTaskReady: (taskId) => this.handleTaskReady(taskId, file),
    }
  }

  /**
   * 任务准备回调（可扩展任务管理功能）
   * @private
   */
  handleTaskReady(taskId, file) {
    console.log(`文件 ${file.name} 任务准备就绪`, taskId)
  }

  /**
   * 执行文件上传
   * @param {Array} files - 要上传的文件列表
   * @param {Object} [eventHandlers] - 事件处理器
   * @param {Function} [eventHandlers.onProgress] - 上传进度回调
   * @param {Function} [eventHandlers.onFileFinish] - 单个文件完成回调
   */
  async upload(files, eventHandlers = {}) {
    try {
      const fileList = files.map((file) => {
        return Object.assign({}, file, this.generateFileConfig(file))
      })

      return await new Promise((resolve, reject) => {
        // 统一错误处理函数
        const handleError = (error) => {
          console.error("上传过程出错:", error)
          reject(error)
        }

        try {
          this.cosInstance.uploadFiles(
            {
              files: fileList,
              SliceSize: this.cosConfig.SliceSize,
              onProgress: this.createProgressHandler(
                eventHandlers.onProgress,
                handleError
              ),
              onFileFinish: this.createFileFinishHandler(
                eventHandlers.onFileFinish,
                resolve,
                reject
              ),
            },
            (err, data) => this.handleUploadComplete(err, data, resolve, reject)
          )
        } catch (error) {
          handleError(error)
        }
      })
    } catch (error) {
      console.error("上传初始化失败:", error)
      return Promise.reject(error)
    }
  }

  /**
   * 创建进度处理器（强化错误处理）
   * @private
   */
  createProgressHandler(customHandler, globalErrorHandler) {
    return (info) => {
      try {
        const percent = parseInt(info.percent * 10000) / 100
        const speed = parseInt((info.speed / 1024 / 1024) * 100) / 100

        if (typeof customHandler === "function") {
          customHandler({ percent, speed })
        } else {
          console.log(`上传进度：${percent}%; 上传速度：${speed}Mb/s;`)
        }
      } catch (error) {
        console.error("进度处理出错:", error)
        globalErrorHandler(error)
      }
    }
  }

  /**
   * 创建文件完成处理器（强化错误处理）
   * @private
   */
  createFileFinishHandler(customHandler, resolve, reject) {
    return (err, data, options) => {
      try {
        console.log(`文件 ${options.Key} ${err ? "上传失败" : "上传成功"}`)

        if (typeof customHandler === "function") {
          customHandler(err, data, options)
        }

        // 优先处理SDK返回的错误
        if (err) {
          reject(err)
        } else {
          resolve(data)
        }
      } catch (error) {
        console.error("文件完成处理出错:", error)
        reject(error)
      }
    }
  }

  /**
   * 处理上传完成回调（强化错误处理）
   * @private
   */
  handleUploadComplete(err, data, resolve, reject) {
    try {
      if (err) {
        console.error("上传失败:", err)
        reject(err)
      } else {
        console.log("上传完成:", data)
        resolve(data)
      }
    } catch (error) {
      console.error("上传完成处理出错:", error)
      reject(error)
    }
  }

  /**
   * 获取文件名称及后缀（增加校验）
   * @private
   */
  extractFileNameAndExtension(filePath) {
    if (typeof filePath !== "string") {
      throw new Error("无效的文件路径类型")
    }

    const segments = filePath.split("/")
    const fileNameWithExtension = segments[segments.length - 1]

    const lastDotIndex = fileNameWithExtension.lastIndexOf(".")
    if (lastDotIndex !== -1) {
      const fileName = fileNameWithExtension.slice(0, lastDotIndex)
      const fileExtension = fileNameWithExtension.slice(lastDotIndex + 1)
      const fullName = `${fileName}.${fileExtension}`
      return fullName
    } else {
      return fileNameWithExtension
    }
  }
}

module.exports = COSUploader
