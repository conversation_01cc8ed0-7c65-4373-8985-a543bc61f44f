const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: String,
    isCollapse: <PERSON>olean,
    isHaveVideo: <PERSON>olean,
    isHaveRight: String,
    isNeedRightAngle: Boolean, // 是否不需要圆角
    activeIndex: {
      type: Number,
      value: 999,
      observer: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.calculateHeight() // 调用计算高度的方法
        }
      },
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isOpen: false,
    isOverFlow: false,
    isGetInfo: false,
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  lifetimes: {
    ready() {
      this.calculateHeight()
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    changeCollapse() {
      this.setData({
        isOpen: !this.data.isOpen,
      })
    },
    clickEvent() {
      this.triggerEvent("clickEvent")
    },
    calculateHeight() {
      if (this.data.isCollapse) {
        if (this.data.isGetInfo) {
          return
        }
        const query = wx.createSelectorQuery().in(this)
        // 使用 `selectAll` 方法选择所有具有特定类名的元素
        query
          .select(".report-card-content")
          .boundingClientRect((rect) => {
            if (rect && rect.height) {
              const height = rect.height
              let isOverFlow = false
              if (this.data.isHaveVideo) {
                isOverFlow = height > 290
              } else {
                isOverFlow = height > 220
              }
              this.setData({
                isOverFlow,
                isGetInfo: true,
              })
            }
          })
          .exec() // 执行查询
      }
    },
  },
})
