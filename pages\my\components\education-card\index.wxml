<view class="education-card">
  <view class="title-area">
    <view class="left">
      <view class="title">教育经历</view>
      <view class="tips" wx:if="{{ incompleteTips }}">{{ incompleteTips }}</view>
    </view>
    <view class="right" bindtap="onNavigateToAddEducation">
      <image class="add-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_add.png" mode="" />
      <view class="add-text">新增</view>
    </view>
  </view>

  <!-- 无数据时的占位显示 -->
  <view class="form-list-item placeholder" wx:if="{{ educationData.length === 0 }}" bindtap="onNavigateToAddEducation">
    <view class="form-item mt0">
      <view class="left">
        学历
        <text class="cred">*</text>
      </view>
      <view class="right">
        <view class="text">待完善</view>
        <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="form-item">
      <view class="left">
        专业
        <text class="cred">*</text>
      </view>
      <view class="right">
        <view class="text">待完善</view>
        <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="form-item">
      <view class="left">
        学位
      </view>
      <view class="right">
        <view class="text">待完善</view>
        <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
  </view>

  <!-- 单条数据时的显示 -->
  <view class="form-list-item single" wx:elif="{{ educationData.length === 1 }}" bindtap="onNavigateToAddEducation" data-id="{{educationData[0].id}}">
    <view class="form-item mt0">
      <view class="degree-text">
        {{educationData[0].education_text}}
        <text class="cred">*</text>
      </view>
      <view class="right">
        <image class="arrow-icon" data-index="0" data-id="{{educationData[0].id}}" catchtap="onDeleteEducation" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_delete.png" mode="" />
      </view>
    </view>
    <view class="form-item">
      <view class="left">
        专业
        <text class="cred">*</text>
      </view>
      <view class="right">
        <view class="text text-ellipsis-1 black-text major-ellipsis">{{ educationData[0].major_text || '待完善' }}</view>
        <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="form-item">
      <view class="left">
        学位
      </view>
      <view class="right">
        <view class="text black-text">{{ educationData[0].degree_text || '无学位' }}</view>
        <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="check-area" catch:tap="checkCard" data-index="0" data-id="{{ educationData[0].id }}">
      <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/education_{{educationData[0].is_default == 1?'selected':'select'}}.png" mode="" />
      <view class="text {{educationData[0].is_default == 1?'active':''}}">以此学历匹配</view>
    </view>
  </view>

  <!-- 多条数据时的scroll-view显示 -->
  <view class="scroll-container" wx:elif="{{ educationData.length > 1 }}">
    <scroll-view class="education-scroll" scroll-x="{{ true }}" show-scrollbar="{{ false }}" bindscroll="onScroll">
      <view class="scroll-content">
        <view class="form-list-item scroll-item" wx:for="{{ educationData }}" wx:key="index" bindtap="onNavigateToAddEducation" data-id="{{item.id}}">
          <view class="form-item mt0">
            <view class="degree-text">
              {{item.education_text}}
              <text class="cred">*</text>
            </view>
            <view class="right">
              <image class="arrow-icon" data-index="{{index}}" data-id="{{item.id}}" catchtap="onDeleteEducation" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_delete.png" mode="" />
            </view>
          </view>
          <view class="form-item">
            <view class="left">
              专业
              <text class="cred">*</text>
            </view>
            <view class="right">
              <view class="text text-ellipsis-1 black-text major-ellipsis">{{ item.major_text || '待完善' }}</view>
              <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
            </view>
          </view>
          <view class="form-item">
            <view class="left">
              学位
            </view>
            <view class="right">
              <view class="text black-text">{{ item.degree_text || '无学位' }}</view>
              <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
            </view>
          </view>
          <view class="check-area" catch:tap="checkCard" data-index="{{index}}" data-id="{{item.id}}">
            <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/education_{{item.is_default == 1?'selected':'select'}}.png" mode="" />
            <view class="text {{item.is_default == 1?'active':''}}">以此学历匹配</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 自定义指示器 -->
    <view class="custom-indicators">
      <view class="indicator-dot {{ currentIndex === index ? 'active' : '' }}" wx:for="{{ educationData }}" wx:key="index"></view>
    </view>
  </view>

  <view class="form-item mt40 w100" bindtap="onEditGraduateStatus">
    <view class="left">
      是否为应届生身份
    </view>
    <view class="right">
      <view class="text {{graduateStatus ? 'has-value' : ''}}">{{ graduateText || '待完善' }}</view>
      <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
    </view>
  </view>

  <!-- 应届生身份选择器 -->
  <van-popup show="{{ graduateShow }}" round position="bottom" z-index="9999" bind:close="onGraduateClose">
    <van-picker id="graduatePicker" show-toolbar title="是否为应届生身份" columns="{{ graduateColumns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onGraduateClose" bind:confirm="onGraduateConfirm" />
  </van-popup>

  <!-- 自定义弹窗 -->
  <view class="custom-dialog-overlay" wx:if="{{showCustomDialog}}" catchtouchmove="preventTouchMove">
    <view class="custom-dialog-box">
      <view class="custom-dialog-title">提示</view>
      <view class="custom-dialog-content">参与匹配的学历不可删除</view>
      <view class="custom-dialog-footer" bindtap="onCustomDialogConfirm">
        <view class="custom-dialog-btn">我知道了</view>
      </view>
    </view>
  </view>
</view>