<view class="{{isFixed?'status-box':'status-no'}} {{show_white && !show_bg ? 'bgf' : ''}}" style="{{show_white ? (show_bg ? 'background-color: ' + show_bg : '') : (defaultBgColor ? 'background-color: ' + defaultBgColor : '')}}">
  <navigation-status-bar></navigation-status-bar>
  <view class="container top-header-nav">
    <view class="header-item-left">
      <slot name="left"></slot>
    </view>
    <view class="header-item-center text-ellipsis-1" wx:if="{{title}}">
      {{title}}
    </view>
    <view class="header-item-right" style="padding-right: {{searchButtonPaddingRight}};">
      <slot name="right"></slot>
    </view>
  </view>
</view>
<!-- <navigation-status-bar></navigation-status-bar>
  <view style="top:{{navigationBarHeight}}px;">
    <view class="tab-list">
      <view class="tab-list-item {{index == activeIndex?'active':''}}" bindtap="changeTab" data-index="{{index}}" wx:for="{{tabList}}" wx:key="index">{{item}}</view>
    </view>
  </view> -->