const ROUTER = require("@/services/mpRouter")
Component({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    extClass: {
      type: String,
      value: "",
    },
    title: {
      type: String,
      value: "",
    },
    background: {
      type: String,
      value: "",
    },
    color: {
      type: String,
      value: "",
    },
    back: {
      type: Boolean,
      value: true,
    },
    loading: {
      type: Boolean,
      value: false,
    },
    homeButton: {
      type: Boolean,
      value: false,
    },
    animated: {
      // 显示隐藏的时候opacity动画效果
      type: Boolean,
      value: true,
    },
    isResult: {
      // 练题报告页返回按钮
      type: Boolean,
      value: false,
    },
    // show: {
    //   // 显示隐藏导航，隐藏的时候navigation-bar的高度占位还在
    //   type: Boolean,
    //   value: true,
    //   observer: '_showChange'
    // },
    // back为true的时候，返回的页面深度
    delta: {
      type: Number,
      value: 1,
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    displayStyle: "",
    configImage: getApp().globalData.CONFIG,
    IMAGE_PREFIX: getApp().globalData.CONFIG.IMAGE_PREFIX,
  },
  lifetimes: {
    attached() {
      let rect = wx.getMenuButtonBoundingClientRect()

      wx.getSystemInfo({
        success: (res) => {
          this.setData({
            innerPaddingRight: `padding-right: ${
              res.windowWidth - rect.left
            }px`,
            leftWidth: `width: ${res.windowWidth - rect.left}px`,
            safeAreaTop: `height: calc(var(--height) + ${
              rect.top - 4
            }px); padding-top: ${rect.top - 4}px`,
          })
        },
      })
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    _showChange(show) {
      const animated = this.data.animated
      let displayStyle = ""
      if (animated) {
        displayStyle = `opacity: ${show ? "1" : "0"};transition:opacity 0.5s;`
      } else {
        displayStyle = `display: ${show ? "" : "none"}`
      }
      this.setData({
        displayStyle,
      })
    },
    back() {
      const data = this.data
      if (getCurrentPages().length === 1) {
        ROUTER.reLaunch({
          path: "/pages/home/<USER>/index",
        })
        return false
      }
      if (data.delta) {
        wx.navigateBack({
          delta: data.delta,
        })
      }

      this.triggerEvent(
        "back",
        {
          delta: data.delta,
        },
        {}
      )
    },
  },
})
