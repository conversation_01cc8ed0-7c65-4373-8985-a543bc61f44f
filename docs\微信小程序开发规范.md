# 微信小程序通用开发规范

> 简洁明了的小程序项目结构和命名规范，适用于所有小程序项目

## 📋 目录
- [项目结构规范](#项目结构规范)
- [命名规范](#命名规范)
- [使用示例](#使用示例)

---

## 🏗️ 项目结构规范

### 标准目录结构

```
miniprogram-project/
├── app.js                    # 小程序入口文件
├── app.json                  # 小程序全局配置
├── app.wxss                  # 小程序全局样式
├── components/              # 通用组件目录
├── pages/                   # 页面目录
├── services/                # 业务逻辑服务
├── static/                  # 静态资源目录
├── utils/                   # 工具函数目录
├── config/                  # 配置文件目录
└── docs/                    # 项目文档目录
```

### 🧩 组件目录结构

#### 项目通用组件
**路径：** `/components/组件类型/组件类型-功能/index`
**命名：** `组件类型-功能`

```
components/
├── popup/                   # 弹窗类组件
│   ├── popup-share/index        # 分享弹窗
│   ├── popup-confirm/index      # 确认弹窗
│   └── popup-picker/index       # 选择器弹窗
├── modal/                   # 模态框组件
│   ├── modal-loading/index      # 加载模态框
│   └── modal-alert/index        # 提示模态框
├── navigation/              # 导航组件
│   ├── navigation-bar/index     # 导航栏
│   └── navigation-tabs/index    # 标签导航
└── feedback/                # 反馈组件
    ├── feedback-empty/index     # 空状态组件
    └── feedback-loading/index   # 加载组件
```

#### 业务通用组件
**路径：** `/components/business-业务模块/业务模块-功能/index`
**命名：** `业务模块-功能`

```
components/
├── business-user/           # 用户业务组件
│   ├── user-card/index          # 用户卡片
│   └── user-avatar/index        # 用户头像
├── business-course/         # 课程业务组件
│   ├── course-card/index        # 课程卡片
│   └── course-list/index        # 课程列表
└── business-common/         # 通用业务组件
    ├── common-header/index      # 通用头部
    └── common-share/index       # 通用分享
```

#### 模块专用组件
**路径：** `pages/大模块/components/`

```
pages/
├── home/
│   ├── components/         # 首页模块专用组件
│   │   ├── home-banner/index    # 首页轮播
│   │   └── home-menu/index      # 首页菜单
│   ├── home/index               # 首页主页
│   └── home-detail/index        # 首页详情
```


### 📄 页面目录结构

**结构层级：** `pages/大模块/子模块-页面/index`

```
pages/
├── home/                    # 首页模块
│   ├── home/index               # 首页主页
│   ├── home-detail/index        # 首页详情
│   └── home-search/index        # 首页搜索
├── course/                  # 课程模块
│   ├── course-list/index        # 课程列表
│   ├── course-detail/index      # 课程详情
│   └── course-category/index    # 课程分类
├── user/                    # 用户模块
│   ├── user-profile/index       # 个人资料
│   ├── user-settings/index      # 用户设置
│   └── user-login/index         # 登录页面
└── common/                  # 通用页面
    ├── common-webview/index     # 网页容器
    └── common-error/index       # 错误页面
```

### 📁 其他核心目录

#### services/ - 业务逻辑服务
```
services/
├── module/                  # 业务模块服务
├── cmdManager.js           # 命令管理器
└── mpRouter.js             # 小程序路由管理
```

#### static/ - 静态资源文件
```
static/
├── images/                  # 图片资源
├── fonts/                   # 字体文件
├── js/                      # 第三方JS库
└── plugins/                 # 插件文件
```

#### utils/ - 工具函数
```
utils/
├── cache/                   # 缓存相关操作
├── wxs/                     # WXS文件
├── request.js              # 网络请求工具
├── util.js                 # 通用工具函数
└── file.js                 # 文件操作工具
```

#### config/ - 配置文件
```
config/
├── api.js                   # API配置
├── config.js               # 应用配置
└── webviewSupportDomain.js # 网页域名配置
```

---

## 📝 命名规范

### 基本原则
- **统一使用短横线连接（kebab-case）**
- **小写字母开头**
- **语义化命名，见名知意**

### 命名对比
```
✅ 正确命名                    ❌ 错误命名
job-list/                    JobList/
user-profile/                userProfile/
popup-share/                 popupShare/
course-detail/               courseDetail/
```

### 文件命名规则

#### 页面文件（固定后缀）
```
页面目录/
├── index.wxml              # 页面结构
├── index.wxss              # 页面样式
├── index.js                # 页面逻辑
└── index.json              # 页面配置
```

#### 组件文件（固定后缀）
```
组件目录/
├── index.wxml              # 组件结构
├── index.wxss              # 组件样式
├── index.js                # 组件逻辑
└── index.json              # 组件配置
```

#### 服务和工具文件
- **服务文件：** `userService.js`、`jobService.js`、`mpRouter.js`
- **工具文件：** `request.js`、`util.js`、`file.js`
- **JS封装：** 内部方法和变量使用小驼峰命名（camelCase）

### 路径命名规则

#### 页面路径
- **格式：** `pages/大模块/子模块-页面/index`
- **示例：**
  - `pages/home/<USER>/index` - 首页主页
  - `pages/course/course-detail/index` - 课程详情页
  - `pages/user/user-profile/index` - 用户资料页

#### 组件路径
- **项目通用组件：** `/components/组件类型/组件类型-功能/index`
- **业务通用组件：** `/components/business-业务模块/业务模块-功能/index`

---

## 💡 使用示例

### API调用示例
```javascript
// 页面中使用API
const API = require("@/config/api")
const UTIL = require("@/utils/util")

Page({
  onLoad() {
    // 调用API获取数据
    UTIL.request(API.getQualificationList)
      .then((res) => {
        this.setData({
          dataList: res.data || []
        })
      })
      .catch((error) => {
        console.error('请求失败:', error)
      })
  }
})
```

### 缓存使用示例
```javascript
// 页面中使用缓存
const BASE_CACHE = require("@/utils/cache/baseCache")

Page({
  onLoad() {
    const baseCache = BASE_CACHE.getBaseCache()

    // 获取缓存
    const cachedData = baseCache.getCache('userInfo')
    if (cachedData) {
      this.setData({ userInfo: cachedData })
      return
    }

    // 设置缓存（有效期1小时）
    baseCache.setCache('userInfo', userData, 3600)
  }
})
```

### 路由跳转使用示例
```javascript
// 页面中使用路由跳转
const mpRouter = require("@/services/mpRouter")

Page({
  // 跳转到职位详情页
  goToJobDetail(e) {
    const jobId = e.currentTarget.dataset.jobId
    mpRouter.navigateTo('/pages/job/job-detail/index', {
      jobId: jobId
    })
  },

  // 跳转到用户资料页
  goToUserProfile() {
    mpRouter.navigateTo('/pages/user/user-profile/index')
  },

  // 返回上一页
  goBack() {
    mpRouter.navigateBack()
  }
})
```

**注意：** JS文件内部的方法和变量使用小驼峰命名（camelCase）
```javascript
// mpRouter.js 内部实现示例
class MpRouter {
  navigateTo(url, params = {}) {
    // 内部方法使用小驼峰命名
    const fullUrl = this.buildUrlWithParams(url, params)
    wx.navigateTo({ url: fullUrl })
  }

  buildUrlWithParams(url, params) {
    // 私有方法也使用小驼峰命名
    if (Object.keys(params).length === 0) return url
    const queryString = Object.keys(params)
      .map(key => `${key}=${params[key]}`)
      .join('&')
    return `${url}?${queryString}`
  }
}
```

### 组件使用示例
```javascript
// 页面配置文件 index.json
{
  "usingComponents": {
    "job-card": "/components/business-job/job-card/index",
    "popup-share": "/components/popup/popup-share/index"
  }
}
```

---

## 📋 核心原则

- **kebab-case命名法** - 所有文件夹和文件名使用短横线连接
- **三层页面结构** - `pages/大模块/子模块-页面/index`
- **组件分类管理** - 项目通用组件、业务通用组件、模块专用组件
- **业务逻辑分离** - 服务层、工具层、缓存层分离
- **语义化命名** - 见名知意，提高代码可读性

---

## ✅ 总结

本规范文档专注于**项目结构**和**命名规范**，确保：

1. **目录结构清晰** - 便于项目管理和团队协作
2. **命名规范统一** - 提高代码可读性和维护性
3. **组件分类合理** - 确保组件的复用性和一致性
4. **使用方式简单** - 符合实际开发习惯

遵循这些规范将有助于团队快速上手并保持代码结构的一致性。



