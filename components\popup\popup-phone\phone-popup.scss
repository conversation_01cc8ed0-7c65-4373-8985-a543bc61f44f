.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.phone-box {
  position: relative;
  background: #fff;
  width: 574rpx;
  padding: 48rpx 40rpx 40rpx 40rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  .user-img {
    width: 120rpx;
    height: 120rpx;
    border: 6rpx solid #fff;
  }

  .title {
    font-size: 32rpx;
    color: #131515;
    font-weight: 500;
  }

  .phone-area {
    width: 100%;
    margin-top: 40rpx;

    .phone-list {
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx;
      background: #F9F9F9;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      border: 1rpx solid transparent;

      .left {
        font-size: 24rpx;
        font-weight: 400;
        color: #666666;

        .text {
          font-weight: 500;
          font-size: 36rpx;
          color: #3C3D42;
          font-family: "DINBold";
        }
      }

      .icon-area {
        width: 32rpx;
        height: 32rpx;

        .check-icon {
          width: 100%;
          height: 100%;
        }

        .none {
          display: none;
        }
      }
    }

    .check {
      background: rgba(214, 40, 40, 0.05);
      border: 2rpx solid rgba(214, 40, 40, 0.5);

      .left {
        color: #D62828;

        .text {
          color: #D62828;
        }
      }
    }
  }

  .btn {
    margin-top: 16rpx;
    width: 100%;
    height: 84rpx;
    background: #EC3E33;
    border-radius: 16rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-item {
    width: 64rpx;
    height: 64rpx;
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    left: 50%;
    bottom: -84rpx;
    transform: translateX(-50%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .close-img {
      width: 20rpx;
      height: 20rpx;
    }
  }
}