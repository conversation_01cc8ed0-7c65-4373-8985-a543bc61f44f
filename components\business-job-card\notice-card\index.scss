.notice-list {
  &-item {
    background: #ffffff;
    padding: 32rpx;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    position: relative;

    &.over {
      .title {
        color: rgba(145, 148, 153, 1);
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      line-height: 48rpx;
      font-weight: 600;
      .is-top {
        width: 52rpx;
        height: 28rpx;
        margin-right: 10rpx;
        transform: translateY(2rpx);
      }
    }
    .bottom {
      display: flex;
      align-items: center;
      margin-top: 40rpx;
      .status {
        font-size: 24rpx;
        color: rgba(19, 191, 128, 0.8);
        &.blue {
          color: rgba(68, 138, 255, 0.8);
        }
        &.over {
          color: rgba(255, 106, 77, 0.8);
        }
        &.end {
          color: rgba(145, 148, 153, 1);
        }
      }
      .line {
        font-size: 20rpx;
        color: rgba(194, 197, 204, 1);
        margin: 0 8rpx;
      }
      .item-text {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 1);
        .num {
          color: rgba(60, 61, 66, 1);
          margin: 0 4rpx;
          // font-weight: 600;
          font-family: "DinBold";
        }
      }
      .mr16 {
        margin-right: 16rpx;
      }
      .flex-v {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
      }
    }
    .time {
      font-size: 24rpx;
      color: rgba(194, 197, 204, 1);
    }
  }
}
