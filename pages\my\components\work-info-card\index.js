const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    workInfo: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    formList: [
      {
        title: "服务基层项目",
        field: "service",
        required: false,
        text: "",
        value: "",
      },
      {
        title: "基层工作经验（含私企工作经历）",
        field: "experience",
        required: false,
        text: "",
        value: "",
      },
    ],

    // 服务基层项目选择器
    serviceShow: false,
    serviceColumns: [],
    serviceSelectedIndex: 0,

    // 基层工作经验选择器
    experienceShow: false,
    experienceColumns: [],
    experienceSelectedIndex: 0,

    // 待完善提示
    incompleteTips: "2项待完善",
  },

  /**
   * 生命周期
   */
  lifetimes: {
    attached() {
      let tryCount = 0
      const tryInit = () => {
        const serverConfig = APP.globalData.serverConfig
        if (serverConfig) {
          // 适配数据结构，确保为[{text, value}]
          let serviceData = serverConfig?.service_project_list || []
          let experienceData = serverConfig?.work_years_list || []
          serviceData = serviceData.map((item) => {
            if (typeof item === "string") return { text: item, value: item }
            if (item.text && item.value) return item
            if (item.name && item.id) return { text: item.name, value: item.id }
            if (item.label && item.value)
              return { text: item.label, value: item.value }
            const keys = Object.keys(item || {})
            let text = ""
            let value = ""
            keys.forEach((k) => {
              if (!text && typeof item[k] === "string") text = item[k]
              if (!value && typeof item[k] !== "object") value = item[k]
            })
            return { text, value }
          })
          experienceData = experienceData.map((item) => {
            if (typeof item === "string") return { text: item, value: item }
            if (item.text && item.value) return item
            if (item.name && item.id) return { text: item.name, value: item.id }
            if (item.label && item.value)
              return { text: item.label, value: item.value }
            const keys = Object.keys(item || {})
            let text = ""
            let value = ""
            keys.forEach((k) => {
              if (!text && typeof item[k] === "string") text = item[k]
              if (!value && typeof item[k] !== "object") value = item[k]
            })
            return { text, value }
          })
          this.setData({
            serviceColumns: [{ values: serviceData }],
            experienceColumns: [{ values: experienceData }],
          })
        } else if (tryCount < 10) {
          tryCount++
          setTimeout(tryInit, 200)
        }
      }
      tryInit()
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化选择器数据
     */
    initSelectors() {
      // 支持异步全局配置
      const setSelectors = (serverConfig) => {
        let serviceData = serverConfig?.service_project_list || []
        let experienceData = serverConfig?.work_years_list || []
        console.log("原始service_project_list:", serviceData)
        console.log("原始work_years_list:", experienceData)
        // 动态适配serviceData
        serviceData = serviceData.map((item) => {
          if (typeof item === "string") return { text: item, value: item }
          if (item.text && item.value) return item
          if (item.name && item.id) return { text: item.name, value: item.id }
          if (item.label && item.value)
            return { text: item.label, value: item.value }
          // 兜底：取第一个字符串字段做text，第一个非字符串字段做value
          const keys = Object.keys(item || {})
          let text = ""
          let value = ""
          keys.forEach((k) => {
            if (!text && typeof item[k] === "string") text = item[k]
            if (!value && typeof item[k] !== "object") value = item[k]
          })
          return { text, value }
        })
        // 动态适配experienceData
        experienceData = experienceData.map((item) => {
          if (typeof item === "string") return { text: item, value: item }
          if (item.text && item.value) return item
          if (item.name && item.id) return { text: item.name, value: item.id }
          if (item.label && item.value)
            return { text: item.label, value: item.value }
          const keys = Object.keys(item || {})
          let text = ""
          let value = ""
          keys.forEach((k) => {
            if (!text && typeof item[k] === "string") text = item[k]
            if (!value && typeof item[k] !== "object") value = item[k]
          })
          return { text, value }
        })
        console.log("适配后serviceData:", serviceData)
        console.log("适配后experienceData:", experienceData)
        this.setData({
          serviceColumns: [{ values: serviceData }],
          experienceColumns: [{ values: experienceData }],
        })
      }
      if (APP.globalData.serverConfig) {
        setSelectors(APP.globalData.serverConfig)
      } else {
        // 注册回调，等全局配置到位后再初始化
        APP.globalData.serverConfigReadyCallback = (serverConfig) => {
          setSelectors(serverConfig)
        }
      }
    },

    /**
     * 统计本卡片待完善项数
     */
    countIncomplete() {
      const formList = this.data.formList
      let count = 0
      formList.forEach((item) => {
        if (
          item.value === undefined ||
          item.value === null ||
          (typeof item.value === "string" && item.value.trim().length < 1)
        ) {
          count++
        }
      })
      this.setData({
        incompleteTips: count > 0 ? `${count}项待完善` : "已完善",
      })
      this.triggerEvent("incompleteCount", { count })
    },

    /**
     * 表单项点击事件
     */
    onFormItemTap(e) {
      console.log("work-info-card 点击事件触发", e.currentTarget.dataset)

      const { field } = e.currentTarget.dataset
      const formList = this.data.formList
      const fieldData = formList.find((item) => item.field === field)

      console.log("点击的字段:", field, "字段数据:", fieldData)

      if (field === "service") {
        console.log("打开服务基层项目选择器")
        this.openServiceSelector(fieldData)
      } else if (field === "experience") {
        console.log("打开基层工作经验选择器")
        this.openExperienceSelector(fieldData)
      }
    },

    /**
     * 打开服务基层项目选择器
     */
    openServiceSelector(fieldData) {
      // 设置回显索引
      let selectedIndex = 0
      if (
        fieldData &&
        fieldData.value &&
        this.data.serviceColumns.length > 0 &&
        this.data.serviceColumns[0].values.length > 0
      ) {
        const serviceData = this.data.serviceColumns[0].values
        const index = serviceData.findIndex(
          (item) => item.value == fieldData.value
        )
        if (index >= 0) {
          selectedIndex = index
        }
      }
      this.setData({
        serviceSelectedIndex: selectedIndex,
        serviceShow: true,
      })
      // 设置picker的默认索引（有数据时才设置）
      setTimeout(() => {
        if (
          this.data.serviceColumns.length > 0 &&
          this.data.serviceColumns[0].values.length > 0
        ) {
          const picker = this.selectComponent("#servicePicker")
          if (picker && picker.setIndexes) {
            picker.setIndexes([selectedIndex])
          }
        }
      }, 50)
    },

    /**
     * 关闭服务基层项目选择器
     */
    onServiceClose() {
      this.setData({
        serviceShow: false,
      })
    },

    /**
     * 确认服务基层项目选择
     */
    onServiceConfirm(e) {
      const { value } = e.detail
      const text = value[0]?.text
      const data = value[0]?.value
      this.setData({
        "formList[0].value": data,
        "formList[0].text": text,
        serviceShow: false,
      })
      this.triggerEvent("dataChange", { formList: this.data.formList })
      this.countIncomplete()
    },

    /**
     * 打开基层工作经验选择器
     */
    openExperienceSelector(fieldData) {
      // 设置回显索引
      let selectedIndex = 0
      if (
        fieldData &&
        fieldData.value &&
        this.data.experienceColumns.length > 0 &&
        this.data.experienceColumns[0].values.length > 0
      ) {
        const experienceData = this.data.experienceColumns[0].values
        const index = experienceData.findIndex(
          (item) => item.value == fieldData.value
        )
        if (index >= 0) {
          selectedIndex = index
        }
      }
      this.setData({
        experienceSelectedIndex: selectedIndex,
        experienceShow: true,
      })
      // 设置picker的默认索引（有数据时才设置）
      setTimeout(() => {
        if (
          this.data.experienceColumns.length > 0 &&
          this.data.experienceColumns[0].values.length > 0
        ) {
          const picker = this.selectComponent("#experiencePicker")
          if (picker && picker.setIndexes) {
            picker.setIndexes([selectedIndex])
          }
        }
      }, 50)
    },

    /**
     * 关闭基层工作经验选择器
     */
    onExperienceClose() {
      this.setData({
        experienceShow: false,
      })
    },

    /**
     * 确认基层工作经验选择
     */
    onExperienceConfirm(e) {
      const { value } = e.detail
      const text = value[0]?.text
      const data = value[0].value
      this.setData({
        "formList[1].value": data,
        "formList[1].text": text,
        experienceShow: false,
      })
      this.triggerEvent("dataChange", { formList: this.data.formList })
      this.countIncomplete()
    },
  },
  observers: {
    workInfo(newVal) {
      if (!newVal) return
      console.log("observers workInfo", newVal)
      this.setData({
        "formList[0].text": newVal.service_project?.text || "",
        "formList[0].value": newVal.service_project?.value,
        "formList[1].text": newVal.work_years?.text || "",
        "formList[1].value": newVal.work_years?.value,
      })
      this.countIncomplete()
    },
  },
})
