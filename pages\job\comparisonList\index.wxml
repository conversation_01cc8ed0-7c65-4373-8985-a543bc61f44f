<wxs src="/utils/wxs/jobUtils.wxs" module="jobUtils" />

<view class="comparison-list">

  <view class="top-card" bindtap="onNavigateToAddJob">
    <image class="icon" src="{{IMAGE_PREFIX}}/job/comparison/add.png" mode="" />
    <view class="text">从关注职位中添加</view>
  </view>

  <view class="card-area" wx:if="{{jobList.length}}">
    <comparison-card wx:for="{{jobList}}" wx:key="id" dataItem="{{item}}" bind:select="onCardSelect" bind:delete="onCardDelete">
    </comparison-card>
  </view>
  <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/foot_no_data.png" text="暂未添加对比职位"></empty-default>

  <!-- 底部操作区域 -->
  <tabbar-box wx:if="{{jobList.length}}">
    <view class="bottom-area">
      <view class="left" bindtap="onToggleSelectAll">
        <image class="icon" src="{{IMAGE_PREFIX}}/job/comparison/{{isAllSelected ? 'selected' : 'select'}}.png" mode="" />
        <view class="text">{{isAllSelected ? '取消全选' : '全选'}}</view>
      </view>
      <view class="btn {{jobUtils.hasSelected(jobList) ? 'active' : ''}}" bindtap="onCompare">
        {{jobUtils.getFullCompareButtonText(jobList)}}
      </view>
    </view>
  </tabbar-box>
</view>
<modal-default show="{{modalShow}}" title="是否要移除该职位" bind:confirm="confirmDelete" bind:cancel="cancelDelete"></modal-default>