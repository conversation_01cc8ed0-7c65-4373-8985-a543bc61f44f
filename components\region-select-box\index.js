const UTIL = require("@/utils/util")
const API = require("@/config/api")
const {
  formatProvinceData,
  extractRegionParams,
} = require("@/utils/regionUtils")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 已选择的地区列表
    selectedRegions: {
      type: Array,
      value: [],
    },

    // 是否在弹窗中使用（影响高度和定位）
    inPopup: {
      type: Boolean,
      value: false,
    },
    // 弹窗中的高度限制
    popupHeight: {
      type: String,
      value: "80vh",
    },
    // 控制颜色
    isShowBg: {
      type: Boolean,
      value: true,
    },
    noPadding: {
      type: Boolean,
      value: false,
    },
    show: {
      type: Boolean,
      value: false,
    },
    filterKey: {
      type: String,
      value: "",
    },
    isSubscribe: {
      type: Boolean,
      value: false,
    },
    subscribedData: {
      type: Object,
      value: null,
    },
    // 选择级别：2=二级(省市)，3=三级(省市区)
    selectLevel: {
      type: Number,
      value: 3, // 默认三级，保持向后兼容
    },
    // 最多选择地区数量
    maxSelectCount: {
      type: Number,
      value: 999, // 默认最多选择3个地区
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 省份列表
    provinceList: [],
    // 城市列表
    cityList: [],
    // 区县列表
    districtList: [],

    // 当前选中的省份
    currentProvince: {},
    // 当前选中的城市
    currentCity: {},

    tempSelected: [],

    currentDistrict: [],

    // 加载状态
    loading: {
      province: false,
      city: false,
      district: false,
    },
  },
  observers: {
    show: function (newVal) {
      if (newVal) {
        this.setData({
          cityList: [],
          districtList: [],
          currentProvince: {},
          currentCity: {},
          currentDistrict: [],
          tempSelected: this.data.selectedRegions || [],
        })
        if (this.data?.tempSelected?.length > 0) {
          this.initializeSelectedRegions()
        }
      }
    },
  },
  lifetimes: {
    async attached() {
      console.log("每次")
      await this.loadNewsProvinceList()
      if (this.data?.tempSelected?.length > 0) {
        await this.initializeSelectedRegions()
      }
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 根据已选择的地区初始化相应的列表数据
     */
    async initializeSelectedRegions() {
      const regionObj = this.data.tempSelected[0]
      console.log(regionObj, "数据是啥")
      // 判断regionObj的级别
      const regionLevel = this.getRegionLevel(regionObj)
      console.log("regionObj级别:", regionLevel, regionObj)

      // 找到对应的省份
      const provinceIndex = this.data.provinceList.findIndex((item) => {
        return (
          item.id == regionObj.area_top_id ||
          item.id == regionObj?.region_province
        )
      })
      const province = this.data.provinceList[provinceIndex]

      if (!province) {
        console.error("未找到对应的省份", regionObj)
        return
      }

      // 设置当前省份
      this.setData({
        currentProvince: province,
        districtList: [], // 只有切换省份时才清空
      })

      console.log(province, "-------------------------", this.data.provinceList)

      try {
        // 加载城市列表
        await this.loadNewsCityList(province)

        // 如果是区县级别，还需要加载区县列表
        if (regionLevel === 3) {
          // 找到对应的城市
          const city = this.findCityByRegionObj(regionObj)
          if (city) {
            console.log("找到对应城市，加载区县列表:", city)
            // 设置当前城市
            this.setData({ currentCity: city })
            // 加载区县列表
            await this.loadNewsDistrictList(city)
          }
        }
      } catch (error) {
        console.error("初始化选中地区失败:", error)
      }
    },
    /**
     * 判断地区对象的级别
     * @param {Object} regionObj - 地区对象
     * @returns {number} 1-省份, 2-城市, 3-区县
     */
    getRegionLevel(regionObj) {
      // 方法1: 通过key判断
      if (regionObj.key) {
        const parts = regionObj.key.split("-")
        if (parts.length >= 3) {
          // 如果第三位不是0，说明是区县级别
          if (parseInt(parts[2]) > 0) return 3
          // 如果第二位不是0，说明是城市级别
          if (parseInt(parts[1]) > 0) return 2
          // 否则是省份级别
          return 1
        }
      }
    },
    /**
     * 根据regionObj找到对应的城市
     * @param {Object} regionObj - 地区对象
     * @returns {Object|null} 找到的城市对象
     */
    findCityByRegionObj(regionObj) {
      if (!this.data.cityList || !this.data.cityList.length) {
        return null
      }

      // 通过area_parent_id或id查找城市
      return this.data.cityList.find((city) => {
        // 如果regionObj是区县，其area_parent_id应该等于城市的id
        return (
          city.id == regionObj.area_parent_id ||
          city.id == regionObj?.region_city
        )
      })
    },
    handleProvinceClick(e) {
      const { index } = e.currentTarget.dataset
      const province = this.data.provinceList[index]

      // 判断是否是当前已选中的省份（根据 id 判断）
      if (
        this.data.currentProvince &&
        this.data.currentProvince.id === province.id
      ) {
        // 已选中的是同一个省份，不需要处理
        return
      }

      // 否则更新 currentProvince，并清空 districtList
      this.setData({
        currentProvince: province,
        districtList: [], // 只有切换省份时才清空
      })

      this.loadNewsCityList(province)
    },
    /**
     * 处理城市点击事件
     */
    handleCityClick(e) {
      const { index } = e.currentTarget.dataset
      const city = this.data.cityList[index]

      this.setData({ currentCity: city })

      // 处理城市选择逻辑
      this.handleCitySelection(city)

      // 根据选择级别决定是否加载区县
      // selectLevel=2时只选择到市级，selectLevel=3时选择到区县级
      if (this.data.selectLevel === 3 && city.single != 1) {
        // 三级选择：如果不是"全部"选项，才加载下一级区县
        this.loadNewsDistrictList(city)
      } else if (this.data.selectLevel === 2) {
        // 二级选择：直接处理城市选择，不加载区县
        // 清空区县列表
        this.setData({
          districtList: [],
          currentDistrict: [],
        })
      }
    },

    /**
     * 处理城市选择逻辑
     */
    handleCitySelection(city) {
      const tempSelected = [...this.data.tempSelected]
      const currentProvince = this.data.currentProvince

      if (city.single == 1) {
        // 选择的是"全部"选项
        // 1. 移除当前省份下的所有其他城市选择
        const filteredSelected = tempSelected.filter((item) => {
          if (!item.key) return true
          const parts = item.key.split("-")
          const provinceId = parseInt(parts[0])
          return provinceId !== currentProvince.id
        })

        // 2. 检查当前"全部"选项是否已选中
        const existsIndex = tempSelected.findIndex(
          (item) => item.id === city.id
        )
        if (existsIndex > -1) {
          // 已选中，取消选中
          const newSelected = [...tempSelected]
          newSelected.splice(existsIndex, 1)
          this.setData({
            tempSelected: newSelected,
            districtList: [], // 清空区县列表
            currentDistrict: [], // 清空当前选中的区县
          })
        } else {
          // 未选中，添加选中
          // 检查是否超过最大选择数量
          if (filteredSelected.length >= this.data.maxSelectCount) {
            wx.showToast({
              title: `最多只能选择${this.data.maxSelectCount}个地区`,
              icon: "none",
            })
            return
          }

          filteredSelected.push(city)
          this.setData({
            tempSelected: filteredSelected,
            districtList: [], // 清空区县列表
            currentDistrict: [], // 清空当前选中的区县
          })
        }
      } else {
        // 选择的是具体城市
        if (this.data.selectLevel === 2) {
          // 二级选择：城市支持多选
          // 1. 移除当前省份下的"全部"选项（如果存在）
          const filteredSelected = tempSelected.filter((item) => {
            if (!item.key) return true
            const parts = item.key.split("-")
            const provinceId = parseInt(parts[0])
            // 如果不是当前省份的项目，保留
            if (provinceId !== currentProvince.id) return true
            // 如果是当前省份的项目，检查是否是"全部"选项
            return item.single != 1
          })

          // 2. 检查当前城市是否已选中
          const existsIndex = filteredSelected.findIndex(
            (item) => item.id === city.id
          )
          if (existsIndex > -1) {
            // 已选中，取消选中
            filteredSelected.splice(existsIndex, 1)
            this.setData({
              tempSelected: filteredSelected,
              currentDistrict: [],
            })
          } else {
            // 未选中，添加选中
            // 检查是否超过最大选择数量
            if (filteredSelected.length >= this.data.maxSelectCount) {
              wx.showToast({
                title: `最多只能选择${this.data.maxSelectCount}个地区`,
                icon: "none",
              })
              return
            }

            filteredSelected.push(city)
            this.setData({
              tempSelected: filteredSelected,
              currentDistrict: [],
            })
          }
        } else {
          // 三级选择：选择的是具体城市，不添加到tempSelected，但要移除当前省份下的"全部"选项
          // 1. 移除当前省份下的"全部"选项
          const filteredSelected = tempSelected.filter((item) => {
            if (!item.key) return true
            const parts = item.key.split("-")
            const provinceId = parseInt(parts[0])
            // 如果不是当前省份的项目，保留
            if (provinceId !== currentProvince.id) return true
            // 如果是当前省份的项目，检查是否是"全部"选项
            return item.single != 1
          })

          // 2. 更新tempSelected并清空区县相关数据
          this.setData({
            tempSelected: filteredSelected,
            currentDistrict: [], // 清空当前区县选择
          })
        }
      }
    },

    /**
     * 处理区县点击事件
     */
    handleDistrictClick(e) {
      const { index } = e.currentTarget.dataset
      const district = this.data.districtList[index] // 假设 district 有 id 字段

      // 处理区县选择逻辑
      this.handleDistrictSelection(district)
    },

    /**
     * 处理区县选择逻辑
     */
    handleDistrictSelection(district) {
      const tempSelected = [...this.data.tempSelected]
      const currentDistrict = [...this.data.currentDistrict]
      const currentCity = this.data.currentCity
      console.log(currentDistrict, "12222222222")
      // 检查是否已经在currentDistrict中选中
      const matchIndex = currentDistrict.findIndex(
        (item) => item.id === district.id
      )

      if (matchIndex > -1) {
        // 已存在，取消选中
        const removedId = currentDistrict[matchIndex].id
        this.removeFromTempSelectedById(removedId)
        currentDistrict.splice(matchIndex, 1)
        this.setData({ currentDistrict })
      } else {
        // 不存在，添加选中
        // 检查是否超过最大选择数量
        if (tempSelected.length >= this.data.maxSelectCount) {
          wx.showToast({
            title: `最多只能选择${this.data.maxSelectCount}个地区`,
            icon: "none",
          })
          return
        }

        if (district.single == 1) {
          // 选择的是"全部"选项
          // 1. 移除当前城市下的所有其他区县选择
          const filteredSelected = tempSelected.filter((item) => {
            if (!item.key) return true
            const parts = item.key.split("-")
            const cityId = parseInt(parts[1])
            return cityId !== currentCity.id
          })

          // 2. 清空currentDistrict并添加"全部"选项
          currentDistrict.length = 0
          currentDistrict.push(district)
          filteredSelected.push(district)

          this.setData({
            tempSelected: filteredSelected,
            currentDistrict,
          })
        } else {
          console.log("进来这里", tempSelected)
          // 选择的是具体区县
          // 1. 移除当前城市下的"全部"选项
          const filteredSelected = tempSelected.filter((item) => {
            if (!item.key) return true
            const parts = item.key.split("-")
            const cityId = parseInt(parts[1])
            // 如果不是当前城市的项目，保留
            if (cityId !== currentCity.id) return true
            // 如果是当前城市的项目，检查是否是"全部"选项
            return item.single != 1
          })

          // 2. 移除currentDistrict中的"全部"选项
          const filteredCurrentDistrict = currentDistrict.filter(
            (item) => item.single != 1
          )

          // 3. 添加当前区县
          filteredSelected.push(district)
          filteredCurrentDistrict.push(district)

          this.setData({
            tempSelected: filteredSelected,
            currentDistrict: filteredCurrentDistrict,
          })
        }
      }
    },

    handleItemChange(selectedItem) {
      const tempSelected = [...this.data.tempSelected] // 创建副本避免直接修改 data

      // 根据 id 判断是否已存在
      const exists = tempSelected.some((item) => item.id === selectedItem.id)

      if (!exists) {
        tempSelected.push(selectedItem)

        this.setData({
          tempSelected,
          // activeList: this.parseRegionKeys(tempSelected),
        })
      }
    },

    removeFromTempSelectedById(id) {
      const tempSelected = [...this.data.tempSelected]
      const matchIndex = tempSelected.findIndex((item) => item.id === id)

      if (matchIndex > -1) {
        tempSelected.splice(matchIndex, 1)
      }

      this.setData({ tempSelected })
    },

    parseRegionKeys(regionArray) {
      const result = {
        province: [],
        city: [],
        district: [],
      }

      if (!Array.isArray(regionArray)) return result

      const allIds = regionArray
        .filter(
          (item) => item && typeof item.key === "string" && item.key.trim()
        )
        .flatMap((item) => item.key.split("-"))

      const uniqueIds = [...new Set(allIds)]

      uniqueIds.forEach((id) => {
        const numId = Number(id)
        if (isNaN(numId)) return

        // 简单规则判断层级
        if (id.endsWith("0000")) {
          result.province.push(numId)
        } else if (id.endsWith("00")) {
          result.city.push(numId)
        } else {
          result.district.push(numId)
        }
      })

      return result
    },

    /**
     * 移除已选择的地区
     */
    removeSelectedRegion(e) {
      const { index } = e.currentTarget.dataset
      const tempSelected = [...this.data.tempSelected]
      if (index > -1) {
        tempSelected.splice(index, 1)
      }
      this.setData({
        tempSelected,
      })
    },

    /**
     * 清空所有选择
     */
    clearAllSelected() {
      // this.triggerEvent("clearAllSelected")
      this.setData({
        tempSelected: [],
        currentDistrict: [],
        currentCity: {},
        currentProvince: {},
        cityList: [],
        districtList: [],
      })
    },

    /**
     * 完成选择
     */
    confirmSelection() {
      // 如果是订阅模式，检查是否超过3个地区
      if (this.data.isSubscribe && this.data.tempSelected.length > 3) {
        wx.showToast({
          title: "最多只能选择3个地区",
          icon: "none",
        })
        return
      }

      this.triggerEvent("confirmSelection", {
        tempSelected: this.data.tempSelected,
        filterKey: this.data.filterKey,
      })
    },

    step() {
      this.triggerEvent("stepSelection")
    },

    /**
     * 加载考试动态城市列表
     */
    async loadNewsCityList(province) {
      // this.setData({
      //   "newsRegionPopupData.loading.city": true,
      // })
      try {
        // 使用工具函数提取参数
        const { topId, parentId } = extractRegionParams(province)

        // 调用接口获取城市列表 - level=2表示城市
        const cityData = await this.getNewsRegionList(topId, parentId, 2)
        // 使用工具函数格式化数据
        const formattedCityList = cityData

        // 同步tempSelected中的城市数据，并设置当前城市
        const syncResult = this.syncTempSelectedWithCityList(
          this.data.tempSelected,
          formattedCityList,
          province
        )

        this.setData({
          cityList: formattedCityList,
          "newsRegionPopupData.loading.city": false,
          tempSelected: syncResult.updatedTempSelected, // 更新同步后的数据
          currentCity: syncResult.currentCity || this.data.currentCity, // 更新当前城市
        })

        // 同步已选择地区的状态
        // this.syncNewsSelectedRegionsWithLists()
      } catch (error) {
        console.error("考试动态加载城市列表失败:", error)
        this.setData({
          "newsRegionPopupData.loading.city": false,
        })
        wx.showToast({
          title: "加载城市失败",
          icon: "none",
        })
      }
    },

    /**
     * 同步tempSelected中的城市数据，并设置当前城市
     * @param {Array} tempSelected - 当前已选择的数据
     * @param {Array} cityList - 完整的城市列表数据
     * @param {Object} currentProvince - 当前选中的省份对象
     * @returns {Object} 包含updatedTempSelected和currentCity的对象
     */
    syncTempSelectedWithCityList(tempSelected, cityList, currentProvince) {
      if (!Array.isArray(tempSelected) || !Array.isArray(cityList)) {
        return {
          updatedTempSelected: tempSelected,
          currentCity: null,
        }
      }

      // 创建cityList的key-对象映射，便于快速查找
      const cityMap = new Map()
      cityList.forEach((city) => {
        if (city.key) {
          cityMap.set(city.key, city)
        }
      })

      let foundCurrentCity = null

      // 遍历tempSelected，替换匹配的对象
      const updatedTempSelected = tempSelected.map((selectedItem) => {
        // 如果tempSelected中的项目有key，且在cityList中能找到匹配的完整对象
        if (selectedItem.key && cityMap.has(selectedItem.key)) {
          const completeCity = cityMap.get(selectedItem.key)
          console.log(`替换城市数据: ${selectedItem.key}`, {
            原数据: selectedItem,
            新数据: completeCity,
          })

          // 检查是否是当前省份的城市，如果是则设置为当前城市
          if (currentProvince && selectedItem.key) {
            const parts = selectedItem.key.split("-")
            const provinceId = parseInt(parts[0])
            if (provinceId === currentProvince.id && !foundCurrentCity) {
              foundCurrentCity = completeCity
            }
          }

          // 用完整的city对象替换原对象
          return completeCity
        } else {
          // 即使没有在cityList中找到匹配，也要检查是否应该设置为当前城市
          if (currentProvince && selectedItem.key && !foundCurrentCity) {
            const parts = selectedItem.key.split("-")
            const provinceId = parseInt(parts[0])
            if (provinceId === currentProvince.id) {
              // 如果是区县级别的数据，尝试根据area_parent_id找到对应城市
              if (selectedItem.area_parent_id) {
                const cityById = cityList.find(
                  (city) => city.id === selectedItem.area_parent_id
                )
                if (cityById) {
                  foundCurrentCity = cityById
                }
              }
            }
          }
        }
        // 如果没有匹配，保持原对象不变
        return selectedItem
      })

      console.log("城市同步结果:", {
        原tempSelected: tempSelected,
        新tempSelected: updatedTempSelected,
        找到的当前城市: foundCurrentCity,
      })

      return {
        updatedTempSelected,
        currentCity: foundCurrentCity,
      }
    },
    /**
     * 加载考试动态区县列表
     */
    async loadNewsDistrictList(city) {
      this.setData({
        "newsRegionPopupData.loading.district": true,
      })

      try {
        // 使用工具函数提取参数
        const topId = city.area_parent_id
        const parentId = city.id
        // 调用接口获取区县列表 - level=3表示区县
        const districtData = await this.getNewsRegionList(topId, parentId, 3)

        // 使用工具函数格式化数据
        const formattedDistrictList = formatProvinceData(districtData)

        // 同步tempSelected中的数据，用完整的districtList对象替换可能缺少字段的对象
        const syncResult = this.syncTempSelectedWithDistrictList(
          this.data.tempSelected,
          formattedDistrictList,
          city
        )

        this.setData({
          districtList: formattedDistrictList,
          "newsRegionPopupData.loading.district": false,
          tempSelected: syncResult.updatedTempSelected, // 更新同步后的数据
          currentDistrict: syncResult.updatedCurrentDistrict, // 更新当前选中的区县
        })

        // 同步已选择地区的状态
        // this.syncNewsSelectedRegionsWithLists()
      } catch (error) {
        console.error("考试动态加载区县列表失败:", error)
        this.setData({
          "newsRegionPopupData.loading.district": false,
        })
        wx.showToast({
          title: "加载区县失败",
          icon: "none",
        })
      }
    },

    /**
     * 同步tempSelected中的数据，用完整的districtList对象替换可能缺少字段的对象
     * 同时同步currentDistrict
     * @param {Array} tempSelected - 当前已选择的数据
     * @param {Array} districtList - 完整的区县列表数据
     * @param {Object} currentCity - 当前选中的城市对象
     * @returns {Object} 包含updatedTempSelected和updatedCurrentDistrict的对象
     */
    syncTempSelectedWithDistrictList(tempSelected, districtList, currentCity) {
      if (!Array.isArray(tempSelected) || !Array.isArray(districtList)) {
        return {
          updatedTempSelected: tempSelected,
          updatedCurrentDistrict: [],
        }
      }

      // 创建districtList的key-对象映射，便于快速查找
      const districtMap = new Map()
      districtList.forEach((district) => {
        if (district.key) {
          districtMap.set(district.key, district)
        }
      })

      // 遍历tempSelected，替换匹配的对象
      const updatedTempSelected = tempSelected.map((selectedItem) => {
        // 如果tempSelected中的项目有key，且在districtList中能找到匹配的完整对象
        if (selectedItem.key && districtMap.has(selectedItem.key)) {
          const completeDistrict = districtMap.get(selectedItem.key)
          console.log(`替换数据: ${selectedItem.key}`, {
            原数据: selectedItem,
            新数据: completeDistrict,
          })
          // 用完整的district对象替换原对象
          return completeDistrict
        }
        // 如果没有匹配，保持原对象不变
        return selectedItem
      })

      // 同步currentDistrict - 找出属于当前城市的已选中区县
      const updatedCurrentDistrict = []
      if (currentCity && currentCity.id) {
        updatedTempSelected.forEach((selectedItem) => {
          // 检查是否是当前城市的区县
          if (selectedItem.key) {
            const parts = selectedItem.key.split("-")
            const cityId = parseInt(parts[1])
            if (cityId === currentCity.id) {
              // 如果在districtList中能找到对应的完整对象，使用完整对象
              if (districtMap.has(selectedItem.key)) {
                updatedCurrentDistrict.push(districtMap.get(selectedItem.key))
                console.log(
                  `添加到currentDistrict: ${selectedItem.key}`,
                  districtMap.get(selectedItem.key)
                )
              } else {
                // 否则使用原对象
                updatedCurrentDistrict.push(selectedItem)
              }
            }
          }
        })
      }

      console.log("同步结果:", {
        原tempSelected: tempSelected,
        新tempSelected: updatedTempSelected,
        新currentDistrict: updatedCurrentDistrict,
      })

      return {
        updatedTempSelected,
        updatedCurrentDistrict,
      }
    },

    /**
     * 调用地区接口获取数据（考试动态专用）
     * @param {number} topId - 省级id
     * @param {number} parentId - 父级id
     * @param {number} level - 第几级（1:省份, 2:城市, 3:区县）
     */
    async getNewsRegionList(topId = 0, parentId = 0, level = 1) {
      const param = {
        top_id: topId,
        parent_id: parentId,
        level: level,
      }

      try {
        const res = await UTIL.request(API.getRegionChildList, param)

        if (res && res.error && res.error.code === 0 && res.data) {
          return res.data
        } else {
          console.error("考试动态获取地区数据失败:", res)
          return []
        }
      } catch (error) {
        console.error("考试动态获取地区数据时出错", error)
        return []
      }
    },

    /**
     * 加载考试动态省份列表
     */
    async loadNewsProvinceList() {
      try {
        // 调用接口获取省份列表 - level=1表示省份
        const provinceData = await this.getNewsRegionList(0, 0, 1)

        // 使用工具函数格式化数据
        const formattedProvinceList = formatProvinceData(provinceData)
        this.setData({ provinceList: formattedProvinceList })
      } catch (error) {
        wx.showToast({
          title: "加载省份失败",
          icon: "none",
        })
      }
    },
  },
})
