.report-card {
  box-sizing: border-box;
  padding: 40rpx 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: 16rpx;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    .title {
      display: flex;
      align-items: center;
      .img {
        height: 34rpx;
        width: 8rpx;
        margin-right: 24rpx;
      }

      .text {
        font-size: 36rpx;
        font-weight: 600;
        color: #22242e;
        line-height: 1;
      }
    }
  }
  .max-style {
    max-height: 400rpx;
    overflow: hidden;
    /*隐藏多出部分文字*/
    text-overflow: ellipsis;
    /*用省略号代替多出部分文字*/
    display: -webkit-box;
    /* 显示多行文本容器 */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }
  .mx7 {
    max-height: 700rpx;
    -webkit-line-clamp: unset;
  }
  .collapse {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin-top: 16rpx;
    &-image {
      width: 176rpx;
      text-align: center;
      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}
.pd-style {
  padding-bottom: 24rpx !important;
}
.right {
  display: flex;
  align-items: center;
  .time-text {
    font-size: 22rpx;
    color: #e60003;
  }
}
.right-angle {
  border-radius: 0;
}
