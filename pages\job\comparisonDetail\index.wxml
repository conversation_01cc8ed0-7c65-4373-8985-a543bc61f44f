<view class="table-scroll-view-container">
  <!-- 外层竖向滚动容器 -->
  <scroll-view class="vertical-scroll-container" scroll-x="{{true}}" scroll-y="{{true}}">

    <!-- 左侧固定区域，独立固定定位 -->
    <view class="filter-fixed-section" style="width: {{firstColumnWidth}}rpx">
      <van-switch checked="{{ isHideSameTab }}" size="16px" active-color="#e60003" inactive-color="#EBECF0" bind:change="onChange" />
      <view class="first-text">隐藏相同项</view>
    </view>

    <!-- 右侧职位列表固定区域 -->
    <view class="box fixed-top-header" style="position: fixed; left: 0; top: 0; height: 264rpx; z-index: 1000; background: #fff; width: 100vw;">
      <view class="horizontal-scroll-area">
        <!-- 筛选固定行 - 显示职位列表 -->
        <view class="filter-scroll-section">
          <view class="data-row">
            <view wx:for="{{jobList}}" wx:key="index" class="data-cell first-job-header-top" style="width: {{columnWidth}}rpx; min-height: {{rowHeight}}rpx;">
              <view class="job-header-cell">
                <view class="tips-box {{ item.isTop?'active':''}}" bindtap="onPinClick" data-index="{{index}}">
                  <image class="tips-icon" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/{{item.isTop?'is_top':'not_top'}}.png " mode="" />
                  <view class="tips-text">{{ item.isTop? '已钉住':'钉在左侧'}}</view>
                </view>
                <image class="close-img" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/close.png" alt="" bindtap="onDeleteJob" data-index="{{index}}" />
                <view class="job-name" bind:tap="goJobDetail" data-id="{{item.id}}">{{item.name}}
                  <image class="job-arrow" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/job_arrow.png" mode="" />
                </view>
                <button-authorize wx:if="{{ !isLogin }}" isBindPhone="{{isLogin}}" bind:onAuthorize="onLogin">
                  <view class="focus-btn" style="display: flex; align-items: center;">
                    <image class="focus-img" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png" mode="" />
                    <view class="focus-text">关注职位</view>
                  </view>
                </button-authorize>
                <view wx:else class="focus-btn {{ item.isFocus?'active':''}}" bindtap="onFocusClick" data-index="{{index}}">
                  <image wx:if="{{item.isFocus}}" class="focus-img" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_gray.png" mode="" />
                  <image wx:else class="focus-img" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png" mode="" />
                  <view class="focus-text">{{item.isFocus? '已关注':'关注职位'}}</view>
                </view>
              </view>
            </view>
            <!-- 添加职位列 -->
            <view class="data-cell first-job-header-top" style="width: {{columnWidth}}rpx; min-height: {{rowHeight}}rpx;" bindtap="onAddJob">
              <view class="add-job-cell">
                <view class="add-job-content">
                  <image class="add-icon" src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/add_job.png" mode="" />
                  <view class="add-text">添加职位</view>
                </view>
              </view>

            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 占位区域，避免内容被固定头部遮挡 -->
    <view class="top-header-placeholder" style="height: 264rpx;"></view>
    <view class="box" style="width: max-content">
      <!-- 左侧固定首列区域 -->
      <view class="fixed-columns-area">
        <!-- 遍历所有表格的固定列 -->
        <view wx:for="{{tableList}}" wx:for-index="tableIndex" wx:key="id" class="table-fixed-section" style="width: {{firstColumnWidth}}rpx;">
          <!-- 吸顶区域 -->
          <view class="sticky-header header-placeholder">
            <view class="sticky-content">
              <view class="line"></view>
              <view class="text">{{item.title}}</view>
            </view>
          </view>
          <!-- 固定首列数据 - 显示表格的字段名 -->
          <view wx:for="{{item.fields}}" wx:for-index="fieldIndex" wx:for-item="fieldObj" wx:key="fieldIndex" class="first-column-cell {{fieldObj.isSameRow ? 'same-row-bg' : 'different-row-bg'}}" style="height: {{firstColumnHeights[tableIndex] && firstColumnHeights[tableIndex][fieldIndex] ? firstColumnHeights[tableIndex][fieldIndex] + 'px' : 'auto'}};">
            {{fieldObj.name}}
          </view>
        </view>

        <view class="table-fixed-section" style="width: {{firstColumnWidth}}rpx;">
          <!-- 吸顶区域 -->
          <view class="sticky-header header-placeholder">
            <view class="sticky-content" style="height:  auto;background: #fff;z-index: 100;">
              <view class="bottom-area">
                <view class="title">温馨提醒</view>
                <view class="list-item-bottom">
                  <view class="num">1</view>
                  <view class="text-desc">以上数据为金标尺公考整理，仅供参考，具体信息请以招考官方发布为准；</view>
                </view>
                <view class="list-item-bottom">
                  <view class="num">2</view>
                  <view class="text-desc">"-" 表示暂未收集到官方数据发布的数据。</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧横向滚动区域 -->
      <view class="horizontal-scroll-area">
        <view class="horizontal-scroll-content">
          <block wx:for="{{tableList}}" wx:for-index="tableIndex" wx:key="id">
            <view class="header-placeholder"></view>
            <!-- 遍历所有表格的可滚动部分 -->
            <view class="table-scroll-section">
              <!-- 遍历每个字段 -->
              <view wx:for="{{item.fields}}" wx:for-index="fieldIndex" wx:for-item="fieldObj" wx:key="fieldIndex" class="data-row {{fieldObj.isSameRow ? 'same-row-bg' : 'different-row-bg'}}" style="height: {{firstColumnHeights[tableIndex] && firstColumnHeights[tableIndex][fieldIndex] ? firstColumnHeights[tableIndex][fieldIndex] + 'px' : 'auto'}}; min-height: {{rowHeight}}rpx;">
                <!-- 遍历每个职位的数据 -->
                <view wx:for="{{item.rows}}" wx:for-index="jobIndex" wx:for-item="jobData" wx:key="jobIndex" class="data-cell {{jobIndex===0?'first-cell':''}} {{item.isRed[fieldIndex][jobIndex] ? 'cred' : ''}}" style="width: {{columnWidth}}rpx; height: 100%;">
                  {{jobData[fieldObj.name] || '-'}}
                </view>
                <!-- 添加职位列 - 空内容 -->
                <view class="data-cell add-job-data-cell" style="width: {{columnWidth}}rpx; height: 100%;">
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
  </scroll-view>

</view>