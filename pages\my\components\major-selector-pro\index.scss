.major-selector-pro {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .header {
    padding: 34rpx 32rpx 32rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 32rpx;
      font-weight: 600;
    }
    .close-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .selected-area {
    padding: 0 24rpx;
    margin-bottom: 40rpx;
    .selected-display {
      background: #f7f8fa;
      border-radius: 16rpx;
      padding: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        font-size: 26rpx;
        color: #313436;
        .text {
          font-weight: 600;
        }
      }

      .delete-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .search-bar {
    padding: 6rpx 32rpx 24rpx 32rpx;
    display: flex;
    position: relative;
    border-bottom: 2rpx solid #ebecf0;

    .search-input {
      flex: 1;
      background-color: #ffffff;
      border: 2rpx solid #ebecf0;
      border-radius: 16rpx;
      padding: 22rpx 24rpx;
      font-size: 28rpx;
    }

    .search-icon {
      position: absolute;
      right: 56rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 32rpx;
      height: 32rpx;
    }
  }

  .tabs {
    display: flex;

    .tab-item {
      padding: 0 32rpx 32rpx 32rpx;
      font-size: 28rpx;
      color: #666666;
      position: relative;
      &.active {
        color: #22242e;
        font-weight: 600;
        &::after {
          content: "";
          position: absolute;
          width: 40rpx;
          height: 6rpx;
          background: #e60003;
          border-radius: 4rpx;
          bottom: 16rpx;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .checkbox {
    width: 40rpx;
    height: 40rpx;
  }

  .bottom-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-top: 1px solid #ebecf0;

    .sync-area {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #666;

      .checkbox-img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
    }

    .save-btn {
      width: 330rpx;
      height: 84rpx;
      border-radius: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      background: #ec3e33;
      color: #fff;
      opacity: 0.5;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        opacity: 1;
      }
    }
  }
}

// 无搜索结果样式
.no-result {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 64rpx;

  .no-result-text {
    font-size: 28rpx;
    color: rgba(194, 197, 204, 1);
  }
}
.major-content {
  flex: 1;
  overflow: hidden;

  .search-results {
    padding: 0 32rpx;
    height: 100%;
    overflow-y: auto;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    .search-item {
      padding: 22rpx 0;

      &:last-child {
        border-bottom: none;
      }

      .search-item-text {
        font-size: 28rpx;
        color: rgba(49, 52, 54, 1);
        line-height: 40rpx;
      }
    }
  }

  .major-list {
    display: flex;
    height: 100%;
    min-height: 0;
    .list-panel {
      flex: 1;
      height: 100%;
      position: relative;

      &::after {
        position: absolute;
        content: "";
        display: block;
        width: 2rpx;
        height: 100%;
        background: #ebecf0;
        right: 0;
        top: 0;
      }

      &:nth-child(1) {
        background-color: rgba(247, 248, 250, 0.5);
        .panel-item {
          &.active::after {
            display: block;
            position: absolute;
            content: " ";
            width: 4rpx;
            height: 100%;
            background-color: #fff;
            top: 0;
            right: 0;
            z-index: 2;
          }
        }
      }

      &:last-child::after {
        display: none;
      }

      .panel-item {
        padding: 24rpx 32rpx;
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.active {
          background-color: #ffffff;
          color: #e60003;
          font-weight: 600;
          position: relative;
        }
      }
    }
  }
}
