const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/mpRouter")

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    jobData: {
      type: Object,
      value: {},
    },
    projectId: {
      type: String,
      value: "",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关注按钮点击事件
     */
    async onFocusClick() {
      const { jobData } = this.properties
      const isFocus = jobData.is_follows == 1
      const param = {
        item_type: "job",
        item_no: [jobData.id],
        type: isFocus ? "unfollow" : "follow",
      }
      const res = await UTIL.request(API.setFollows, param)
      if (res) {
        wx.showToast({
          title: isFocus ? "已取消关注" : "关注成功",
          icon: "none",
        })
        this.setData({
          ["jobData.is_follows"]: isFocus ? 0 : 1,
        })
      }
    },

    /**
     * 卡片点击事件
     */
    onCardClick() {
      const { jobData } = this.properties
      if (!jobData || !jobData.id) return

      ROUTER.navigateTo({
        path: "/pages/job/detail/index",
        query: {
          id: jobData.id,
        },
      })
    },
  },
})
