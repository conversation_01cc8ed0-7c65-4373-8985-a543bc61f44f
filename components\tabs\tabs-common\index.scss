.scroll-x {
  white-space: nowrap;

  .scroll-item {
    display: inline-block;
  }
}

.cmpt-tabs {
  .tab-nav {
    box-sizing: border-box;
    &.flex {
      text-align: center;
      .nav-item {
        margin: 0 60rpx !important;
      }
    }

    &.flex_center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-item {
      position: relative;
      flex: 1;
      line-height: 60rpx;
      padding-bottom: 10rpx;
      text-align: center;
      font-size: 28rpx;
      color: #3c3d42;
      margin-right: 80rpx;
      &:last-child {
        margin-right: 60rpx;
      }

      &.active {
        font-size: 30rpx;
        font-weight: 600;
      }
    }
  }
}

.tab-nav1 {
  .nav-item {
    position: relative;
    &.active {
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 32rpx;
        height: 8rpx;
        background: #d62828;
        border-radius: 4rpx;
        margin-left: -16rpx;
      }
    }
  }
}

.tab-nav2 {
  position: relative;

  .line {
    position: absolute;
    bottom: 0rpx;
    left: 50%;
    width: 32rpx;
    height: 8rpx;
    background: #d62828;
    border-radius: 4rpx;
    margin-left: -16rpx;
    display: none;

    &.active {
      display: block;
      transition: 0.3s;
    }
  }
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-list {
  display: flex;
  align-items: center;
  position: relative;
  .nav-item {
    margin-right: 0 !important;
  }
}

.try-listen {
  width: 52rpx;
  height: 28rpx;
  position: absolute;
  right: -60rpx;
  top: 5rpx;
}
