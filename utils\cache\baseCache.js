import { setCache, getCache } from "./core/core"

const CACHE_NAME = "base"

// 设置用户信息缓存
export function setBaseCache(data) {
  return setCache(CACHE_NAME, data)
}

// 获取用户信息缓存
export function getBaseCache() {
  return getCache(CACHE_NAME) || { province: {}, examDirection: {}, campus: {} }
}

// 设置省份信息
export function setBaseProvinceCache(data) {
  return setCache(`${CACHE_NAME}.province`, data)
}

// 获取省份信息
export function getBaseProvinceCache() {
  return getCache(`${CACHE_NAME}.province`)
}

// 设置考试信息
export function setBaseExamDirectionCache(data) {
  return setCache(`${CACHE_NAME}.examDirection`, data)
}

// 获取考试信息
export function getBaseExamDirectionCache() {
  return getCache(`${CACHE_NAME}.examDirection`)
}

// 设置学校信息
export function setBaseCampusCache(data) {
  return setCache(`${CACHE_NAME}.campus`, data)
}

// 获取学校信息
export function getBaseCampusCache() {
  return getCache(`${CACHE_NAME}.campus`)
}
