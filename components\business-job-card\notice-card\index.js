const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
    multipleSlots: true,
  },
  properties: {
    list: {
      type: Array,
      value: [],
    },
    isTop: {
      type: Boolean,
      value: true,
    },
  },
  data: {},
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.item
      const path =
        data.type == "article_list"
          ? "/pages/notice/collection/index"
          : "/pages/notice/detail/index"
      ROUTER.navigateTo({
        path,
        query: {
          id: data.id,
        },
      })
    },
  },
})
