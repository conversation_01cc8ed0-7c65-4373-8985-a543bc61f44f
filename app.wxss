/**app.wxss**/
@import "./static/fonts/DINBold";

page {
  font-family: "Arial";
}

image {
  width: auto;
  height: auto;
}

.container {
  padding-left: var(--container-margin);
  padding-right: var(--container-margin);
  box-sizing: border-box;
}

.container-negative {
  margin-left: -40rpx;
  margin-right: -40rpx;
}

.text-ellipsis-1 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  /*显示行数*/
}

.text-ellipsis-2 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.text-ellipsis-3 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.text-ellipsis-4 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}