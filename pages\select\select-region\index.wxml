<view class="main-content">
  <common-header show_white="{{show_white}}" title="报考地区选择">
    <view slot="left" class="lefts">
      <image class="left-arrow" wx:if="{{show_white}}" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
      <image class="left-arrow" wx:else catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_left_white.png"></image>
    </view>
  </common-header>
  <!-- 使用地区选择组件 -->
  <!-- <region-select-box style="flex:1;min-height:0" isShowBg="{{false}}" selectedRegions="{{selectedRegions}}" provinceList="{{provinceList}}" cityList="{{cityList}}" districtList="{{districtList}}" currentProvince="{{currentProvince}}" currentCity="{{currentCity}}" loading="{{loading}}" bind:provinceClick="handleProvinceClick" bind:cityClick="handleCityClick" bind:districtClick="handleDistrictClick" bind:districtLongPress="handleDistrictLongPress" bind:removeSelectedRegion="removeSelectedRegion" bind:clearAllSelected="clearAllSelected" bind:confirmSelection="confirmSelection" /> -->
  <region-select-box style="flex:1;min-height:0" show="{{showPopupFilterMenu}}" filterKey="apply_region" isShowBg="{{false}}" selectedRegions="{{selectedRegions}}" bind:confirmSelection="confirmSelection" />
  <!-- confirmSelection -->
</view>