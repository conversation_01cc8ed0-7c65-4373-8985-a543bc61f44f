// pages/components/common/teacher-detail-model/index.js
const APP = getApp()
Component({
  options: {
    multipleSlots: true, // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    brandName: APP.globalData.CONFIG.MP_BRAND_NAME,
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 弹窗关闭
    closeChange() {
      this.triggerEvent("closechange", {
        status: false,
      })
    },
    confirm() {
      this.triggerEvent("confirm")
    },
    // 跳转用户隐私协议
    toPrivacyUser() {
      wx.navigateTo({
        url:
          "/pages/webview/any/index?url=" +
          encodeURIComponent(APP.globalData.CONFIG.webviewPrivacyUser),
      })
    },
    // 跳转隐私协议
    toPrivacy() {
      wx.navigateTo({
        url:
          "/pages/webview/any/index?url=" +
          encodeURIComponent(APP.globalData.CONFIG.webviewPrivacy),
      })
    },
  },
})
