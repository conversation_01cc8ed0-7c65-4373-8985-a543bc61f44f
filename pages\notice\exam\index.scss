page {
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);

  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-image: url(https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_bg_red.png);
}

.lefts {
  padding-left: 32rpx;
  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.rights {
  .collection-img {
    width: 40rpx;
    height: 40rpx;
    margin-left: 34rpx;
  }
}

.collection-list {
  padding: 34rpx 32rpx;
  &-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    &:last-child {
      margin-bottom: 0;
    }
    .top {
      display: flex;
      .title {
        font-size: 32rpx;
        color: rgba(34, 36, 46, 1);
        flex: 1;
        min-width: 0;
        padding-right: 50rpx;
        font-weight: 600;
        line-height: 44rpx;
      }
      .label-text {
        margin-top: 5rpx;
        font-size: 24rpx;
        color: rgba(60, 61, 66, 1);
        .num {
          font-size: 24rpx;
          color: rgba(230, 0, 3, 1);
          font-weight: 600;
          font-family: "DINBold";
        }
      }
    }
    .time {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 32rpx;
    }
  }
}
