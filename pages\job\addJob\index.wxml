<wxs src="/utils/wxs/jobUtils.wxs" module="jobUtils" />

<view class="add-job-page">

  <view class="top-area">
    <text class="text-1">来自：</text>
    <text class="text-2">关注的职位</text>
  </view>

  <!-- 职位卡片列表 -->
  <view class="card-area">
    <comparison-card wx:for="{{jobList}}" wx:key="id" dataItem="{{item}}" hideDelete="{{true}}" bind:select="onCardSelect">
    </comparison-card>
  </view>

  <tabbar-box>
    <view class="bottom-btn">
      <view class="btn" bindtap="onAddJobs">添加</view>
    </view>
  </tabbar-box>
</view>