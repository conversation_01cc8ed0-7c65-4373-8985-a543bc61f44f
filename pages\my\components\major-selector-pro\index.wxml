<van-popup show="{{ show }}" position="bottom" custom-style="height: 90%; border-radius: 24rpx 24rpx 0 0;" z-index="9999" bind:close="onClose">
  <view class="major-selector-pro">
    <view class="header">
      <view class="title">2025年教育部专业指导目录</view>
    </view>

    <view class="selected-area" wx:if="{{selectedMajorIds && selectedMajorIds.length == 3}}">
      <view class="selected-display">
        <view class="left">
          已选：<text class="text">{{ selectedText }}</text>
        </view>
        <image class="delete-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/selector_delete.png" bind:tap="clearSelection" />
      </view>
    </view>

    <view class="tabs">
      <view wx:for="{{ tabList }}" wx:key="index" class="tab-item {{ educationId == item.education_id ? 'active' : '' }}" data-data="{{item}}" data-id="{{ item.education_id }}" id="tab-{{index}}" bind:tap="onTabClick">
        {{ item.name }}
      </view>
    </view>

    <view class="search-bar">
      <input class="search-input" placeholder="请输入专业名词关键词" value="{{ searchKeyword }}" bindinput="onSearchInput" />
      <image class="search-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search.png" />
    </view>



    <view class="major-content">
      <!-- 搜索结果 -->
      <view class="search-results" wx:if="{{ searchKeyword && searchResults.length > 0 }}">
        <view class="search-item" wx:for="{{ searchResults }}" wx:key="index" bindtap="onSelectSearchResult" data-item="{{ item }}">
          <view class="search-item-text">{{ item.name }}</view>
        </view>
      </view>

      <!-- 无搜索结果 -->
      <view class="no-result" wx:elif="{{ searchKeyword && searchResults.length === 0 }}">
        <view class="no-result-text">未找到相关专业</view>
      </view>

      <!-- 专业列表 -->
      <view class="major-list" wx:else>
        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view class="panel-item {{ selectedFirstIndex === index ? 'active' : '' }}" wx:for="{{ firstLevelList }}" wx:key="index" bindtap="onFirstLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view class="panel-item {{ selectedSecondIndex === index ? 'active' : '' }}" wx:for="{{ secondLevelList }}" wx:key="index" bindtap="onSecondLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view class="panel-item {{ selectedThirdIndex === index ? 'active' : '' }}" wx:for="{{ thirdLevelList }}" wx:key="index" bindtap="onThirdLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
      </view>
    </view>
    <view class="bottom-actions">
      <view class="sync-area" bind:tap="toggleSyncToResume">
        <image class="checkbox-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/{{ syncToResume ? 'selector_red' : 'selector_gray' }}.png" />
        <text>同步至我的简历</text>
      </view>
      <view class="action-btn save-btn {{ selectedMajor ? 'active' : '' }}" bind:tap="onSave">保存</view>
    </view>
  </view>
</van-popup>