.share-popup {
  .share-popup-box {
    padding-top: 48rpx;
    .title {
      font-size: 24rpx;
      color: #3c3d42;
      text-align: center;
    }
    image {
      width: 96rpx;
      height: 96rpx;
    }
    .img-item {
      margin: 0 100rpx;
      text {
        font-size: 20rpx;
        color: #64696d;
        text-align: center;
        margin-top: 16rpx;
      }
      display: flex;
      flex-direction: column;
      justify-content: center;
      background: #fff;
      border: 0;
      padding: 0;
      line-height: 1em;
      &::after {
        border: none;
      }
    }
    .img-content {
      display: flex;
      justify-content: center;
      padding: 64rpx 0;
      border-bottom: 1px solid #ebecf0;
    }
    .confirm {
      font-size: 28rpx;
      color: #3c3d42;
      padding: 40rpx 0;
      text-align: center;
      font-weight: 500;
    }
  }
}
