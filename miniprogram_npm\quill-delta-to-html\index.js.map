{"version": 3, "sources": ["main.js", "QuillDeltaToHtmlConverter.js", "InsertOpsConverter.js", "DeltaInsertOp.js", "value-types.js", "InsertData.js", "OpAttributeSanitizer.js", "mentions/MentionSanitizer.js", "OpLinkSanitizer.js", "helpers/url.js", "funcs-html.js", "helpers/array.js", "InsertOpDenormalizer.js", "helpers/string.js", "helpers/object.js", "OpToHtmlConverter.js", "grouper/Grouper.js", "grouper/group-types.js", "grouper/ListNester.js", "grouper/TableGrouper.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,ADGA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AIZA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,ALeA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,ADGA,AOrBA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AQxBA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ADGA,AFMA,AHSA;ADIA,AENA,AHSA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,ADGA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,ADGA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AFMA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,AJYA,AFMA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AENA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,APqBA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,ACHA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ALeA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AENA,AOrBA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,ACHA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA,AHSA;ADIA,AS3BA,AV8BA,AIZA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA,ANkBA;AJaA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AGTA,ADGA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA,AENA;AV+BA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AS3BA,ANkBA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA,ANkBA;ARyBA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ACHA,AFMA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,AS3BA,AMlBA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA,ADGA;Ad2CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;Af8CA,AGTA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,Ae7CA,AENA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA,AiBnDA;AZqCA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AKdA,AS3BA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA,Ad0CA;AczCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar QuillDeltaToHtmlConverter_1 = require(\"./QuillDeltaToHtmlConverter\");\nexports.QuillDeltaToHtmlConverter = QuillDeltaToHtmlConverter_1.QuillDeltaToHtmlConverter;\nvar OpToHtmlConverter_1 = require(\"./OpToHtmlConverter\");\nexports.OpToHtmlConverter = OpToHtmlConverter_1.OpToHtmlConverter;\nvar group_types_1 = require(\"./grouper/group-types\");\nexports.InlineGroup = group_types_1.InlineGroup;\nexports.VideoItem = group_types_1.VideoItem;\nexports.BlockGroup = group_types_1.BlockGroup;\nexports.ListGroup = group_types_1.ListGroup;\nexports.ListItem = group_types_1.ListItem;\nexports.BlotBlock = group_types_1.BlotBlock;\nvar DeltaInsertOp_1 = require(\"./DeltaInsertOp\");\nexports.DeltaInsertOp = DeltaInsertOp_1.DeltaInsertOp;\nvar InsertData_1 = require(\"./InsertData\");\nexports.InsertDataQuill = InsertData_1.InsertDataQuill;\nexports.InsertDataCustom = InsertData_1.InsertDataCustom;\nvar value_types_1 = require(\"./value-types\");\nexports.NewLine = value_types_1.NewLine;\nexports.ListType = value_types_1.ListType;\nexports.ScriptType = value_types_1.ScriptType;\nexports.DirectionType = value_types_1.DirectionType;\nexports.AlignType = value_types_1.AlignType;\nexports.DataType = value_types_1.DataType;\nexports.GroupType = value_types_1.GroupType;\n", "\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar InsertOpsConverter_1 = require(\"./InsertOpsConverter\");\nvar OpToHtmlConverter_1 = require(\"./OpToHtmlConverter\");\nvar Grouper_1 = require(\"./grouper/Grouper\");\nvar group_types_1 = require(\"./grouper/group-types\");\nvar ListNester_1 = require(\"./grouper/ListNester\");\nvar funcs_html_1 = require(\"./funcs-html\");\nvar obj = __importStar(require(\"./helpers/object\"));\nvar value_types_1 = require(\"./value-types\");\nvar TableGrouper_1 = require(\"./grouper/TableGrouper\");\nvar BrTag = '<br/>';\nvar QuillDeltaToHtmlConverter = (function () {\n    function QuillDeltaToHtmlConverter(deltaOps, options) {\n        this.rawDeltaOps = [];\n        this.callbacks = {};\n        this.options = obj.assign({\n            paragraphTag: 'p',\n            encodeHtml: true,\n            classPrefix: 'ql',\n            inlineStyles: false,\n            multiLineBlockquote: true,\n            multiLineHeader: true,\n            multiLineCodeblock: true,\n            multiLineParagraph: true,\n            multiLineCustomBlock: true,\n            allowBackgroundClasses: false,\n            linkTarget: '_blank',\n        }, options, {\n            orderedListTag: 'ol',\n            bulletListTag: 'ul',\n            listItemTag: 'li',\n        });\n        var inlineStyles;\n        if (!this.options.inlineStyles) {\n            inlineStyles = undefined;\n        }\n        else if (typeof this.options.inlineStyles === 'object') {\n            inlineStyles = this.options.inlineStyles;\n        }\n        else {\n            inlineStyles = {};\n        }\n        this.converterOptions = {\n            encodeHtml: this.options.encodeHtml,\n            classPrefix: this.options.classPrefix,\n            inlineStyles: inlineStyles,\n            listItemTag: this.options.listItemTag,\n            paragraphTag: this.options.paragraphTag,\n            linkRel: this.options.linkRel,\n            linkTarget: this.options.linkTarget,\n            allowBackgroundClasses: this.options.allowBackgroundClasses,\n            customTag: this.options.customTag,\n            customTagAttributes: this.options.customTagAttributes,\n            customCssClasses: this.options.customCssClasses,\n            customCssStyles: this.options.customCssStyles,\n        };\n        this.rawDeltaOps = deltaOps;\n    }\n    QuillDeltaToHtmlConverter.prototype._getListTag = function (op) {\n        return op.isOrderedList()\n            ? this.options.orderedListTag + ''\n            : op.isBulletList()\n                ? this.options.bulletListTag + ''\n                : op.isCheckedList()\n                    ? this.options.bulletListTag + ''\n                    : op.isUncheckedList()\n                        ? this.options.bulletListTag + ''\n                        : '';\n    };\n    QuillDeltaToHtmlConverter.prototype.getGroupedOps = function () {\n        var deltaOps = InsertOpsConverter_1.InsertOpsConverter.convert(this.rawDeltaOps, this.options);\n        var pairedOps = Grouper_1.Grouper.pairOpsWithTheirBlock(deltaOps);\n        var groupedSameStyleBlocks = Grouper_1.Grouper.groupConsecutiveSameStyleBlocks(pairedOps, {\n            blockquotes: !!this.options.multiLineBlockquote,\n            header: !!this.options.multiLineHeader,\n            codeBlocks: !!this.options.multiLineCodeblock,\n            customBlocks: !!this.options.multiLineCustomBlock,\n        });\n        var groupedOps = Grouper_1.Grouper.reduceConsecutiveSameStyleBlocksToOne(groupedSameStyleBlocks);\n        var tableGrouper = new TableGrouper_1.TableGrouper();\n        groupedOps = tableGrouper.group(groupedOps);\n        var listNester = new ListNester_1.ListNester();\n        return listNester.nest(groupedOps);\n    };\n    QuillDeltaToHtmlConverter.prototype.convert = function () {\n        var _this = this;\n        var groups = this.getGroupedOps();\n        return groups\n            .map(function (group) {\n            if (group instanceof group_types_1.ListGroup) {\n                return _this._renderWithCallbacks(value_types_1.GroupType.List, group, function () {\n                    return _this._renderList(group);\n                });\n            }\n            else if (group instanceof group_types_1.TableGroup) {\n                return _this._renderWithCallbacks(value_types_1.GroupType.Table, group, function () {\n                    return _this._renderTable(group);\n                });\n            }\n            else if (group instanceof group_types_1.BlockGroup) {\n                var g = group;\n                return _this._renderWithCallbacks(value_types_1.GroupType.Block, group, function () {\n                    return _this._renderBlock(g.op, g.ops);\n                });\n            }\n            else if (group instanceof group_types_1.BlotBlock) {\n                return _this._renderCustom(group.op, null);\n            }\n            else if (group instanceof group_types_1.VideoItem) {\n                return _this._renderWithCallbacks(value_types_1.GroupType.Video, group, function () {\n                    var g = group;\n                    var converter = new OpToHtmlConverter_1.OpToHtmlConverter(g.op, _this.converterOptions);\n                    return converter.getHtml();\n                });\n            }\n            else {\n                return _this._renderWithCallbacks(value_types_1.GroupType.InlineGroup, group, function () {\n                    return _this._renderInlines(group.ops, true);\n                });\n            }\n        })\n            .join('');\n    };\n    QuillDeltaToHtmlConverter.prototype._renderWithCallbacks = function (groupType, group, myRenderFn) {\n        var html = '';\n        var beforeCb = this.callbacks['beforeRender_cb'];\n        html =\n            typeof beforeCb === 'function'\n                ? beforeCb.apply(null, [groupType, group])\n                : '';\n        if (!html) {\n            html = myRenderFn();\n        }\n        var afterCb = this.callbacks['afterRender_cb'];\n        html =\n            typeof afterCb === 'function'\n                ? afterCb.apply(null, [groupType, html])\n                : html;\n        return html;\n    };\n    QuillDeltaToHtmlConverter.prototype._renderList = function (list) {\n        var _this = this;\n        var firstItem = list.items[0];\n        return (funcs_html_1.makeStartTag(this._getListTag(firstItem.item.op)) +\n            list.items.map(function (li) { return _this._renderListItem(li); }).join('') +\n            funcs_html_1.makeEndTag(this._getListTag(firstItem.item.op)));\n    };\n    QuillDeltaToHtmlConverter.prototype._renderListItem = function (li) {\n        li.item.op.attributes.indent = 0;\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(li.item.op, this.converterOptions);\n        var parts = converter.getHtmlParts();\n        var liElementsHtml = this._renderInlines(li.item.ops, false);\n        return (parts.openingTag +\n            liElementsHtml +\n            (li.innerList ? this._renderList(li.innerList) : '') +\n            parts.closingTag);\n    };\n    QuillDeltaToHtmlConverter.prototype._renderTable = function (table) {\n        var _this = this;\n        return (funcs_html_1.makeStartTag('table') +\n            funcs_html_1.makeStartTag('tbody') +\n            table.rows.map(function (row) { return _this._renderTableRow(row); }).join('') +\n            funcs_html_1.makeEndTag('tbody') +\n            funcs_html_1.makeEndTag('table'));\n    };\n    QuillDeltaToHtmlConverter.prototype._renderTableRow = function (row) {\n        var _this = this;\n        return (funcs_html_1.makeStartTag('tr') +\n            row.cells.map(function (cell) { return _this._renderTableCell(cell); }).join('') +\n            funcs_html_1.makeEndTag('tr'));\n    };\n    QuillDeltaToHtmlConverter.prototype._renderTableCell = function (cell) {\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(cell.item.op, this.converterOptions);\n        var parts = converter.getHtmlParts();\n        var cellElementsHtml = this._renderInlines(cell.item.ops, false);\n        return (funcs_html_1.makeStartTag('td', {\n            key: 'data-row',\n            value: cell.item.op.attributes.table,\n        }) +\n            parts.openingTag +\n            cellElementsHtml +\n            parts.closingTag +\n            funcs_html_1.makeEndTag('td'));\n    };\n    QuillDeltaToHtmlConverter.prototype._renderBlock = function (bop, ops) {\n        var _this = this;\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(bop, this.converterOptions);\n        var htmlParts = converter.getHtmlParts();\n        if (bop.isCodeBlock()) {\n            return (htmlParts.openingTag +\n                funcs_html_1.encodeHtml(ops\n                    .map(function (iop) {\n                    return iop.isCustomEmbed()\n                        ? _this._renderCustom(iop, bop)\n                        : iop.insert.value;\n                })\n                    .join('')) +\n                htmlParts.closingTag);\n        }\n        var inlines = ops.map(function (op) { return _this._renderInline(op, bop); }).join('');\n        return htmlParts.openingTag + (inlines || BrTag) + htmlParts.closingTag;\n    };\n    QuillDeltaToHtmlConverter.prototype._renderInlines = function (ops, isInlineGroup) {\n        var _this = this;\n        if (isInlineGroup === void 0) { isInlineGroup = true; }\n        var opsLen = ops.length - 1;\n        var html = ops\n            .map(function (op, i) {\n            if (i > 0 && i === opsLen && op.isJustNewline()) {\n                return '';\n            }\n            return _this._renderInline(op, null);\n        })\n            .join('');\n        if (!isInlineGroup) {\n            return html;\n        }\n        var startParaTag = funcs_html_1.makeStartTag(this.options.paragraphTag);\n        var endParaTag = funcs_html_1.makeEndTag(this.options.paragraphTag);\n        if (html === BrTag || this.options.multiLineParagraph) {\n            return startParaTag + html + endParaTag;\n        }\n        return (startParaTag +\n            html\n                .split(BrTag)\n                .map(function (v) {\n                return v === '' ? BrTag : v;\n            })\n                .join(endParaTag + startParaTag) +\n            endParaTag);\n    };\n    QuillDeltaToHtmlConverter.prototype._renderInline = function (op, contextOp) {\n        if (op.isCustomEmbed()) {\n            return this._renderCustom(op, contextOp);\n        }\n        var converter = new OpToHtmlConverter_1.OpToHtmlConverter(op, this.converterOptions);\n        return converter.getHtml().replace(/\\n/g, BrTag);\n    };\n    QuillDeltaToHtmlConverter.prototype._renderCustom = function (op, contextOp) {\n        var renderCb = this.callbacks['renderCustomOp_cb'];\n        if (typeof renderCb === 'function') {\n            return renderCb.apply(null, [op, contextOp]);\n        }\n        return '';\n    };\n    QuillDeltaToHtmlConverter.prototype.beforeRender = function (cb) {\n        if (typeof cb === 'function') {\n            this.callbacks['beforeRender_cb'] = cb;\n        }\n    };\n    QuillDeltaToHtmlConverter.prototype.afterRender = function (cb) {\n        if (typeof cb === 'function') {\n            this.callbacks['afterRender_cb'] = cb;\n        }\n    };\n    QuillDeltaToHtmlConverter.prototype.renderCustomWith = function (cb) {\n        this.callbacks['renderCustomOp_cb'] = cb;\n    };\n    return QuillDeltaToHtmlConverter;\n}());\nexports.QuillDeltaToHtmlConverter = QuillDeltaToHtmlConverter;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar DeltaInsertOp_1 = require(\"./DeltaInsertOp\");\nvar value_types_1 = require(\"./value-types\");\nvar InsertData_1 = require(\"./InsertData\");\nvar OpAttributeSanitizer_1 = require(\"./OpAttributeSanitizer\");\nvar InsertOpDenormalizer_1 = require(\"./InsertOpDenormalizer\");\nvar OpLinkSanitizer_1 = require(\"./OpLinkSanitizer\");\nvar InsertOpsConverter = (function () {\n    function InsertOpsConverter() {\n    }\n    InsertOpsConverter.convert = function (deltaOps, options) {\n        if (!Array.isArray(deltaOps)) {\n            return [];\n        }\n        var denormalizedOps = [].concat.apply([], deltaOps.map(InsertOpDenormalizer_1.InsertOpDenormalizer.denormalize));\n        var results = [];\n        var insertVal, attributes;\n        for (var _i = 0, denormalizedOps_1 = denormalizedOps; _i < denormalizedOps_1.length; _i++) {\n            var op = denormalizedOps_1[_i];\n            if (!op.insert) {\n                continue;\n            }\n            insertVal = InsertOpsConverter.convertInsertVal(op.insert, options);\n            if (!insertVal) {\n                continue;\n            }\n            attributes = OpAttributeSanitizer_1.OpAttributeSanitizer.sanitize(op.attributes, options);\n            results.push(new DeltaInsertOp_1.DeltaInsertOp(insertVal, attributes));\n        }\n        return results;\n    };\n    InsertOpsConverter.convertInsertVal = function (insertPropVal, sanitizeOptions) {\n        if (typeof insertPropVal === 'string') {\n            return new InsertData_1.InsertDataQuill(value_types_1.DataType.Text, insertPropVal);\n        }\n        if (!insertPropVal || typeof insertPropVal !== 'object') {\n            return null;\n        }\n        var keys = Object.keys(insertPropVal);\n        if (!keys.length) {\n            return null;\n        }\n        return value_types_1.DataType.Image in insertPropVal\n            ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Image, OpLinkSanitizer_1.OpLinkSanitizer.sanitize(insertPropVal[value_types_1.DataType.Image] + '', sanitizeOptions))\n            : value_types_1.DataType.Video in insertPropVal\n                ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Video, OpLinkSanitizer_1.OpLinkSanitizer.sanitize(insertPropVal[value_types_1.DataType.Video] + '', sanitizeOptions))\n                : value_types_1.DataType.Formula in insertPropVal\n                    ? new InsertData_1.InsertDataQuill(value_types_1.DataType.Formula, insertPropVal[value_types_1.DataType.Formula])\n                    :\n                        new InsertData_1.InsertDataCustom(keys[0], insertPropVal[keys[0]]);\n    };\n    return InsertOpsConverter;\n}());\nexports.InsertOpsConverter = InsertOpsConverter;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar value_types_1 = require(\"./value-types\");\nvar InsertData_1 = require(\"./InsertData\");\nvar lodash_isequal_1 = __importDefault(require(\"lodash.isequal\"));\nvar DeltaInsertOp = (function () {\n    function DeltaInsertOp(insertVal, attrs) {\n        if (typeof insertVal === 'string') {\n            insertVal = new InsertData_1.InsertDataQuill(value_types_1.DataType.Text, insertVal + '');\n        }\n        this.insert = insertVal;\n        this.attributes = attrs || {};\n    }\n    DeltaInsertOp.createNewLineOp = function () {\n        return new DeltaInsertOp(value_types_1.NewLine);\n    };\n    DeltaInsertOp.prototype.isContainerBlock = function () {\n        return (this.isBlockquote() ||\n            this.isList() ||\n            this.isTable() ||\n            this.isCodeBlock() ||\n            this.isHeader() ||\n            this.isBlockAttribute() ||\n            this.isCustomTextBlock());\n    };\n    DeltaInsertOp.prototype.isBlockAttribute = function () {\n        var attrs = this.attributes;\n        return !!(attrs.align || attrs.direction || attrs.indent);\n    };\n    DeltaInsertOp.prototype.isBlockquote = function () {\n        return !!this.attributes.blockquote;\n    };\n    DeltaInsertOp.prototype.isHeader = function () {\n        return !!this.attributes.header;\n    };\n    DeltaInsertOp.prototype.isTable = function () {\n        return !!this.attributes.table;\n    };\n    DeltaInsertOp.prototype.isSameHeaderAs = function (op) {\n        return op.attributes.header === this.attributes.header && this.isHeader();\n    };\n    DeltaInsertOp.prototype.hasSameAdiAs = function (op) {\n        return (this.attributes.align === op.attributes.align &&\n            this.attributes.direction === op.attributes.direction &&\n            this.attributes.indent === op.attributes.indent);\n    };\n    DeltaInsertOp.prototype.hasSameIndentationAs = function (op) {\n        return this.attributes.indent === op.attributes.indent;\n    };\n    DeltaInsertOp.prototype.hasSameAttr = function (op) {\n        return lodash_isequal_1.default(this.attributes, op.attributes);\n    };\n    DeltaInsertOp.prototype.hasHigherIndentThan = function (op) {\n        return ((Number(this.attributes.indent) || 0) >\n            (Number(op.attributes.indent) || 0));\n    };\n    DeltaInsertOp.prototype.isInline = function () {\n        return !(this.isContainerBlock() ||\n            this.isVideo() ||\n            this.isCustomEmbedBlock());\n    };\n    DeltaInsertOp.prototype.isCodeBlock = function () {\n        return !!this.attributes['code-block'];\n    };\n    DeltaInsertOp.prototype.hasSameLangAs = function (op) {\n        return this.attributes['code-block'] === op.attributes['code-block'];\n    };\n    DeltaInsertOp.prototype.isJustNewline = function () {\n        return this.insert.value === value_types_1.NewLine;\n    };\n    DeltaInsertOp.prototype.isList = function () {\n        return (this.isOrderedList() ||\n            this.isBulletList() ||\n            this.isCheckedList() ||\n            this.isUncheckedList());\n    };\n    DeltaInsertOp.prototype.isOrderedList = function () {\n        return this.attributes.list === value_types_1.ListType.Ordered;\n    };\n    DeltaInsertOp.prototype.isBulletList = function () {\n        return this.attributes.list === value_types_1.ListType.Bullet;\n    };\n    DeltaInsertOp.prototype.isCheckedList = function () {\n        return this.attributes.list === value_types_1.ListType.Checked;\n    };\n    DeltaInsertOp.prototype.isUncheckedList = function () {\n        return this.attributes.list === value_types_1.ListType.Unchecked;\n    };\n    DeltaInsertOp.prototype.isACheckList = function () {\n        return (this.attributes.list == value_types_1.ListType.Unchecked ||\n            this.attributes.list === value_types_1.ListType.Checked);\n    };\n    DeltaInsertOp.prototype.isSameListAs = function (op) {\n        return (!!op.attributes.list &&\n            (this.attributes.list === op.attributes.list ||\n                (op.isACheckList() && this.isACheckList())));\n    };\n    DeltaInsertOp.prototype.isSameTableRowAs = function (op) {\n        return (!!op.isTable() &&\n            this.isTable() &&\n            this.attributes.table === op.attributes.table);\n    };\n    DeltaInsertOp.prototype.isText = function () {\n        return this.insert.type === value_types_1.DataType.Text;\n    };\n    DeltaInsertOp.prototype.isImage = function () {\n        return this.insert.type === value_types_1.DataType.Image;\n    };\n    DeltaInsertOp.prototype.isFormula = function () {\n        return this.insert.type === value_types_1.DataType.Formula;\n    };\n    DeltaInsertOp.prototype.isVideo = function () {\n        return this.insert.type === value_types_1.DataType.Video;\n    };\n    DeltaInsertOp.prototype.isLink = function () {\n        return this.isText() && !!this.attributes.link;\n    };\n    DeltaInsertOp.prototype.isCustomEmbed = function () {\n        return this.insert instanceof InsertData_1.InsertDataCustom;\n    };\n    DeltaInsertOp.prototype.isCustomEmbedBlock = function () {\n        return this.isCustomEmbed() && !!this.attributes.renderAsBlock;\n    };\n    DeltaInsertOp.prototype.isCustomTextBlock = function () {\n        return this.isText() && !!this.attributes.renderAsBlock;\n    };\n    DeltaInsertOp.prototype.isMentions = function () {\n        return this.isText() && !!this.attributes.mentions;\n    };\n    return DeltaInsertOp;\n}());\nexports.DeltaInsertOp = DeltaInsertOp;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar NewLine = '\\n';\nexports.NewLine = NewLine;\nvar ListType;\n(function (ListType) {\n    ListType[\"Ordered\"] = \"ordered\";\n    ListType[\"Bullet\"] = \"bullet\";\n    ListType[\"Checked\"] = \"checked\";\n    ListType[\"Unchecked\"] = \"unchecked\";\n})(ListType || (ListType = {}));\nexports.ListType = ListType;\nvar ScriptType;\n(function (ScriptType) {\n    ScriptType[\"Sub\"] = \"sub\";\n    ScriptType[\"Super\"] = \"super\";\n})(ScriptType || (ScriptType = {}));\nexports.ScriptType = ScriptType;\nvar DirectionType;\n(function (DirectionType) {\n    DirectionType[\"Rtl\"] = \"rtl\";\n})(DirectionType || (DirectionType = {}));\nexports.DirectionType = DirectionType;\nvar AlignType;\n(function (AlignType) {\n    AlignType[\"Left\"] = \"left\";\n    AlignType[\"Center\"] = \"center\";\n    AlignType[\"Right\"] = \"right\";\n    AlignType[\"Justify\"] = \"justify\";\n})(AlignType || (AlignType = {}));\nexports.AlignType = AlignType;\nvar DataType;\n(function (DataType) {\n    DataType[\"Image\"] = \"image\";\n    DataType[\"Video\"] = \"video\";\n    DataType[\"Formula\"] = \"formula\";\n    DataType[\"Text\"] = \"text\";\n})(DataType || (DataType = {}));\nexports.DataType = DataType;\nvar GroupType;\n(function (GroupType) {\n    GroupType[\"Block\"] = \"block\";\n    GroupType[\"InlineGroup\"] = \"inline-group\";\n    GroupType[\"List\"] = \"list\";\n    GroupType[\"Video\"] = \"video\";\n    GroupType[\"Table\"] = \"table\";\n})(GroupType || (GroupType = {}));\nexports.GroupType = GroupType;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar InsertDataQuill = (function () {\n    function InsertDataQuill(type, value) {\n        this.type = type;\n        this.value = value;\n    }\n    return InsertDataQuill;\n}());\nexports.InsertDataQuill = InsertDataQuill;\nvar InsertDataCustom = (function () {\n    function InsertDataCustom(type, value) {\n        this.type = type;\n        this.value = value;\n    }\n    return InsertDataCustom;\n}());\nexports.InsertDataCustom = InsertDataCustom;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar value_types_1 = require(\"./value-types\");\nvar MentionSanitizer_1 = require(\"./mentions/MentionSanitizer\");\nvar array_1 = require(\"./helpers/array\");\nvar OpLinkSanitizer_1 = require(\"./OpLinkSanitizer\");\nvar OpAttributeSanitizer = (function () {\n    function OpAttributeSanitizer() {\n    }\n    OpAttributeSanitizer.sanitize = function (dirtyAttrs, sanitizeOptions) {\n        var cleanAttrs = {};\n        if (!dirtyAttrs || typeof dirtyAttrs !== 'object') {\n            return cleanAttrs;\n        }\n        var booleanAttrs = [\n            'bold',\n            'italic',\n            'underline',\n            'strike',\n            'code',\n            'blockquote',\n            'code-block',\n            'renderAsBlock',\n        ];\n        var colorAttrs = ['background', 'color'];\n        var font = dirtyAttrs.font, size = dirtyAttrs.size, link = dirtyAttrs.link, script = dirtyAttrs.script, list = dirtyAttrs.list, header = dirtyAttrs.header, align = dirtyAttrs.align, direction = dirtyAttrs.direction, indent = dirtyAttrs.indent, mentions = dirtyAttrs.mentions, mention = dirtyAttrs.mention, width = dirtyAttrs.width, target = dirtyAttrs.target, rel = dirtyAttrs.rel;\n        var codeBlock = dirtyAttrs['code-block'];\n        var sanitizedAttrs = booleanAttrs.concat(colorAttrs, [\n            'font',\n            'size',\n            'link',\n            'script',\n            'list',\n            'header',\n            'align',\n            'direction',\n            'indent',\n            'mentions',\n            'mention',\n            'width',\n            'target',\n            'rel',\n            'code-block',\n        ]);\n        booleanAttrs.forEach(function (prop) {\n            var v = dirtyAttrs[prop];\n            if (v) {\n                cleanAttrs[prop] = !!v;\n            }\n        });\n        colorAttrs.forEach(function (prop) {\n            var val = dirtyAttrs[prop];\n            if (val &&\n                (OpAttributeSanitizer.IsValidHexColor(val + '') ||\n                    OpAttributeSanitizer.IsValidColorLiteral(val + '') ||\n                    OpAttributeSanitizer.IsValidRGBColor(val + ''))) {\n                cleanAttrs[prop] = val;\n            }\n        });\n        if (font && OpAttributeSanitizer.IsValidFontName(font + '')) {\n            cleanAttrs.font = font;\n        }\n        if (size && OpAttributeSanitizer.IsValidSize(size + '')) {\n            cleanAttrs.size = size;\n        }\n        if (width && OpAttributeSanitizer.IsValidWidth(width + '')) {\n            cleanAttrs.width = width;\n        }\n        if (link) {\n            cleanAttrs.link = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(link + '', sanitizeOptions);\n        }\n        if (target && OpAttributeSanitizer.isValidTarget(target)) {\n            cleanAttrs.target = target;\n        }\n        if (rel && OpAttributeSanitizer.IsValidRel(rel)) {\n            cleanAttrs.rel = rel;\n        }\n        if (codeBlock) {\n            if (OpAttributeSanitizer.IsValidLang(codeBlock)) {\n                cleanAttrs['code-block'] = codeBlock;\n            }\n            else {\n                cleanAttrs['code-block'] = !!codeBlock;\n            }\n        }\n        if (script === value_types_1.ScriptType.Sub || value_types_1.ScriptType.Super === script) {\n            cleanAttrs.script = script;\n        }\n        if (list === value_types_1.ListType.Bullet ||\n            list === value_types_1.ListType.Ordered ||\n            list === value_types_1.ListType.Checked ||\n            list === value_types_1.ListType.Unchecked) {\n            cleanAttrs.list = list;\n        }\n        if (Number(header)) {\n            cleanAttrs.header = Math.min(Number(header), 6);\n        }\n        if (array_1.find([value_types_1.AlignType.Center, value_types_1.AlignType.Right, value_types_1.AlignType.Justify, value_types_1.AlignType.Left], function (a) { return a === align; })) {\n            cleanAttrs.align = align;\n        }\n        if (direction === value_types_1.DirectionType.Rtl) {\n            cleanAttrs.direction = direction;\n        }\n        if (indent && Number(indent)) {\n            cleanAttrs.indent = Math.min(Number(indent), 30);\n        }\n        if (mentions && mention) {\n            var sanitizedMention = MentionSanitizer_1.MentionSanitizer.sanitize(mention, sanitizeOptions);\n            if (Object.keys(sanitizedMention).length > 0) {\n                cleanAttrs.mentions = !!mentions;\n                cleanAttrs.mention = mention;\n            }\n        }\n        return Object.keys(dirtyAttrs).reduce(function (cleaned, k) {\n            if (sanitizedAttrs.indexOf(k) === -1) {\n                cleaned[k] = dirtyAttrs[k];\n            }\n            return cleaned;\n        }, cleanAttrs);\n    };\n    OpAttributeSanitizer.IsValidHexColor = function (colorStr) {\n        return !!colorStr.match(/^#([0-9A-F]{6}|[0-9A-F]{3})$/i);\n    };\n    OpAttributeSanitizer.IsValidColorLiteral = function (colorStr) {\n        return !!colorStr.match(/^[a-z]{1,50}$/i);\n    };\n    OpAttributeSanitizer.IsValidRGBColor = function (colorStr) {\n        var re = /^rgb\\(((0|25[0-5]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),\\s*){2}(0|25[0-5]|2[0-4]\\d|1\\d\\d|0?\\d?\\d)\\)$/i;\n        return !!colorStr.match(re);\n    };\n    OpAttributeSanitizer.IsValidFontName = function (fontName) {\n        return !!fontName.match(/^[a-z\\s0-9\\- ]{1,30}$/i);\n    };\n    OpAttributeSanitizer.IsValidSize = function (size) {\n        return !!size.match(/^[a-z0-9\\-]{1,20}$/i);\n    };\n    OpAttributeSanitizer.IsValidWidth = function (width) {\n        return !!width.match(/^[0-9]*(px|em|%)?$/);\n    };\n    OpAttributeSanitizer.isValidTarget = function (target) {\n        return !!target.match(/^[_a-zA-Z0-9\\-]{1,50}$/);\n    };\n    OpAttributeSanitizer.IsValidRel = function (relStr) {\n        return !!relStr.match(/^[a-zA-Z\\s\\-]{1,250}$/i);\n    };\n    OpAttributeSanitizer.IsValidLang = function (lang) {\n        if (typeof lang === 'boolean') {\n            return true;\n        }\n        return !!lang.match(/^[a-zA-Z\\s\\-\\\\\\/\\+]{1,50}$/i);\n    };\n    return OpAttributeSanitizer;\n}());\nexports.OpAttributeSanitizer = OpAttributeSanitizer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar OpLinkSanitizer_1 = require(\"../OpLinkSanitizer\");\nvar MentionSanitizer = (function () {\n    function MentionSanitizer() {\n    }\n    MentionSanitizer.sanitize = function (dirtyObj, sanitizeOptions) {\n        var cleanObj = {};\n        if (!dirtyObj || typeof dirtyObj !== 'object') {\n            return cleanObj;\n        }\n        if (dirtyObj.class && MentionSanitizer.IsValidClass(dirtyObj.class)) {\n            cleanObj.class = dirtyObj.class;\n        }\n        if (dirtyObj.id && MentionSanitizer.IsValidId(dirtyObj.id)) {\n            cleanObj.id = dirtyObj.id;\n        }\n        if (MentionSanitizer.IsValidTarget(dirtyObj.target + '')) {\n            cleanObj.target = dirtyObj.target;\n        }\n        if (dirtyObj.avatar) {\n            cleanObj.avatar = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(dirtyObj.avatar + '', sanitizeOptions);\n        }\n        if (dirtyObj['end-point']) {\n            cleanObj['end-point'] = OpLinkSanitizer_1.OpLinkSanitizer.sanitize(dirtyObj['end-point'] + '', sanitizeOptions);\n        }\n        if (dirtyObj.slug) {\n            cleanObj.slug = dirtyObj.slug + '';\n        }\n        return cleanObj;\n    };\n    MentionSanitizer.IsValidClass = function (classAttr) {\n        return !!classAttr.match(/^[a-zA-Z0-9_\\-]{1,500}$/i);\n    };\n    MentionSanitizer.IsValidId = function (idAttr) {\n        return !!idAttr.match(/^[a-zA-Z0-9_\\-\\:\\.]{1,500}$/i);\n    };\n    MentionSanitizer.IsValidTarget = function (target) {\n        return ['_self', '_blank', '_parent', '_top'].indexOf(target) > -1;\n    };\n    return MentionSanitizer;\n}());\nexports.MentionSanitizer = MentionSanitizer;\n", "\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar url = __importStar(require(\"./helpers/url\"));\nvar funcs_html_1 = require(\"./funcs-html\");\nvar OpLinkSanitizer = (function () {\n    function OpLinkSanitizer() {\n    }\n    OpLinkSanitizer.sanitize = function (link, options) {\n        var sanitizerFn = function () {\n            return undefined;\n        };\n        if (options && typeof options.urlSanitizer === 'function') {\n            sanitizerFn = options.urlSanitizer;\n        }\n        var result = sanitizerFn(link);\n        return typeof result === 'string' ? result : funcs_html_1.encodeLink(url.sanitize(link));\n    };\n    return OpLinkSanitizer;\n}());\nexports.OpLinkSanitizer = OpLinkSanitizer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction sanitize(str) {\n    var val = str;\n    val = val.replace(/^\\s*/gm, '');\n    var whiteList = /^((https?|s?ftp|file|blob|mailto|tel):|#|\\/|data:image\\/)/;\n    if (whiteList.test(val)) {\n        return val;\n    }\n    return 'unsafe:' + val;\n}\nexports.sanitize = sanitize;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar EncodeTarget;\n(function (EncodeTarget) {\n    EncodeTarget[EncodeTarget[\"Html\"] = 0] = \"Html\";\n    EncodeTarget[EncodeTarget[\"Url\"] = 1] = \"Url\";\n})(EncodeTarget || (EncodeTarget = {}));\nfunction makeStartTag(tag, attrs) {\n    if (attrs === void 0) { attrs = undefined; }\n    if (!tag) {\n        return '';\n    }\n    var attrsStr = '';\n    if (attrs) {\n        var arrAttrs = [].concat(attrs);\n        attrsStr = arrAttrs\n            .map(function (attr) {\n            return attr.key + (attr.value ? '=\"' + attr.value + '\"' : '');\n        })\n            .join(' ');\n    }\n    var closing = '>';\n    if (tag === 'img' || tag === 'br') {\n        closing = '/>';\n    }\n    return attrsStr ? \"<\" + tag + \" \" + attrsStr + closing : \"<\" + tag + closing;\n}\nexports.makeStartTag = makeStartTag;\nfunction makeEndTag(tag) {\n    if (tag === void 0) { tag = ''; }\n    return (tag && \"</\" + tag + \">\") || '';\n}\nexports.makeEndTag = makeEndTag;\nfunction decodeHtml(str) {\n    return encodeMappings(EncodeTarget.Html).reduce(decodeMapping, str);\n}\nexports.decodeHtml = decodeHtml;\nfunction encodeHtml(str, preventDoubleEncoding) {\n    if (preventDoubleEncoding === void 0) { preventDoubleEncoding = true; }\n    if (preventDoubleEncoding) {\n        str = decodeHtml(str);\n    }\n    return encodeMappings(EncodeTarget.Html).reduce(encodeMapping, str);\n}\nexports.encodeHtml = encodeHtml;\nfunction encodeLink(str) {\n    var linkMaps = encodeMappings(EncodeTarget.Url);\n    var decoded = linkMaps.reduce(decodeMapping, str);\n    return linkMaps.reduce(encodeMapping, decoded);\n}\nexports.encodeLink = encodeLink;\nfunction encodeMappings(mtype) {\n    var maps = [\n        ['&', '&amp;'],\n        ['<', '&lt;'],\n        ['>', '&gt;'],\n        ['\"', '&quot;'],\n        [\"'\", '&#x27;'],\n        ['\\\\/', '&#x2F;'],\n        ['\\\\(', '&#40;'],\n        ['\\\\)', '&#41;'],\n    ];\n    if (mtype === EncodeTarget.Html) {\n        return maps.filter(function (_a) {\n            var v = _a[0], _ = _a[1];\n            return v.indexOf('(') === -1 && v.indexOf(')') === -1;\n        });\n    }\n    else {\n        return maps.filter(function (_a) {\n            var v = _a[0], _ = _a[1];\n            return v.indexOf('/') === -1;\n        });\n    }\n}\nfunction encodeMapping(str, mapping) {\n    return str.replace(new RegExp(mapping[0], 'g'), mapping[1]);\n}\nfunction decodeMapping(str, mapping) {\n    return str.replace(new RegExp(mapping[1], 'g'), mapping[0].replace('\\\\', ''));\n}\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction preferSecond(arr) {\n    if (arr.length === 0) {\n        return null;\n    }\n    return arr.length >= 2 ? arr[1] : arr[0];\n}\nexports.preferSecond = preferSecond;\nfunction flatten(arr) {\n    return arr.reduce(function (pv, v) {\n        return pv.concat(Array.isArray(v) ? flatten(v) : v);\n    }, []);\n}\nexports.flatten = flatten;\nfunction find(arr, predicate) {\n    if (Array.prototype.find) {\n        return Array.prototype.find.call(arr, predicate);\n    }\n    for (var i = 0; i < arr.length; i++) {\n        if (predicate(arr[i]))\n            return arr[i];\n    }\n    return undefined;\n}\nexports.find = find;\nfunction groupConsecutiveElementsWhile(arr, predicate) {\n    var groups = [];\n    var currElm, currGroup;\n    for (var i = 0; i < arr.length; i++) {\n        currElm = arr[i];\n        if (i > 0 && predicate(currElm, arr[i - 1])) {\n            currGroup = groups[groups.length - 1];\n            currGroup.push(currElm);\n        }\n        else {\n            groups.push([currElm]);\n        }\n    }\n    return groups.map(function (g) { return (g.length === 1 ? g[0] : g); });\n}\nexports.groupConsecutiveElementsWhile = groupConsecutiveElementsWhile;\nfunction sliceFromReverseWhile(arr, startIndex, predicate) {\n    var result = {\n        elements: [],\n        sliceStartsAt: -1,\n    };\n    for (var i = startIndex; i >= 0; i--) {\n        if (!predicate(arr[i])) {\n            break;\n        }\n        result.sliceStartsAt = i;\n        result.elements.unshift(arr[i]);\n    }\n    return result;\n}\nexports.sliceFromReverseWhile = sliceFromReverseWhile;\nfunction intersperse(arr, item) {\n    return arr.reduce(function (pv, v, index) {\n        pv.push(v);\n        if (index < arr.length - 1) {\n            pv.push(item);\n        }\n        return pv;\n    }, []);\n}\nexports.intersperse = intersperse;\n", "\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar value_types_1 = require(\"./value-types\");\nvar str = __importStar(require(\"./helpers/string\"));\nvar obj = __importStar(require(\"./helpers/object\"));\nvar InsertOpDenormalizer = (function () {\n    function InsertOpDenormalizer() {\n    }\n    InsertOpDenormalizer.denormalize = function (op) {\n        if (!op || typeof op !== 'object') {\n            return [];\n        }\n        if (typeof op.insert === 'object' || op.insert === value_types_1.NewLine) {\n            return [op];\n        }\n        var newlinedArray = str.tokenizeWithNewLines(op.insert + '');\n        if (newlinedArray.length === 1) {\n            return [op];\n        }\n        var nlObj = obj.assign({}, op, { insert: value_types_1.NewLine });\n        return newlinedArray.map(function (line) {\n            if (line === value_types_1.NewLine) {\n                return nlObj;\n            }\n            return obj.assign({}, op, {\n                insert: line,\n            });\n        });\n    };\n    return InsertOpDenormalizer;\n}());\nexports.InsertOpDenormalizer = InsertOpDenormalizer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction tokenizeWithNewLines(str) {\n    var NewLine = '\\n';\n    if (str === NewLine) {\n        return [str];\n    }\n    var lines = str.split(NewLine);\n    if (lines.length === 1) {\n        return lines;\n    }\n    var lastIndex = lines.length - 1;\n    return lines.reduce(function (pv, line, ind) {\n        if (ind !== lastIndex) {\n            if (line !== '') {\n                pv = pv.concat(line, NewLine);\n            }\n            else {\n                pv.push(NewLine);\n            }\n        }\n        else if (line !== '') {\n            pv.push(line);\n        }\n        return pv;\n    }, []);\n}\nexports.tokenizeWithNewLines = tokenizeWithNewLines;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction assign(target) {\n    var sources = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        sources[_i - 1] = arguments[_i];\n    }\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 0; index < sources.length; index++) {\n        var nextSource = sources[index];\n        if (nextSource != null) {\n            for (var nextKey in nextSource) {\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\nexports.assign = assign;\n", "\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar funcs_html_1 = require(\"./funcs-html\");\nvar value_types_1 = require(\"./value-types\");\nvar obj = __importStar(require(\"./helpers/object\"));\nvar arr = __importStar(require(\"./helpers/array\"));\nvar OpAttributeSanitizer_1 = require(\"./OpAttributeSanitizer\");\nvar DEFAULT_INLINE_FONTS = {\n    serif: 'font-family: Georgia, Times New Roman, serif',\n    monospace: 'font-family: Monaco, Courier New, monospace',\n};\nexports.DEFAULT_INLINE_STYLES = {\n    font: function (value) { return DEFAULT_INLINE_FONTS[value] || 'font-family:' + value; },\n    size: {\n        small: 'font-size: 0.75em',\n        large: 'font-size: 1.5em',\n        huge: 'font-size: 2.5em',\n    },\n    indent: function (value, op) {\n        var indentSize = parseInt(value, 10) * 3;\n        var side = op.attributes['direction'] === 'rtl' ? 'right' : 'left';\n        return 'padding-' + side + ':' + indentSize + 'em';\n    },\n    direction: function (value, op) {\n        if (value === 'rtl') {\n            return ('direction:rtl' + (op.attributes['align'] ? '' : '; text-align:inherit'));\n        }\n        else {\n            return undefined;\n        }\n    },\n};\nvar OpToHtmlConverter = (function () {\n    function OpToHtmlConverter(op, options) {\n        this.op = op;\n        this.options = obj.assign({}, {\n            classPrefix: 'ql',\n            inlineStyles: undefined,\n            encodeHtml: true,\n            listItemTag: 'li',\n            paragraphTag: 'p',\n        }, options);\n    }\n    OpToHtmlConverter.prototype.prefixClass = function (className) {\n        if (!this.options.classPrefix) {\n            return className + '';\n        }\n        return this.options.classPrefix + '-' + className;\n    };\n    OpToHtmlConverter.prototype.getHtml = function () {\n        var parts = this.getHtmlParts();\n        return parts.openingTag + parts.content + parts.closingTag;\n    };\n    OpToHtmlConverter.prototype.getHtmlParts = function () {\n        var _this = this;\n        if (this.op.isJustNewline() && !this.op.isContainerBlock()) {\n            return { openingTag: '', closingTag: '', content: value_types_1.NewLine };\n        }\n        var tags = this.getTags(), attrs = this.getTagAttributes();\n        if (!tags.length && attrs.length) {\n            tags.push('span');\n        }\n        var beginTags = [], endTags = [];\n        var imgTag = 'img';\n        var isImageLink = function (tag) {\n            return tag === imgTag && !!_this.op.attributes.link;\n        };\n        for (var _i = 0, tags_1 = tags; _i < tags_1.length; _i++) {\n            var tag = tags_1[_i];\n            if (isImageLink(tag)) {\n                beginTags.push(funcs_html_1.makeStartTag('a', this.getLinkAttrs()));\n            }\n            beginTags.push(funcs_html_1.makeStartTag(tag, attrs));\n            endTags.push(tag === 'img' ? '' : funcs_html_1.makeEndTag(tag));\n            if (isImageLink(tag)) {\n                endTags.push(funcs_html_1.makeEndTag('a'));\n            }\n            attrs = [];\n        }\n        endTags.reverse();\n        return {\n            openingTag: beginTags.join(''),\n            content: this.getContent(),\n            closingTag: endTags.join(''),\n        };\n    };\n    OpToHtmlConverter.prototype.getContent = function () {\n        if (this.op.isContainerBlock()) {\n            return '';\n        }\n        if (this.op.isMentions()) {\n            return this.op.insert.value;\n        }\n        var content = this.op.isFormula() || this.op.isText() ? this.op.insert.value : '';\n        return (this.options.encodeHtml && funcs_html_1.encodeHtml(content)) || content;\n    };\n    OpToHtmlConverter.prototype.getCssClasses = function () {\n        var attrs = this.op.attributes;\n        if (this.options.inlineStyles) {\n            return [];\n        }\n        var propsArr = ['indent', 'align', 'direction', 'font', 'size'];\n        if (this.options.allowBackgroundClasses) {\n            propsArr.push('background');\n        }\n        return (this.getCustomCssClasses() || []).concat(propsArr\n            .filter(function (prop) { return !!attrs[prop]; })\n            .filter(function (prop) {\n            return prop === 'background'\n                ? OpAttributeSanitizer_1.OpAttributeSanitizer.IsValidColorLiteral(attrs[prop])\n                : true;\n        })\n            .map(function (prop) { return prop + '-' + attrs[prop]; })\n            .concat(this.op.isFormula() ? 'formula' : [])\n            .concat(this.op.isVideo() ? 'video' : [])\n            .concat(this.op.isImage() ? 'image' : [])\n            .map(this.prefixClass.bind(this)));\n    };\n    OpToHtmlConverter.prototype.getCssStyles = function () {\n        var _this = this;\n        var attrs = this.op.attributes;\n        var propsArr = [['color']];\n        if (!!this.options.inlineStyles || !this.options.allowBackgroundClasses) {\n            propsArr.push(['background', 'background-color']);\n        }\n        if (this.options.inlineStyles) {\n            propsArr = propsArr.concat([\n                ['indent'],\n                ['align', 'text-align'],\n                ['direction'],\n                ['font', 'font-family'],\n                ['size'],\n            ]);\n        }\n        return (this.getCustomCssStyles() || [])\n            .concat(propsArr\n            .filter(function (item) { return !!attrs[item[0]]; })\n            .map(function (item) {\n            var attribute = item[0];\n            var attrValue = attrs[attribute];\n            var attributeConverter = (_this.options.inlineStyles &&\n                _this.options.inlineStyles[attribute]) ||\n                exports.DEFAULT_INLINE_STYLES[attribute];\n            if (typeof attributeConverter === 'object') {\n                return attributeConverter[attrValue];\n            }\n            else if (typeof attributeConverter === 'function') {\n                var converterFn = attributeConverter;\n                return converterFn(attrValue, _this.op);\n            }\n            else {\n                return arr.preferSecond(item) + ':' + attrValue;\n            }\n        }))\n            .filter(function (item) { return item !== undefined; });\n    };\n    OpToHtmlConverter.prototype.getTagAttributes = function () {\n        if (this.op.attributes.code && !this.op.isLink()) {\n            return [];\n        }\n        var makeAttr = this.makeAttr.bind(this);\n        var customTagAttributes = this.getCustomTagAttributes();\n        var customAttr = customTagAttributes\n            ? Object.keys(this.getCustomTagAttributes()).map(function (k) {\n                return makeAttr(k, customTagAttributes[k]);\n            })\n            : [];\n        var classes = this.getCssClasses();\n        var tagAttrs = classes.length\n            ? customAttr.concat([makeAttr('class', classes.join(' '))])\n            : customAttr;\n        if (this.op.isImage()) {\n            this.op.attributes.width &&\n                (tagAttrs = tagAttrs.concat(makeAttr('width', this.op.attributes.width)));\n            return tagAttrs.concat(makeAttr('src', this.op.insert.value));\n        }\n        if (this.op.isACheckList()) {\n            return tagAttrs.concat(makeAttr('data-checked', this.op.isCheckedList() ? 'true' : 'false'));\n        }\n        if (this.op.isFormula()) {\n            return tagAttrs;\n        }\n        if (this.op.isVideo()) {\n            return tagAttrs.concat(makeAttr('frameborder', '0'), makeAttr('allowfullscreen', 'true'), makeAttr('src', this.op.insert.value));\n        }\n        if (this.op.isMentions()) {\n            var mention = this.op.attributes.mention;\n            if (mention.class) {\n                tagAttrs = tagAttrs.concat(makeAttr('class', mention.class));\n            }\n            if (mention['end-point'] && mention.slug) {\n                tagAttrs = tagAttrs.concat(makeAttr('href', mention['end-point'] + '/' + mention.slug));\n            }\n            else {\n                tagAttrs = tagAttrs.concat(makeAttr('href', 'about:blank'));\n            }\n            if (mention.target) {\n                tagAttrs = tagAttrs.concat(makeAttr('target', mention.target));\n            }\n            return tagAttrs;\n        }\n        var styles = this.getCssStyles();\n        if (styles.length) {\n            tagAttrs.push(makeAttr('style', styles.join(';')));\n        }\n        if (this.op.isCodeBlock() &&\n            typeof this.op.attributes['code-block'] === 'string') {\n            return tagAttrs.concat(makeAttr('data-language', this.op.attributes['code-block']));\n        }\n        if (this.op.isContainerBlock()) {\n            return tagAttrs;\n        }\n        if (this.op.isLink()) {\n            tagAttrs = tagAttrs.concat(this.getLinkAttrs());\n        }\n        return tagAttrs;\n    };\n    OpToHtmlConverter.prototype.makeAttr = function (k, v) {\n        return { key: k, value: v };\n    };\n    OpToHtmlConverter.prototype.getLinkAttrs = function () {\n        var tagAttrs = [];\n        var targetForAll = OpAttributeSanitizer_1.OpAttributeSanitizer.isValidTarget(this.options.linkTarget || '')\n            ? this.options.linkTarget\n            : undefined;\n        var relForAll = OpAttributeSanitizer_1.OpAttributeSanitizer.IsValidRel(this.options.linkRel || '')\n            ? this.options.linkRel\n            : undefined;\n        var target = this.op.attributes.target || targetForAll;\n        var rel = this.op.attributes.rel || relForAll;\n        return tagAttrs\n            .concat(this.makeAttr('href', this.op.attributes.link))\n            .concat(target ? this.makeAttr('target', target) : [])\n            .concat(rel ? this.makeAttr('rel', rel) : []);\n    };\n    OpToHtmlConverter.prototype.getCustomTag = function (format) {\n        if (this.options.customTag &&\n            typeof this.options.customTag === 'function') {\n            return this.options.customTag.apply(null, [format, this.op]);\n        }\n    };\n    OpToHtmlConverter.prototype.getCustomTagAttributes = function () {\n        if (this.options.customTagAttributes &&\n            typeof this.options.customTagAttributes === 'function') {\n            return this.options.customTagAttributes.apply(null, [this.op]);\n        }\n    };\n    OpToHtmlConverter.prototype.getCustomCssClasses = function () {\n        if (this.options.customCssClasses &&\n            typeof this.options.customCssClasses === 'function') {\n            var res = this.options.customCssClasses.apply(null, [this.op]);\n            if (res) {\n                return Array.isArray(res) ? res : [res];\n            }\n        }\n    };\n    OpToHtmlConverter.prototype.getCustomCssStyles = function () {\n        if (this.options.customCssStyles &&\n            typeof this.options.customCssStyles === 'function') {\n            var res = this.options.customCssStyles.apply(null, [this.op]);\n            if (res) {\n                return Array.isArray(res) ? res : [res];\n            }\n        }\n    };\n    OpToHtmlConverter.prototype.getTags = function () {\n        var _this = this;\n        var attrs = this.op.attributes;\n        if (!this.op.isText()) {\n            return [\n                this.op.isVideo() ? 'iframe' : this.op.isImage() ? 'img' : 'span',\n            ];\n        }\n        var positionTag = this.options.paragraphTag || 'p';\n        var blocks = [\n            ['blockquote'],\n            ['code-block', 'pre'],\n            ['list', this.options.listItemTag],\n            ['header'],\n            ['align', positionTag],\n            ['direction', positionTag],\n            ['indent', positionTag],\n        ];\n        for (var _i = 0, blocks_1 = blocks; _i < blocks_1.length; _i++) {\n            var item = blocks_1[_i];\n            var firstItem = item[0];\n            if (attrs[firstItem]) {\n                var customTag = this.getCustomTag(firstItem);\n                return customTag\n                    ? [customTag]\n                    : firstItem === 'header'\n                        ? ['h' + attrs[firstItem]]\n                        : [arr.preferSecond(item)];\n            }\n        }\n        if (this.op.isCustomTextBlock()) {\n            var customTag = this.getCustomTag('renderAsBlock');\n            return customTag ? [customTag] : [positionTag];\n        }\n        var customTagsMap = Object.keys(attrs).reduce(function (res, it) {\n            var customTag = _this.getCustomTag(it);\n            if (customTag) {\n                res[it] = customTag;\n            }\n            return res;\n        }, {});\n        var inlineTags = [\n            ['link', 'a'],\n            ['mentions', 'a'],\n            ['script'],\n            ['bold', 'strong'],\n            ['italic', 'em'],\n            ['strike', 's'],\n            ['underline', 'u'],\n            ['code'],\n        ];\n        return inlineTags.filter(function (item) { return !!attrs[item[0]]; }).concat(Object.keys(customTagsMap)\n            .filter(function (t) { return !inlineTags.some(function (it) { return it[0] == t; }); })\n            .map(function (t) { return [t, customTagsMap[t]]; })).map(function (item) {\n            return customTagsMap[item[0]]\n                ? customTagsMap[item[0]]\n                : item[0] === 'script'\n                    ? attrs[item[0]] === value_types_1.ScriptType.Sub\n                        ? 'sub'\n                        : 'sup'\n                    : arr.preferSecond(item);\n        });\n    };\n    return OpToHtmlConverter;\n}());\nexports.OpToHtmlConverter = OpToHtmlConverter;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar DeltaInsertOp_1 = require(\"./../DeltaInsertOp\");\nvar array_1 = require(\"./../helpers/array\");\nvar group_types_1 = require(\"./group-types\");\nvar Grouper = (function () {\n    function Grouper() {\n    }\n    Grouper.pairOpsWithTheirBlock = function (ops) {\n        var result = [];\n        var canBeInBlock = function (op) {\n            return !(op.isJustNewline() ||\n                op.isCustomEmbedBlock() ||\n                op.isVideo() ||\n                op.isContainerBlock());\n        };\n        var isInlineData = function (op) { return op.isInline(); };\n        var lastInd = ops.length - 1;\n        var opsSlice;\n        for (var i = lastInd; i >= 0; i--) {\n            var op = ops[i];\n            if (op.isVideo()) {\n                result.push(new group_types_1.VideoItem(op));\n            }\n            else if (op.isCustomEmbedBlock()) {\n                result.push(new group_types_1.BlotBlock(op));\n            }\n            else if (op.isContainerBlock()) {\n                opsSlice = array_1.sliceFromReverseWhile(ops, i - 1, canBeInBlock);\n                result.push(new group_types_1.BlockGroup(op, opsSlice.elements));\n                i = opsSlice.sliceStartsAt > -1 ? opsSlice.sliceStartsAt : i;\n            }\n            else {\n                opsSlice = array_1.sliceFromReverseWhile(ops, i - 1, isInlineData);\n                result.push(new group_types_1.InlineGroup(opsSlice.elements.concat(op)));\n                i = opsSlice.sliceStartsAt > -1 ? opsSlice.sliceStartsAt : i;\n            }\n        }\n        result.reverse();\n        return result;\n    };\n    Grouper.groupConsecutiveSameStyleBlocks = function (groups, blocksOf) {\n        if (blocksOf === void 0) { blocksOf = {\n            header: true,\n            codeBlocks: true,\n            blockquotes: true,\n            customBlocks: true,\n        }; }\n        return array_1.groupConsecutiveElementsWhile(groups, function (g, gPrev) {\n            if (!(g instanceof group_types_1.BlockGroup) || !(gPrev instanceof group_types_1.BlockGroup)) {\n                return false;\n            }\n            return ((blocksOf.codeBlocks &&\n                Grouper.areBothCodeblocksWithSameLang(g, gPrev)) ||\n                (blocksOf.blockquotes &&\n                    Grouper.areBothBlockquotesWithSameAdi(g, gPrev)) ||\n                (blocksOf.header &&\n                    Grouper.areBothSameHeadersWithSameAdi(g, gPrev)) ||\n                (blocksOf.customBlocks &&\n                    Grouper.areBothCustomBlockWithSameAttr(g, gPrev)));\n        });\n    };\n    Grouper.reduceConsecutiveSameStyleBlocksToOne = function (groups) {\n        var newLineOp = DeltaInsertOp_1.DeltaInsertOp.createNewLineOp();\n        return groups.map(function (elm) {\n            if (!Array.isArray(elm)) {\n                if (elm instanceof group_types_1.BlockGroup && !elm.ops.length) {\n                    elm.ops.push(newLineOp);\n                }\n                return elm;\n            }\n            var groupsLastInd = elm.length - 1;\n            elm[0].ops = array_1.flatten(elm.map(function (g, i) {\n                if (!g.ops.length) {\n                    return [newLineOp];\n                }\n                return g.ops.concat(i < groupsLastInd ? [newLineOp] : []);\n            }));\n            return elm[0];\n        });\n    };\n    Grouper.areBothCodeblocksWithSameLang = function (g1, gOther) {\n        return (g1.op.isCodeBlock() &&\n            gOther.op.isCodeBlock() &&\n            g1.op.hasSameLangAs(gOther.op));\n    };\n    Grouper.areBothSameHeadersWithSameAdi = function (g1, gOther) {\n        return g1.op.isSameHeaderAs(gOther.op) && g1.op.hasSameAdiAs(gOther.op);\n    };\n    Grouper.areBothBlockquotesWithSameAdi = function (g, gOther) {\n        return (g.op.isBlockquote() &&\n            gOther.op.isBlockquote() &&\n            g.op.hasSameAdiAs(gOther.op));\n    };\n    Grouper.areBothCustomBlockWithSameAttr = function (g, gOther) {\n        return (g.op.isCustomTextBlock() &&\n            gOther.op.isCustomTextBlock() &&\n            g.op.hasSameAttr(gOther.op));\n    };\n    return Grouper;\n}());\nexports.Grouper = Grouper;\n", "\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = Object.setPrototypeOf ||\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar InlineGroup = (function () {\n    function InlineGroup(ops) {\n        this.ops = ops;\n    }\n    return InlineGroup;\n}());\nexports.InlineGroup = InlineGroup;\nvar SingleItem = (function () {\n    function SingleItem(op) {\n        this.op = op;\n    }\n    return SingleItem;\n}());\nvar VideoItem = (function (_super) {\n    __extends(VideoItem, _super);\n    function VideoItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return VideoItem;\n}(SingleItem));\nexports.VideoItem = VideoItem;\nvar BlotBlock = (function (_super) {\n    __extends(BlotBlock, _super);\n    function BlotBlock() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return BlotBlock;\n}(SingleItem));\nexports.BlotBlock = BlotBlock;\nvar BlockGroup = (function () {\n    function BlockGroup(op, ops) {\n        this.op = op;\n        this.ops = ops;\n    }\n    return BlockGroup;\n}());\nexports.BlockGroup = BlockGroup;\nvar ListGroup = (function () {\n    function ListGroup(items) {\n        this.items = items;\n    }\n    return ListGroup;\n}());\nexports.ListGroup = ListGroup;\nvar ListItem = (function () {\n    function ListItem(item, innerList) {\n        if (innerList === void 0) { innerList = null; }\n        this.item = item;\n        this.innerList = innerList;\n    }\n    return ListItem;\n}());\nexports.ListItem = ListItem;\nvar TableGroup = (function () {\n    function TableGroup(rows) {\n        this.rows = rows;\n    }\n    return TableGroup;\n}());\nexports.TableGroup = TableGroup;\nvar TableRow = (function () {\n    function TableRow(cells) {\n        this.cells = cells;\n    }\n    return TableRow;\n}());\nexports.TableRow = TableRow;\nvar TableCell = (function () {\n    function TableCell(item) {\n        this.item = item;\n    }\n    return TableCell;\n}());\nexports.TableCell = TableCell;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar group_types_1 = require(\"./group-types\");\nvar array_1 = require(\"./../helpers/array\");\nvar ListNester = (function () {\n    function ListNester() {\n    }\n    ListNester.prototype.nest = function (groups) {\n        var _this = this;\n        var listBlocked = this.convertListBlocksToListGroups(groups);\n        var groupedByListGroups = this.groupConsecutiveListGroups(listBlocked);\n        var nested = array_1.flatten(groupedByListGroups.map(function (group) {\n            if (!Array.isArray(group)) {\n                return group;\n            }\n            return _this.nestListSection(group);\n        }));\n        var groupRootLists = array_1.groupConsecutiveElementsWhile(nested, function (curr, prev) {\n            if (!(curr instanceof group_types_1.ListGroup && prev instanceof group_types_1.ListGroup)) {\n                return false;\n            }\n            return curr.items[0].item.op.isSameListAs(prev.items[0].item.op);\n        });\n        return groupRootLists.map(function (v) {\n            if (!Array.isArray(v)) {\n                return v;\n            }\n            var litems = v.map(function (g) { return g.items; });\n            return new group_types_1.ListGroup(array_1.flatten(litems));\n        });\n    };\n    ListNester.prototype.convertListBlocksToListGroups = function (items) {\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\n            return (g instanceof group_types_1.BlockGroup &&\n                gPrev instanceof group_types_1.BlockGroup &&\n                g.op.isList() &&\n                gPrev.op.isList() &&\n                g.op.isSameListAs(gPrev.op) &&\n                g.op.hasSameIndentationAs(gPrev.op));\n        });\n        return grouped.map(function (item) {\n            if (!Array.isArray(item)) {\n                if (item instanceof group_types_1.BlockGroup && item.op.isList()) {\n                    return new group_types_1.ListGroup([new group_types_1.ListItem(item)]);\n                }\n                return item;\n            }\n            return new group_types_1.ListGroup(item.map(function (g) { return new group_types_1.ListItem(g); }));\n        });\n    };\n    ListNester.prototype.groupConsecutiveListGroups = function (items) {\n        return array_1.groupConsecutiveElementsWhile(items, function (curr, prev) {\n            return curr instanceof group_types_1.ListGroup && prev instanceof group_types_1.ListGroup;\n        });\n    };\n    ListNester.prototype.nestListSection = function (sectionItems) {\n        var _this = this;\n        var indentGroups = this.groupByIndent(sectionItems);\n        Object.keys(indentGroups)\n            .map(Number)\n            .sort()\n            .reverse()\n            .forEach(function (indent) {\n            indentGroups[indent].forEach(function (lg) {\n                var idx = sectionItems.indexOf(lg);\n                if (_this.placeUnderParent(lg, sectionItems.slice(0, idx))) {\n                    sectionItems.splice(idx, 1);\n                }\n            });\n        });\n        return sectionItems;\n    };\n    ListNester.prototype.groupByIndent = function (items) {\n        return items.reduce(function (pv, cv) {\n            var indent = cv.items[0].item.op.attributes.indent;\n            if (indent) {\n                pv[indent] = pv[indent] || [];\n                pv[indent].push(cv);\n            }\n            return pv;\n        }, {});\n    };\n    ListNester.prototype.placeUnderParent = function (target, items) {\n        for (var i = items.length - 1; i >= 0; i--) {\n            var elm = items[i];\n            if (target.items[0].item.op.hasHigherIndentThan(elm.items[0].item.op)) {\n                var parent = elm.items[elm.items.length - 1];\n                if (parent.innerList) {\n                    parent.innerList.items = parent.innerList.items.concat(target.items);\n                }\n                else {\n                    parent.innerList = target;\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    return ListNester;\n}());\nexports.ListNester = ListNester;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar group_types_1 = require(\"./group-types\");\nvar array_1 = require(\"../helpers/array\");\nvar TableGrouper = (function () {\n    function TableGrouper() {\n    }\n    TableGrouper.prototype.group = function (groups) {\n        var tableBlocked = this.convertTableBlocksToTableGroups(groups);\n        return tableBlocked;\n    };\n    TableGrouper.prototype.convertTableBlocksToTableGroups = function (items) {\n        var _this = this;\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\n            return (g instanceof group_types_1.BlockGroup &&\n                gPrev instanceof group_types_1.BlockGroup &&\n                g.op.isTable() &&\n                gPrev.op.isTable());\n        });\n        return grouped.map(function (item) {\n            if (!Array.isArray(item)) {\n                if (item instanceof group_types_1.BlockGroup && item.op.isTable()) {\n                    return new group_types_1.TableGroup([new group_types_1.TableRow([new group_types_1.TableCell(item)])]);\n                }\n                return item;\n            }\n            return new group_types_1.TableGroup(_this.convertTableBlocksToTableRows(item));\n        });\n    };\n    TableGrouper.prototype.convertTableBlocksToTableRows = function (items) {\n        var grouped = array_1.groupConsecutiveElementsWhile(items, function (g, gPrev) {\n            return (g instanceof group_types_1.BlockGroup &&\n                gPrev instanceof group_types_1.BlockGroup &&\n                g.op.isTable() &&\n                gPrev.op.isTable() &&\n                g.op.isSameTableRowAs(gPrev.op));\n        });\n        return grouped.map(function (item) {\n            return new group_types_1.TableRow(Array.isArray(item)\n                ? item.map(function (it) { return new group_types_1.TableCell(it); })\n                : [new group_types_1.TableCell(item)]);\n        });\n    };\n    return TableGrouper;\n}());\nexports.TableGrouper = TableGrouper;\n"]}