const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const { handleMultiSelect } = require("@/services/selectionService")
const { getSelectedRegionsCache } = require("@/utils/cache/regionCache")
const {
  getNoticeSelectForTemplateCache,
  setExamSelectForTemplateCache,
  getExamSelectForTemplateCache,
  setNoticeSelectForTemplateCache,
} = require("@/utils/cache/filterCache")

const PopupMenuFilterMixin = require("@/components/popup/popup-menu-filter/mixin")

const { processMenuList } = require("@/services/menuServices")

/**
 * 首页页面配置对象
 *
 * 该对象混合了PopupMenuFilterMixin和RegionSelectMixin的功能，用于管理首页的公告和考试动态两个标签页。
 * 主要功能包括：
 * - 处理顶部导航栏和筛选菜单的交互
 * - 管理公告和考试动态的数据获取与筛选
 * - 处理页面滚动时的样式变化
 * - 管理地区选择功能
 *
 * @mixes PopupMenuFilterMixin
 * @property {Object} data - 页面数据对象
 * @property {Function} changeTab - 切换标签页处理函数
 * @property {Function} getHome - 获取首页数据
 * @property {Function} getArticleList - 获取公告列表
 * @property {Function} getNewsList - 获取考试动态列表
 * @property {Function} applyFilter - 应用筛选条件
 * @property {Function} buildApiParams - 构造API请求参数
 */
const pageConfig = Object.assign({}, PopupMenuFilterMixin, {
  data: {
    tabList: [
      {
        title: "公告",
        img:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/tab_title_one.png",
        select_img:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/tab_title_one_select.png",
        width: "68",
        select_width: "72",
      },
      {
        title: "考试动态",
        img:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/tab_title_two.png",
        select_img:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/tab_title_two_select.png",
        width: "138",
        select_width: "144",
      },
    ],
    activeIndex: 0,
    bannerSwiperIndex: 0,
    show_white: false,
    showPopupFilterMenu: false,
    headerHeight: 100, // 导航栏高度，用于sticky定位
    menuSticky: false, // 菜单是否处于吸顶状态
    menuOffsetTop: 0, // 菜单距离页面顶部的初始距离
    activeExpanded: "", // 当前展开的菜单key
    activeExpanded: "suitable", // 当前打开弹窗类型

    popupTitle: "筛选", // 弹窗相关属性
    showFooter: false,
    menuList: [], // 单行布局：[[menu1, menu2, menu3]] - 长度为1的二维数组，分组布局：[[leftMenus], [rightMenus]] - 长度为2的二维数组
    examMenuList: [], // 考试动态Tab的菜单列表
    examMenuData: {},
    newsArticleList: [], // 考试动态列表
    noticeSelectForTemplate: {}, // 统一选中状态管理对象（直接使用数组格式）
    examSelectForTemplate: {}, // 考试动态选中状态管理对象
    showRegionList: false, // 是否显示地区列表

    // 公告页面动画相关状态
    topBoxVisible: true, // 控制top-box元素的显示状态
    homeData: null,
    isRequest: false,
    articleList: [], // 公告列表
    isPageLoadComplete: false, // 页面是否加载完成
    isLogin: false,
    noticePage: 1,
    // 分页相关状态
    noticeHasMore: true, // 是否还有更多数据
    noticeLoading: false, // 是否正在加载
    examPage: 1,
    // 分页相关状态
    examHasMore: true, // 是否还有更多数据
    examLoading: false, // 是否正在加载
    isSubscribe: true,
    showCacelGrouponDialog: false,
  },

  async onLoad(options) {
    console.log("页面加载，初始化数据")
    await APP.checkLoadRequest()

    // 初始化Tab滚动位置记忆功能
    this.tabScrollPositions = {
      0: 0, // 公告tab的滚动位置
      1: 0, // 考试动态tab的滚动位置
    }

    // 初始化弹窗管理功能
    this.initPopupMenuFilterMixin()

    await this.getHome()
    // 页面渲染完成后获取导航栏高度并设置sticky的top值
    this.setMenuStickyTop()

    // 从缓存恢复筛选条件
    this.restoreFilterFromCache()

    // 检查并更新关注公众号显示状态
    if (this.data.homeData?.wx_public_notice?.is_show == 1) {
      const shouldShowSubscribe = APP.shouldShowFollowSubscribe()
      if (this.data.isSubscribe !== shouldShowSubscribe) {
        this.setData({
          isSubscribe: shouldShowSubscribe
        })
        console.log('更新关注公众号显示状态:', shouldShowSubscribe)
      }
    }

    // 初始化公告列表（构造API参数后传递）
    const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
    await this.getArticleList(apiParams, false)
  },
  async onShow() {
    console.log("页面显示，更新筛选条件")

    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }

    // 保存当前状态用于对比
    const currentState = this.getCurrentStateForComparison()

    // 获取最新的首页数据
    await this.getHome()

    // 获取新状态并对比是否发生变化
    const newState = this.getCurrentStateForComparison()
    const hasStateChanged = !UTIL.isObjectEqual(currentState, newState)

    console.log("状态对比结果:", {
      currentState,
      newState,
      hasStateChanged,
    })

    // 从缓存恢复筛选条件
    this.restoreFilterFromCache()

    // 检查并更新关注公众号显示状态
    if (this.data.homeData?.wx_public_notice?.is_show == 1) {
      const shouldShowSubscribe = APP.shouldShowFollowSubscribe()
      if (this.data.isSubscribe !== shouldShowSubscribe) {
        this.setData({
          isSubscribe: shouldShowSubscribe
        })
        console.log('更新关注公众号显示状态:', shouldShowSubscribe)
      }
    }

    // 如果状态发生变化，重新请求数据
    if (hasStateChanged) {
      console.log("检测到状态变化，重新请求数据")
      this.setData({
        noticePage: 1,
      })
      this.applyFilter()
    } else {
      console.log("状态未变化，保持当前数据")
    }
  },

  /**
   * 获取当前状态用于对比
   * 根据登录状态返回不同的对比数据
   * @returns {Object|Array} 用于对比的状态数据
   */
  getCurrentStateForComparison() {
    const { isLogin } = this.data

    if (isLogin) {
      // 已登录：对比 noticeMenuData
      return this.data.noticeMenuData || {}
    } else {
      // 未登录：对比缓存的地区数据
      return getSelectedRegionsCache("announcement") || []
    }
  },
  goSubscribe() {
    ROUTER.navigateTo({
      path: "/pages/subscribe/list/index",
    })
  },
  onReachBottom() {
    console.log("触底了")
    // 检查是否还有更多数据且当前不在加载中
    if (this.data.activeIndex === 0) {
      if (this.data.noticeHasMore && !this.data.noticeLoading) {
        // 页码+1
        const nextPage = this.data.noticePage + 1
        this.setData({
          noticePage: nextPage,
        })

        // 加载下一页数据
        const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
        this.getArticleList(apiParams, true)
      }
    } else {
      if (this.data.examHasMore && !this.data.examLoading) {
        // 页码+1
        const nextPage = this.data.examPage + 1
        this.setData({
          examPage: nextPage,
        })

        // 加载下一页数据
        const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
        this.getNewsList(apiParams, true)
      }
    }
  },
  /**
   * 设置导航栏背景色和胶囊按钮颜色
   * 根据当前选择的tab和滚动位置自动计算并设置show_white
   */
  setNavigationBarStyle() {
    const { activeIndex } = this.data
    let showWhite = false

    if (activeIndex == 1) {
      // 考试动态tab始终为白色背景
      showWhite = true
    } else {
      // 公告tab根据滚动位置决定背景色
      const currentScrollTop = this.tabScrollPositions[activeIndex] || 0
      showWhite = currentScrollTop > 0
    }

    // 更新show_white状态
    if (this.data.show_white === showWhite) {
      return
    }

    this.setData({ show_white: showWhite })
    // 设置导航栏颜色
    if (showWhite) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  /**
   * 恢复指定tab的滚动位置
   * @param {number} tabIndex tab索引
   */
  restoreTabScrollPosition(tabIndex) {
    const scrollTop = this.tabScrollPositions[tabIndex] || 0
    wx.pageScrollTo({
      scrollTop: scrollTop,
      duration: 0,
    })
    wx.nextTick(() => {
      this.setData({
        topBoxVisible: true,
      })
    })
  },

  /**
   * 获取首页数据
   */
  async getHome() {
    try {
      const res = await UTIL.request(API.getHome)
      if (res && res.error && res.error.code === 0 && res.data) {
        console.log(res, "首页数据")
        const isAll = {
          is_default: 1,
          name: "全部",
          region_city: 0,
          region_district: 0,
          region_province: 0,
          short_name: "",
          value: "0-0-0",
        }

        // 处理数据：将 key 换成 value，并在数组开头添加 isAll
        const processedData = getSelectedRegionsCache("announcement").map(
          (item) => ({
            ...item,
            name: item.text || item.area_name,
            value: item.key,
          })
        )
        this.setData({
          homeData: res.data,
          isRequest: true,
          isSubscribe: res.data.wx_public_notice.is_show == 1 ? APP.shouldShowFollowSubscribe() : false,
          isLogin: APP.getIsLogin(),
          cachedRegions: [isAll, ...processedData],
        })
        // 初始化动态菜单
        if (res.data.article_filter_menu) {
          this.initDynamicMenu(res.data.article_filter_menu)
        }
      }
    } catch (error) {}
  },
  async getArticleList(filterConditions = {}, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.noticeLoading ||
      (!isLoadMore && !this.data.noticeHasMore && this.data.noticePage > 1)
    ) {
      return
    }
    try {
      // 确保传递的参数不为空，至少是一个空对象
      const requestParams = {
        page: this.data.noticePage,
        ...filterConditions,
      }

      this.setData({
        noticeLoading: true,
      })

      const res = await UTIL.request(API.getHomeArticleList, requestParams)

      if (res && res.error && res.error.code === 0 && res.data) {
        console.log("获取公告列表成功:", res.data)

        // 处理公告列表数据
        const newList = res.data.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedJobList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedJobList = [...this.data.articleList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedJobList = newList
        }

        // 更新页面数据
        this.setData({
          articleList: updatedJobList,
          isRequest: true,
          isPageLoadComplete: true,
          noticeHasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          noticeLoading: false,
        })

        return res.data
      } else {
        console.error("获取公告列表失败:", res)
        this.setData({
          noticeLoading: false,
          noticeHasMore: false,
        })
        return null
      }
    } catch (error) {
      console.error("请求公告列表异常:", error)
      this.setData({
        noticeLoading: false,
      })
      return null
    }
  },
  // 获取考试动态顶部筛选列表
  async getNoticeFilterMenu() {
    const res = await UTIL.request(API.getNoticeFilterMenu)
    if (res && res.error && res.error.code === 0 && res.data) {
      if (
        res.data.notice_filter_menu &&
        Array.isArray(res.data.notice_filter_menu)
      ) {
        this.initExamSelectFromMenuData(res.data.notice_filter_menu)
      }
    }
  },

  // 获取考试动态列表
  async getNewsList(filterConditions = {}, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.examLoading ||
      (!isLoadMore && !this.data.examHasMore && this.data.examPage > 1)
    ) {
      return
    }
    try {
      console.log("请求考试动态列表，筛选条件:", filterConditions)

      // 确保传递的参数不为空，至少是一个空对象
      const requestParams = {
        page: this.data.examPage,
        ...filterConditions,
      }

      this.setData({
        examLoading: true,
      })
      const res = await UTIL.request(API.getNoticeList, requestParams)

      if (res && res.error && res.error.code === 0 && res.data) {
        console.log("获取考试动态列表成功:", res.data)
        // 处理公告列表数据
        const newList = res.data.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedJobList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedJobList = [...this.data.newsArticleList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedJobList = newList
        }
        // 更新页面数据
        this.setData({
          newsArticleList: updatedJobList,
          examHasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          examLoading: false,
        })

        return res.data
      } else {
        console.error("获取考试动态列表失败:", res)
        this.setData({
          examLoading: false,
          examHasMore: false,
        })
        return null
      }
    } catch (error) {
      console.error("请求考试动态列表异常:", error)
      return null
    }
  },
  // 初始化动态菜单
  initDynamicMenu(serverMenuList) {
    if (serverMenuList && serverMenuList.length) {
      // 使用提取后的纯函数处理菜单
      const menuList = processMenuList(serverMenuList)
      const noticeMenuData = {}
      const cacheRegion = getSelectedRegionsCache("announcement")

      serverMenuList.forEach((item) => {
        noticeMenuData[item.filter_key] = item
        // if (item.filter_key === "apply_region") {
        //   // 检查是否登录
        //   if (this.data.isLogin && cacheRegion?.length) {
        //     // 没有登录且有缓存数据时，合并原有数据和缓存数据
        //     const originalList =
        //       noticeMenuData[item.filter_key].data[0].list || []
        //     const mergedList = [...originalList, ...cacheRegion]

        //     // 去重处理，根据 key 或 value 字段去重
        //     const uniqueList = mergedList.filter((item, index, array) => {
        //       const identifier = item.key || item.value || item.id
        //       return (
        //         array.findIndex(
        //           (el) => (el.key || el.value || el.id) === identifier
        //         ) === index
        //       )
        //     })

        //     noticeMenuData[item.filter_key].data[0].list = uniqueList

        //     console.log("未登录状态下合并地区数据:", {
        //       originalCount: originalList.length,
        //       cacheCount: cacheRegion.length,
        //       mergedCount: uniqueList.length,
        //     })
        //   }
        // }
      })

      console.log(this.getNoticeSelectFromMenuData(serverMenuList), "12333333")
      this.setData({
        noticeMenuData,
        menuList,
        noticeSelectForTemplate: this.getNoticeSelectFromMenuData(
          serverMenuList
        ),
      })
    }
  },
  handleRegionConfirmSelection(e) {
    const { filterKey, tempSelected } = e.detail
    this.setData({
      [`examSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setExamSelectForTemplateCache(this.data.examSelectForTemplate, "news")
    this.applyNewsFilter()
    this.hidePopupMenuFilter()
  },
  /**
   * 从菜单数据初始化noticeSelectForTemplate对象
   * 数据结构：{ filter_key: Array<value> } 或 { filter_key: { nested_key: Array<value> } }
   * @param {Array} serverMenuList 原始菜单数据 (article_filter_menu)
   */
  getNoticeSelectFromMenuData(serverMenuList) {
    const result = {}
    serverMenuList.forEach((menu) => {
      const filterKey = menu.filter_key

      if (filterKey === "filter_list") {
        result[filterKey] = {}
        menu.data.forEach((filterGroup) => {
          const groupFilterKey = filterGroup.filter_key
          if (groupFilterKey) {
            result[filterKey][groupFilterKey] = []
          }
        })
      } else if (filterKey) {
        result[filterKey] = []
      }
    })
    return result
  },
  /**
   * 从菜单数据初始化examSelectForTemplate对象（考试动态Tab）
   * @param {Array} serverMenuList 原始菜单数据 (news_filter_menu)
   */
  initExamSelectFromMenuData(serverMenuList) {
    if (serverMenuList && serverMenuList.length) {
      console.log(serverMenuList, "进来米")
      const examMenuList = processMenuList(serverMenuList)
      let examMenuData = {}
      serverMenuList.forEach((item) => {
        examMenuData[item.filter_key] = item
      })
      this.setData({
        examMenuList,
        examMenuData,
        examSelectForTemplate: this.getNoticeSelectFromMenuData(serverMenuList),
      })
    }
  },

  /**
   * 应用考试动态Tab的筛选条件
   */
  async applyNewsFilter() {
    try {
      // 构造考试动态的API参数
      const { examSelectForTemplate } = this.data
      const apiParams = this.buildApiParams(examSelectForTemplate)
      // 保存考试动态筛选条件到缓存
      setExamSelectForTemplateCache(this.data.examSelectForTemplate, "news")
      // 获取考试动态列表
      await this.getNewsList(apiParams, false)
    } catch (error) {
      console.error("应用考试动态筛选失败:", error)
    }
  },

  // 设置菜单sticky的top值
  setMenuStickyTop() {
    const query = wx.createSelectorQuery()
    query
      .select("#commonHeader")
      .boundingClientRect((headerRect) => {
        let headerHeight = 100 // 默认高度
        console.log("获取到的header信息:", headerRect)

        if (headerRect) {
          headerHeight = headerRect.height
          console.log("成功获取导航栏高度:", headerHeight)
        } else {
          console.log("无法获取导航栏高度，使用降级方案")
          // 降级方案：通过系统信息计算
          const systemInfo = wx.getSystemInfoSync()
          const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
          headerHeight = menuButtonInfo.bottom
          console.log("降级方案计算高度:", headerHeight)
        }

        // 获取菜单容器的位置信息
        const menuQuery = wx.createSelectorQuery()
        menuQuery.select(".main-top").boundingClientRect()
        menuQuery.selectViewport().scrollOffset()
        menuQuery.exec((res) => {
          const menuRect = res[0]
          const scrollOffset = res[1]

          let menuOffsetTop = 0
          if (menuRect) {
            // 计算菜单距离页面顶部的距离
            menuOffsetTop = menuRect.top + scrollOffset.scrollTop
          }

          // 设置数据
          this.setData({
            headerHeight: headerHeight,
            menuOffsetTop: menuOffsetTop,
          })

          console.log("导航栏高度:", headerHeight)
          console.log("菜单初始位置:", menuOffsetTop)
        })
      })
      .exec()
  },

  goSearch() {
    ROUTER.navigateTo({
      path: "/pages/search/index",
    })
  },
  goBig() {
    ROUTER.navigateTo({
      path: "/pages/special/big-data/index",
    })
  },
  goFollow() {
    ROUTER.navigateTo({
      path: "/pages/my/focusList/index",
    })
  },

  /**
   * 处理添加地区按钮点击事件
   */
  handleAddRegionClick() {
    const { activeIndex } = this.data
    const tabType = activeIndex === 0 ? "announcement" : "news"

    // 跳转到地区选择页面，传递Tab类型
    ROUTER.navigateTo({
      path: "/pages/select/select-region/index",
      query: {
        tabType: tabType,
      },
    })
  },
  /**
   * 处理地区列表项点击
   * @param {Object} e 事件对象
   */
  handleRegionItemClick(e) {
    const { item } = e.currentTarget.dataset
    const { noticeSelectForTemplate } = this.data

    noticeSelectForTemplate.apply_region = [item.value]
    this.setData({
      noticeSelectForTemplate,
      noticePageL: 1,
    })
    setNoticeSelectForTemplateCache(
      this.data.noticeSelectForTemplate,
      "announcement"
    )

    // 应用筛选
    this.applyFilter()
  },

  handleNoticeMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`noticeSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setNoticeSelectForTemplateCache(
      this.data.noticeSelectForTemplate,
      "announcement"
    )
    this.hidePopupMenuFilter()
    this.applyFilter()
  },
  handleExamMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`examSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setNoticeSelectForTemplateCache(this.data.noticeSelectForTemplate, "news")
    this.hidePopupMenuFilter()
    this.applyNewsFilter()
  },

  goDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  changeTab(e) {
    console.log(e.currentTarget.dataset.index)
    const index = e.currentTarget.dataset.index
    const currentIndex = this.data.activeIndex

    // 如果点击的是当前tab，不做任何处理
    if (index === currentIndex) {
      return
    }

    // 如果有弹窗打开，先关闭弹窗
    if (this.data.showPopupFilterMenu) {
      this.hidePopupMenuFilter()
    }

    this.setData({
      activeIndex: index,
      // 切换Tab时隐藏地区列表
      showRegionList: false,
      // 切换Tab时清空展开状态
      activeExpanded: "",
      topBoxVisible: index === 0 ? false : true,
    })

    if (index == 1) {
      // 切换到考试动态时，获取筛选菜单数据和初始化考试动态列表
      this.initNewsTabData()
    } else {
      // 切换到公告时，从缓存恢复 noticeSelectForTemplate
      const previousNoticeSelect = JSON.stringify(
        this.data.noticeSelectForTemplate
      )
      this.updateNoticeSelectForTemplateFromCache()
      const currentNoticeSelect = JSON.stringify(
        this.data.noticeSelectForTemplate
      )

      // 只有当筛选条件真的发生变化时才重新请求数据
      if (previousNoticeSelect !== currentNoticeSelect) {
        console.log("筛选条件发生变化，重新请求数据")
        this.applyFilter()
      } else {
        console.log("筛选条件未变化，保持当前数据状态")
      }
    }

    // 恢复目标tab的滚动位置
    this.restoreTabScrollPosition(index)

    // 设置导航栏样式（会自动计算show_white）
    this.setNavigationBarStyle()
  },

  /**
   * 初始化考试动态Tab数据
   */
  async initNewsTabData() {
    // 获取菜单数据
    await this.getNoticeFilterMenu()
    this.restoreExamFilterFromCache()
    const apiParams = this.buildApiParams(this.data.examSelectForTemplate)
    await this.getNewsList(apiParams, false)
  },

  // 处理banner swiper 切换
  handleBannerSwiperChange(e) {
    this.setData({
      bannerSwiperIndex: e.detail.current,
    })
  },
  goNotice(e) {
    console.log(e)
    const data = e.currentTarget.dataset.item
    const path =
      data.type == "article_list"
        ? "/pages/notice/collection/index"
        : "/pages/notice/detail/index"
    ROUTER.navigateTo({
      path,
      query: {
        id: data.id,
      },
    })
  },
  handleNewsMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showPopupFilterMenu, noticeSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key

    const currentMenuSelected = noticeSelectForTemplate[filterKey]

    if (type === "check" || type === "apply_region") {
      this.hidePopupMenuFilter()
    } else if (
      showPopupFilterMenu === true &&
      this.data.activeExpanded === filterKey
    ) {
      this.hidePopupMenuFilter()
    } else {
      this.showPopupMenuFilter()
    }

    this.setData({
      examPage: 1,
      activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "check") {
      this.setData({
        [`examSelectForTemplate.${filterKey}`]: handleMultiSelect(
          currentMenuSelected || [],
          1
        ),
      })
      setExamSelectForTemplateCache(this.data.examSelectForTemplate, "news")
      return
    }
  },

  /**
   * 处理菜单项点击事件
   * @param {Object} e 事件对象
   */
  handleMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showPopupFilterMenu, noticeSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key

    const currentMenuSelected = noticeSelectForTemplate[filterKey]

    if (type !== "apply_region") {
      this.setData({
        showRegionList: false,
      })
    }

    if (type === "check" || type === "apply_region") {
      this.hidePopupMenuFilter()
    } else if (
      showPopupFilterMenu === true &&
      this.data.activeExpanded === filterKey
    ) {
      this.hidePopupMenuFilter()
    } else {
      this.showPopupMenuFilter()
    }

    this.setData({
      isLogin: APP.getIsLogin(),
      noticePage: 1,
      activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
    if (filterKey === "apply_region") {
      // 切换地区列表显示状态
      this.setData({
        showRegionList: !this.data.showRegionList,
      })
      return
    }

    // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "check") {
      this.setData({
        [`noticeSelectForTemplate.${filterKey}`]: handleMultiSelect(
          currentMenuSelected || [],
          1
        ),
      })
      setNoticeSelectForTemplateCache(
        this.data.noticeSelectForTemplate,
        "announcement"
      )
      this.applyFilter()
      return
    }
  },

  /**
   * 应用筛选条件 - 统一处理两个Tab
   */
  async applyFilter() {
    const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
    console.log("拿到的参数", apiParams)
    await this.getArticleList(apiParams, false)
  },

  /**
   * 构造API请求参数 - 直接从 noticeSelectForTemplate 构建
   * @returns {Object} API参数对象
   */
  buildApiParams(selectedData) {
    const apiParams = {}

    Object.keys(selectedData).forEach((keyName) => {
      const data =
        selectedData[keyName] || (keyName !== "filter_list" ? [] : {})
      if (keyName === "fit_me" || keyName === "has_tenure") {
        apiParams[keyName] = data[0] || null
      }
      if (keyName === "apply_region") {
        if (data[0] == "0-0-0") {
          // 从 list 中提取所有有 key 值的项目，组成 key 值数组
          const regionList =
            this.data.noticeMenuData.apply_region.data[0].list || []
          const keyArray = regionList
            .filter((item) => item && item.key) // 过滤掉没有 key 的项目
            .map((item) => item.key) // 提取 key 值

          apiParams["region"] = keyArray

          console.log("提取的地区 key 数组:", keyArray)
        } else {
          apiParams["region"] = data
        }
      }
      // if (keyName === "apply_region") {
      //   console.log(
      //     data,
      //     "12312312312",
      //     this.data.noticeMenuData.apply_region.data[0].list
      //   )
      //   if (data[0] == "0-0-0") {
      //     apiParams["region"] = data
      //   }
      // }
      if (keyName === "filter_list") {
        Object.keys(data).forEach((cKeyName) => {
          const cData = data[cKeyName] || []

          if (cKeyName === "need_num") {
            const numValue = Number(cData[0])
            const val = !isNaN(numValue) ? numValue : null
            apiParams[cKeyName] = val
          }

          if (cKeyName === "apply_status") {
            apiParams[cKeyName] = cData
          }
        })
      }
      apiParams[keyName] = data
    })
    return UTIL.convertArraysToString(apiParams)
  },

  handlePopupClose() {
    console.log("弹窗关闭事件")
    // 清空展开状态
    this.setData({
      activeExpanded: "",
      showRegionList: false,
    })
    // 调用hidePopupMenuFilter真正关闭弹窗（包含恢复备份的逻辑）
    this.hidePopupMenuFilter()
  },
  goMenuItem(e) {
    const cmdJson = e.currentTarget.dataset.item
    APP.toCmdUnitKey(cmdJson)
  },
  toNewsDetail(e) {
    const { id } = e.currentTarget.dataset.item
    ROUTER.navigateTo({
      path: "/pages/notice/dynamics/index",
      query: {
        id,
      },
    })
  },
  goMenu(e) {
    const cmdJson = e.currentTarget.dataset.item
    console.log(e, cmdJson)
    APP.toCmdUnitKey(cmdJson)
  },
  /**
   * 从缓存恢复筛选条件
   */
  restoreFilterFromCache() {
    const noticeSelectForTemplate = this.updateNoticeSelectForTemplateFromCache()
    this.changeNoTiceSelect(noticeSelectForTemplate)
  },
  /**
   * 从缓存恢复公告选中状态 (noticeSelectForTemplate)
   */
  updateNoticeSelectForTemplateFromCache() {
    const noticeSelectForTemplate = this.data.noticeSelectForTemplate
    const cacheNoticeSelectForTemplate = getNoticeSelectForTemplateCache(
      "announcement"
    )
    for (const key in noticeSelectForTemplate) {
      if (cacheNoticeSelectForTemplate[key]) {
        noticeSelectForTemplate[key] = cacheNoticeSelectForTemplate[key]
      }
      if (key == "apply_region" && !cacheNoticeSelectForTemplate[key]) {
        noticeSelectForTemplate[key] = ["0-0-0"]
      }
    }
    return noticeSelectForTemplate
  },
  changeNoTiceSelect(noticeSelectForTemplate) {
    this.setData({
      noticeSelectForTemplate,
    })
  },
  /**
   * 从缓存恢复考试动态筛选条件
   */
  restoreExamFilterFromCache() {
    this.setData({
      examSelectForTemplate: getExamSelectForTemplateCache("news"),
    })
  },
  closeIsSubscribe() {
    // 记录关闭时间到缓存，3天后重新显示
    APP.setFollowCloseTime()
    
    this.hidePopupMenuFilter()
    this.setData({
      activeExpanded: "",
      isSubscribe: false,
      // overlayTop: this.data.overlayTop - 18,
    })
  },
  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }

    const scrollTop = e.scrollTop
    const { headerHeight, activeIndex } = this.data

    this.tabScrollPositions[activeIndex] = scrollTop

    // 处理头部背景色变化 - 只在公告标签页（activeIndex == 0）时响应滚动
    if (activeIndex == 0) {
      this.setNavigationBarStyle()
    }

    // 检测菜单是否吸顶
    // 当滚动距离大于等于菜单到顶部的距离减去导航栏高度时，菜单开始吸顶
    const { menuOffsetTop } = this.data
    const isMenuSticky =
      menuOffsetTop > 0 && scrollTop >= menuOffsetTop - headerHeight

    if (isMenuSticky !== this.data.menuSticky) {
      this.setData({
        menuSticky: isMenuSticky,
      })
    }
  },
  // 获取分享参数
  getPageShareParams() {
    const query = {}
    const shareParams = APP.createShareParams({
      title: "",
      imageUrl: "",
      path: "/pages/home/<USER>/index",
      query,
    })
    return shareParams
  },
  // 获取分享参数
  getPageShareParams() {
    const share_info = this.data?.homeData?.share_info || {}
    let shareTitle = share_info.title
    let shareImageUrl = share_info.image
    const params = {
      title: shareTitle,
      imageUrl: shareImageUrl,
      path: "/pages/home/<USER>/index",
      query: {},
    }
    return params
  },
  onShareAppMessage() {
    return APP.createShareParams(this.getPageShareParams())
  },
  // 分享朋友圈
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    const result = APP.createShareParams(params)
    result.query = ROUTER.convertPathQuery(result.query)
    return result
  },
})
// 创建页面实例
Page(pageConfig)
