/**
 * 判断数组中是否包含指定值
 * @param {Array} arr 数组
 * @param {Number} value 要查找的值
 * @returns {Boolean} 是否包含
 */
function isActive(arr, value) {
  if (!arr || arr.length === 0) {
    return false
  }

  for (var i = 0; i < arr.length; i++) {
    if (arr[i] === value) {
      return true
    }
  }
  return false
}

function formatNumber(num) {
  if (num) {
    // 先将数字四舍五入保留一位小数
    var rounded = Math.round(num * 10) / 10
    // 检查是否为整数（小数部分为0）
    if (rounded % 1 === 0) {
      return rounded.toString() // 整数直接返回字符串形式
    } else {
      return rounded.toFixed(1) // 非整数保留一位小数
    }
  } else {
    return "-"
  }
}

module.exports = {
  isActive: isActive,
  formatNumber: formatNumber,
}
