/* 弹窗底部操作栏组件样式 */
.popu-bottom {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid rgba(235, 236, 240, 1);
  background: rgba(255, 255, 255, 1);
  transform: rotateZ(360deg);
  &.education {
    justify-content: space-between;
    .btn {
      width: 330rpx;
      flex: none;
    }
    .img {
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }
    .synchro-box {
      display: flex;
      align-items: center;
    }
  }
  .btn {
    font-size: 28rpx;
    flex: 1;
    background-color: rgba(236, 62, 51, 1);
    color: #fff;
    height: 84rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    // 重置按钮样式
    &.op-5 {
      background-color: rgba(236, 62, 51, 0.1);
      color: rgba(236, 62, 51, 1);
      margin-right: 24rpx;
    }

    // 按钮点击效果
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }
  .synchro-box {
    font-size: 26rpx;
    color: rgba(102, 102, 102, 1);
  }
}
