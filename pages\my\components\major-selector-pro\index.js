const majorData = require("./majorData.js")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    defaultValue: {
      type: String,
      value: "",
    },
    educationId: {
      type: String,
      value: "6", // 默认为普通本科
    },
    selectedMajorIds: {
      type: Array,
      value: [],
    },
  },

  /**
   * 监听属性变化
   */
  observers: {
    show(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        this.initData()
      }
    },
  },

  data: {
    tabList: [
      { education_id: 4, name: "专科" },
      { education_id: 6, name: "普通本科" },
      { education_id: 8, name: "硕士研究生" },
      { education_id: 10, name: "博士研究生" },
    ],
    firstLevelList: [],
    secondLevelList: [],
    thirdLevelList: [],
    selectedFirstIndex: -1,
    selectedSecondIndex: -1,
    selectedThirdIndex: -1,
    selectedText: "",
    // 专业数据
    selectedMajor: null, // 已选中的专业 {id, name, fullName}
    syncToResume: true, // 默认同步至我的简历
    isSearching: false,
    searchResults: [],
    selectedMajorNames: "", // 用于显示已选专业名称

    // 搜索
    searchKeyword: "",
  },

  methods: {
    // 初始化数据
    async initData() {
      this.setData({
        educationId: this.properties.educationId || 6,
      })
      try {
        this.setData({
          firstLevelList: [],
          secondLevelList: [],
          thirdLevelList: [],
          selectedFirstIndex: -1,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          selectedMajorIds: this.properties.selectedMajorIds || [],
        })

        const param = {
          pid: 0,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)
        console.log("第一级专业数据:", res)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            firstLevelList: res.data,
          })

          // 回显
          if (this.data.selectedMajorIds.length === 3) {
            await this.performEchoByIds()
          }
        }
      } catch (error) {
        console.error("获取第一级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    async performEchoByIds() {
      const [firstId, secondId, thirdId] = this.data.selectedMajorIds

      try {
        // 1. 找到第一级索引
        const firstIndex = this.data.firstLevelList.findIndex(
          (item) => item.id == firstId
        )
        if (firstIndex === -1) return

        // 2. 选中第一级并获取第二级数据
        await this.onFirstLevelSelect({
          currentTarget: { dataset: { index: firstIndex } },
        })

        // 3. 找到第二级索引
        const secondIndex = this.data.secondLevelList.findIndex(
          (item) => item.id == secondId
        )
        if (secondIndex === -1) return

        // 4. 选中第二级并获取第三级数据
        await this.onSecondLevelSelect({
          currentTarget: { dataset: { index: secondIndex } },
        })

        // 5. 找到第三级索引
        const thirdIndex = this.data.thirdLevelList.findIndex(
          (item) => item.id == thirdId
        )
        if (thirdIndex === -1) return

        // 6. 选中第三级
        this.onThirdLevelSelect({
          currentTarget: { dataset: { index: thirdIndex } },
        })
      } catch (error) {
        console.error("回显失败:", error)
      }
    },

    // 切换学历Tab
    onTabClick(e) {
      const tabId = e.currentTarget.dataset.id
      this.setData({
        educationId: tabId,
      })
      this.initData()
    },

    /**
     * 选择一级分类
     */
    async onFirstLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.firstLevelList[index]

      try {
        this.setData({
          selectedFirstIndex: index,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          secondLevelList: [],
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            secondLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第二级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },
    async onSelectSearchResult(e) {
      const selectedItem = e.currentTarget.dataset.item
      const selectedText = `${selectedItem.grand_name}-${selectedItem.parent_name}-${selectedItem.name}`
      const selectedMajorIds = [
        selectedItem.grand_id,
        selectedItem.parent_id,
        selectedItem.id,
      ]
      this.setData({
        selectedMajorIds,
        selectedText,
      })
      await this.performEchoByIds()
      this.setData({
        searchKeyword: "",
        searchResults: [],
      })
    },

    /**
     * 选择二级分类
     */
    async onSecondLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.secondLevelList[index]

      try {
        this.setData({
          selectedSecondIndex: index,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)
        console.log("第三级专业数据:", res)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            thirdLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第三级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择三级分类（具体专业）
     */
    onThirdLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.thirdLevelList[index]
      const firstLevel = this.data.firstLevelList[this.data.selectedFirstIndex]
      const secondLevel = this.data.secondLevelList[
        this.data.selectedSecondIndex
      ]

      const selectedText = `${firstLevel.name}-${secondLevel.name}-${selectedItem.name}`

      this.setData({
        selectedThirdIndex: index,
        selectedMajor: selectedItem,
        selectedText: selectedText,
      })
    },

    // 重置
    onReset() {
      this.setData({
        educationId: "",
        searchKeyword: "",
        isSearching: false,
        selectedMajor: null,
        activeCategoryId: null,
        activeSecondLevelId: null,
      })
      this.loadMajorData()
    },

    clearSelection() {
      this.setData({
        selectedMajor: null,
        selectedText: "",
        selectedFirstIndex: -1,
        selectedSecondIndex: -1,
        selectedThirdIndex: -1,
        selectedMajorIds: [],
      })
    },

    toggleSyncToResume() {
      this.setData({
        syncToResume: !this.data.syncToResume,
      })
    },

    onSave() {
      // 未选择专业时，点击无效
      if (!this.data.selectedMajor) {
        return
      }
      // 保存选中的索引和ID用于回显
      const selectedIds = []
      if (this.data.selectedFirstIndex >= 0) {
        selectedIds.push(
          this.data.firstLevelList[this.data.selectedFirstIndex].id
        )
      }
      if (this.data.selectedSecondIndex >= 0) {
        selectedIds.push(
          this.data.secondLevelList[this.data.selectedSecondIndex].id
        )
      }
      if (this.data.selectedThirdIndex >= 0) {
        selectedIds.push(
          this.data.thirdLevelList[this.data.selectedThirdIndex].id
        )
      }
      // 触发确认事件，直接选择
      this.triggerEvent("confirm", {
        major: this.data.selectedMajor,
        text: this.data.selectedText,
        selectedMajorIds: selectedIds,
        educationId: this.data.educationId,
        isSync: this.data.syncToResume,
      })
    },

    // 关闭
    onClose() {
      console.log(1111)
      this.triggerEvent("close")
    },

    onSearchInput(e) {
      const keyword = e.detail.value.trim()
      this.setData({
        searchKeyword: keyword,
      })

      if (keyword) {
        this.performSearch(keyword)
      } else {
        this.setData({
          searchResults: [],
        })
      }
    },

    async performSearch(keyword) {
      try {
        const param = {
          keywords: keyword,
          education_id: this.data.educationId,
        }
        const res = await UTIL.request(API.getMajorByKeyWords, param)
        if (res && res.data && res.data.length > 0) {
          this.setData({
            searchResults: res.data,
          })
        } else {
          this.setData({
            searchResults: [],
          })
        }
      } catch (error) {
        console.error("搜索失败:", error)
        this.setData({
          searchResults: [],
        })
        wx.showToast({
          title: "搜索失败",
          icon: "none",
        })
      }
    },

    searchMajors(keyword) {
      if (!keyword) {
        this.setData({ searchResults: [] })
        return
      }
      const results = []
      const allMajors = [
        // ...Object.values(majorData.benke.thirdLevel).flat(),
        // ...Object.values(majorData.zhuanke.thirdLevel).flat(),
        // ...Object.values(majorData.shuoshi.thirdLevel).flat(),
        // ...Object.values(majorData.boshi.thirdLevel).flat(),
      ]

      // allMajors.forEach((major) => {
      //   if (major.name.includes(keyword)) {
      //     results.push(major)
      //   }
      // })

      this.setData({ searchResults: results })
    },
  },
})
