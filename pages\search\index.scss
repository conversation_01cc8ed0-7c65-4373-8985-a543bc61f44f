page {
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);
  box-sizing: border-box;
}

.lefts {
  padding-left: 32rpx;
  display: flex;
  align-items: center;
  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.search-box-top {
  background: #fff;
  padding: 16rpx 40rpx 24rpx 40rpx;
  border-bottom: 1rpx solid rgba(235, 236, 240, 1);
  transform: rotateZ(360deg);
  box-sizing: border-box;

  // 无边框样式 - 当没有搜索结果时使用
  &.no-border {
    border-bottom: none;
  }
  .search-box {
    display: flex;
    align-items: center;
    background: rgba(246, 248, 249, 1);
    font-size: 28rpx;
    padding: 12rpx 24rpx;
    border-radius: 32rpx;
    border: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);
    .img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
    input {
      flex: 1;
      font-size: 28rpx;
    }
    .clear {
      width: 32rpx;
      height: 32rpx;
      margin-left: 32rpx;
    }
  }
  .menu-list {
    display: flex;
    align-items: center;
    margin-top: 36rpx;
    &-item {
      font-size: 28rpx;
      color: rgba(102, 102, 102, 1);
      flex: 1;
      display: flex;
      justify-content: center;
      position: relative;
      &.active {
        font-size: 32rpx;
        font-weight: 600;
        color: rgba(34, 36, 46, 1);
        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 40rpx;
          height: 6rpx;
          background-color: rgba(230, 0, 3, 1);
          border-radius: 4rpx;
          bottom: -14rpx;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}

.search-box-content {
  box-sizing: border-box;
  padding: 40rpx;
  .all-num {
    font-size: 24rpx;
    color: rgba(145, 148, 153, 1);
    margin-bottom: 32rpx;
    .num {
      color: rgba(230, 0, 3, 1);
      font-weight: 600;
      font-family: "DINGBold";
    }
  }
  .history-box {
    .history-box-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 32rpx;
        color: rgba(60, 61, 66, 1);
        font-weight: 600;
      }
      .delete-img {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .history-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 32rpx;
      box-sizing: border-box;
      &-item {
        font-size: 26rpx;
        color: rgba(102, 102, 102, 1);
        background: rgba(247, 248, 250, 1);
        margin-right: 24rpx;
        padding: 12rpx 32rpx;
        border-radius: 12rpx;
        margin-bottom: 24rpx;
      }
    }
  }
  .select-exams {
    border: 1rpx solid rgba(230, 0, 3, 0.2);
    transform: rotateZ(360deg);
    padding: 40rpx 32rpx;
    border-radius: 16rpx;
    box-sizing: border-box;
    background: url("https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_select_bg.png");
    background-repeat: no-repeat;
    background-size: 100%;
    .select-exams-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-img {
        width: 144rpx;
      }
      .more {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: rgba(145, 148, 153, 1);
        .arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .exam-list {
      box-sizing: border-box;
      margin-top: 40rpx;
      &-item {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .num {
          width: 36rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }
        .text {
          font-size: 28rpx;
          flex: 1;
          min-width: 0;
          color: rgba(60, 61, 66, 1);
        }
        .new {
          width: 36rpx;
          height: 36rpx;
          margin-left: 40rpx;
        }
      }
    }
  }
}

// 加载中状态样式
.loading-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: rgba(145, 148, 153, 1);
  font-size: 28rpx;
}

// 历史搜索项点击效果
.history-list-item {
  &:active {
    background: rgba(230, 0, 3, 0.1) !important;
    transform: scale(0.98);
  }
}

// 热门搜索项点击效果
.exam-list-item {
  &:active {
    background: rgba(230, 0, 3, 0.05);
    border-radius: 8rpx;
    transform: scale(0.98);
  }
}

// 清除按钮点击效果
.clear {
  &:active {
    transform: scale(0.9);
    opacity: 0.7;
  }
}

// 搜索结果数量高亮
.all-num .num {
  font-family: "DIN", "DINGBold", Arial, sans-serif;
}
