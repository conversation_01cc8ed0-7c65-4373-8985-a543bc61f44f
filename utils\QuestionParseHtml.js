import { QuillDeltaToHtmlConverter } from "../miniprogram_npm/quill-delta-to-html/index"

// 过滤样式函数：只保留指定的样式属性
const filterStyleAttributes = function(styleString) {
  if (!styleString || typeof styleString !== 'string') {
    return styleString
  }
  
  // 允许保留的样式属性列表
  const allowedStyles = ['color', 'font-weight', 'text-align', 'text-indent', 'background', 'background-color']
  
  try {
    // 解析样式字符串，分割为单个样式声明
    const styleDeclarations = styleString.split(';').filter(Boolean)
    const filteredStyles = []
    
    styleDeclarations.forEach(declaration => {
      const trimmedDeclaration = declaration.trim()
      if (!trimmedDeclaration) return
      
      // 分离属性名和值
      const colonIndex = trimmedDeclaration.indexOf(':')
      if (colonIndex === -1) return
      
      const property = trimmedDeclaration.substring(0, colonIndex).trim().toLowerCase()
      const value = trimmedDeclaration.substring(colonIndex + 1).trim()
      
      // 检查是否为允许的样式属性
      const isAllowed = allowedStyles.some(allowedStyle => {
        // 支持前缀匹配，比如 background-color 可以被 background 匹配到
        return property === allowedStyle || property.startsWith(allowedStyle + '-')
      })
      
      if (isAllowed && value) {
        filteredStyles.push(`${property}:${value}`)
      }
    })
    
    return filteredStyles.join(';')
  } catch (error) {
    console.warn('样式过滤失败:', error)
    return styleString
  }
}

// 处理HTML字符串中的内联样式
const processInlineStyles = function(htmlContent) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent
  }
  
  // 使用正则表达式匹配所有带有 style 属性的标签
  return htmlContent.replace(/style\s*=\s*["']([^"']*)["']/gi, function(match, styleContent) {
    const filteredStyle = filterStyleAttributes(styleContent)
    
    // 如果过滤后还有样式内容，保留 style 属性，否则移除整个 style 属性
    if (filteredStyle && filteredStyle.trim()) {
      return `style="${filteredStyle}"`
    } else {
      return ''
    }
  })
}

// 新的富文本处理方法：处理图片和表格
const processRichTextContent = function (htmlContent) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent
  }
  
  let processedHtml = htmlContent
  
  try {
    // 0. 首先过滤样式属性，只保留允许的样式
    processedHtml = processInlineStyles(processedHtml)
    
    // 1. 处理img标签：添加统一的class和最大宽度样式
    processedHtml = processedHtml.replace(/<img([^>]*)>/gi, function(match, attributes) {
      // 检查是否已经有class属性
      const hasClass = /class\s*=\s*["'][^"']*["']/i.test(attributes)
      // 检查是否已经有style属性
      const hasStyle = /style\s*=\s*["'][^"']*["']/i.test(attributes)
      
      let newAttributes = attributes
      
      // 添加或更新class
      if (hasClass) {
        // 如果已有class，添加到现有class中
        newAttributes = newAttributes.replace(/class\s*=\s*["']([^"']*)["']/i, function(classMatch, existingClasses) {
          if (existingClasses.includes('rich-text-img')) {
            return classMatch // 已经有这个class了
          }
          return `class="${existingClasses} rich-text-img"`
        })
      } else {
        // 没有class属性，添加新的
        newAttributes += ' class="rich-text-img"'
      }
      
      // 添加或更新style
      if (hasStyle) {
        // 如果已有style，添加到现有style中
        newAttributes = newAttributes.replace(/style\s*=\s*["']([^"']*)["']/i, function(styleMatch, existingStyles) {
          if (existingStyles.includes('max-width')) {
            return styleMatch // 已经有max-width了
          }
          const separator = existingStyles.trim().endsWith(';') ? '' : ';'
          return `style="${existingStyles}${separator}max-width:100%;"`
        })
      } else {
        // 没有style属性，添加新的
        newAttributes += ' style="max-width:100%;"'
      }
      
      return `<img${newAttributes}>`
    })
    
    // 2. 处理table标签：外层包裹div并添加class，同时为table添加强制样式
    processedHtml = processedHtml.replace(/<table([^>]*)>/gi, function(match, attributes) {
      // 为table添加强制的内联样式
      const tableStyle = 'style="width:100% !important;min-width:100% !important;border-collapse:collapse !important;border:1px solid #e1e2e6 !important;font-size:26rpx !important;"'
      
      // 检查是否已经有style属性
      if (/style\s*=\s*["'][^"']*["']/i.test(attributes)) {
        // 如果已有style，合并样式
        const newAttributes = attributes.replace(/style\s*=\s*["']([^"']*)["']/i, function(styleMatch, existingStyles) {
          return `style="${existingStyles};width:100% !important;min-width:100% !important;border-collapse:collapse !important;border:1px solid #e1e2e6 !important;font-size:26rpx !important;"`
        })
        return `<div class="rich-text-table-wrapper"><table ${newAttributes}>`
      } else {
        // 没有style属性，直接添加
        return `<div class="rich-text-table-wrapper"><table ${attributes} ${tableStyle}>`
      }
    })
    
    // 处理th和td标签，添加边框样式
    processedHtml = processedHtml.replace(/<(th|td)([^>]*)>/gi, function(match, tagName, attributes) {
      const cellStyle = 'style="border:1px solid #e1e2e6 !important;padding:8px 12px !important;text-align:left !important;white-space:nowrap !important;min-width:80px !important;color:#3c3d42 !important;font-size:26rpx !important;line-height:1.4 !important;box-sizing:border-box !important;"'
      
      // 为th添加额外的背景色
      const headerStyle = tagName === 'th' ? 'background-color:#f5f5f5 !important;font-weight:bold !important;color:#333 !important;' : ''
      
      if (/style\s*=\s*["'][^"']*["']/i.test(attributes)) {
        const newAttributes = attributes.replace(/style\s*=\s*["']([^"']*)["']/i, function(styleMatch, existingStyles) {
          return `style="${existingStyles};border:1px solid #e1e2e6 !important;padding:8px 12px !important;text-align:left !important;white-space:nowrap !important;min-width:80px !important;color:#3c3d42 !important;font-size:26rpx !important;line-height:1.4 !important;box-sizing:border-box !important;${headerStyle}"`
        })
        return `<${tagName} ${newAttributes}>`
      } else {
        const finalStyle = `style="border:1px solid #e1e2e6 !important;padding:8px 12px !important;text-align:left !important;white-space:nowrap !important;min-width:80px !important;color:#3c3d42 !important;font-size:26rpx !important;line-height:1.4 !important;box-sizing:border-box !important;${headerStyle}"`
        return `<${tagName} ${attributes} ${finalStyle}>`
      }
    })
    
    // 为每个table的结束标签添加对应的div结束标签
    processedHtml = processedHtml.replace(/<\/table>/gi, '</table></div>')
    
    console.log('富文本内容处理完成')
    return processedHtml
    
  } catch (error) {
    console.error('富文本内容处理失败:', error)
    return htmlContent // 如果处理失败，返回原始内容
  }
}

// 获取JSON文本中的html
function getQuestionJsonText(options) {
  return new QuillDeltaToHtmlConverter(options.ops).convert()
}

// 清除最外层p标签
function clearPLabel(html) {
  if (html.startsWith("<p>") && html.endsWith("</p>")) {
    return html.slice(3, html.length - 4)
  }
  return html
}

// 获取json文本显示内容
const parseQuestionHtml = function (jsonText) {
  let html = ""
  try {
    // 尝试解析JSON文本并标准化换行符
    jsonText = JSON.stringify(JSON.parse(jsonText)).replace(
      /(\r\n|\r|\n)/g,
      "\n"
    )

    if (
      Object.prototype.toString.call(JSON.parse(jsonText)) === "[object Object]"
    ) {
      // 获取并处理问题的JSON文本内容
      html = getQuestionJsonText(JSON.parse(jsonText))

      // 清理段落标签（假设此函数移除不需要的<p>标签）
      html = clearPLabel(html)

      // 解析图片HTML（假设此函数处理图片标签）
      html = parseImageHtml(html)

      // 压缩多个连续换行符为一个换行符
      html = html.replace(/(\n)+/g, "\n")

      // 处理换行：替换单个换行符为<br>
      html = html.replace(/\n/g, "<br>")

      // 确保最外层有<p>标签，并且将单个<br>作为段落分隔
      // 在这里为每个<p>标签添加 class='overflow-three'
      html = html
        .split("<br>")
        .map((paragraph) => paragraph.trim())
        .filter(Boolean)
        .map((paragraph) => `<p class="overflow-three">${paragraph}</p>`)
        .join("")
    } else {
      // 如果不是对象则直接解析图片HTML，并替换换行符
      html = parseImageHtml(jsonText)

      // 压缩多个连续换行符为一个换行符
      html = html.replace(/(\n)+/g, "\n")

      // 替换换行符为<br>
      html = html.replace(/\n/g, "<br>")

      // 包装非对象情况下的html到<p>标签并添加类名
      html = `<p class="overflow-three">${html.replace(
        /<\/?p[^>]*>/gi,
        ""
      )}</p>`
    }
  } catch (error) {
    // 如果解析失败，尝试直接解析图片HTML，并替换换行符
    html = parseImageHtml(jsonText)

    // 压缩多个连续换行符为一个换行符
    html = html.replace(/(\n)+/g, "\n")

    // 替换换行符为<br>
    html = html.replace(/\n/g, "<br>")

    // 包装错误情况下的html到<p>标签并添加类名
    html = `<p class="overflow-three">${html.replace(/<\/?p[^>]*>/gi, "")}</p>`
  }

  return html
}

// 获取图片文本HTML
const parseImageHtml = function (title) {
  console.log("进来没得12333333333333333")
  title = title.toString().replace("<F", "&lt")
  return title.toString().replace(/\[img=.*?\]/g, function (s) {
    const url = s.toString().replace(/\[img=|\((.*)\)\]$/g, "")
    return '<img src="' + url + '" style="max-width:100%"/>'
  })
}

export { parseQuestionHtml, parseImageHtml, processRichTextContent }
