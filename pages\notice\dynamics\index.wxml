<block wx:if="{{isPageLoadComplete}}">
  <common-header show_white="{{true}}" id="commonHeader">
    <view slot="left" class="lefts">
      <image class="left-arrow" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
    </view>
  </common-header>
  <view class="main-content">
    <view class="header-top">
      <view class="title">{{noticeData.title}}</view>
      <view class="label"><text class="adress" wx:if="{{noticeData.exam_type_name}}">{{noticeData.region_province_name}}<text class="dians">·</text>{{noticeData.exam_type_name}}</text><text class="time {{!noticeData.exam_type_name?'no_after':''}}">{{noticeData.release_time}}</text></view>
    </view>
    <view class="content-box bor_bb">
      <view class="content-center">
        <view class="pr-box">
          <view id="richTextContainer" class="rich-text-container">
            <!-- <rich-text id="richTextContent" style="font-size: 32rpx !important;" nodes="{{noticeData.content.body_content}}"></rich-text> -->
            <mp-html content="{{noticeData.content.body_content}}" />
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="action-bar-box">
    <view class="action-bar container flex-justify_between">
      <view class="flex-c">
        <view class="bottom-item" bindtap="tapCustomerService" data-item="{{noticeData.join_group}}" wx:if="{{noticeData.join_group}}">
          <image src="{{noticeData.join_group.icon}}"></image>
          {{noticeData.join_group.btn_title}}
        </view>
        <!-- <view class="bottom-item" bindtap="openExam" wx:if="{{noticeData.type=='article_list'}}">
          <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_exam_list.png"></image>
          考试列表
          <view class="new-box">{{noticeData.detail.article_num}}</view>
        </view> -->
      </view>
      <view class="filter-button {{!noticeData.join_group?'wp100':''}}" bindtap="goDetail">查看相关公告</view>
    </view>
  </view>
</block>