.container-box {
  padding-top: 320rpx;
  text-align: center;
  .image {
    width: 300rpx;
    height: 300rpx;
    margin: 0 auto;
    display: block;
  }
  .text {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    margin-top: 32rpx;
  }
  .wei-btn {
    font-size: 24rpx;
    color: #cd3023;
    width: 240rpx;
    height: 80rpx;
    border-radius: 60rpx;
    border: 1px solid rgba(205, 48, 35, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    margin-top: 80rpx;
  }
}
.gray-bg {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 320rpx 74rpx 0 76rpx;
  .card-box {
    background: #ffffff;
    box-shadow: 0rpx 12rpx 24rpx 2rpx rgba(0, 0, 0, 0.05);
    border-radius: 32rpx;
    box-sizing: border-box;
    padding: 80rpx 48rpx 48rpx 48rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    .web-view-icon {
      width: 96rpx;
      height: 96rpx;
    }
    .title-text {
      font-size: 36rpx;
      color: #22242e;
      font-weight: 500;
      margin-top: 40rpx;
    }
    .gray-box {
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx 32rpx;
      background: #f7f8fa;
      border-radius: 16rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      margin-top: 60rpx;
      word-break: break-all;
    }
    .copy-btn {
      width: 100%;
      height: 80rpx;
      background: #d62828;
      border-radius: 12rpx;
      font-weight: 500;
      color: #ffffff;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 80rpx;
    }
  }
}
