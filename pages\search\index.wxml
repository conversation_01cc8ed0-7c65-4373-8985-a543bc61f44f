<page-meta page-style="background:{{bgColor}}"></page-meta>
<view>
  <common-header show_white="{{show_white}}" title="搜索">
    <view slot="left" class="lefts">
      <image class="left-arrow" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
    </view>
  </common-header>
  <view class="search-box-top {{(!hasSearched || (hasSearched && !isLoading && searchResults.length === 0 && !hasAnyResults)) ? 'no-border' : ''}}">
    <view class="search-box">
      <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search_hui.png"></image>
      <input type="text" bindinput="bindKeyInput" bindconfirm="performSearch" value="{{value}}" placeholder="{{placeholder}}" placeholder-style="color:rgba(194, 197, 204, 1)" />
      <image class="clear" wx:if="{{value}}" bindtap="clearInput" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_input_clear.png"></image>
    </view>
    <view class="menu-list" wx:if="{{hasSearched && !isLoading && hasAnyResults}}">
      <view class="menu-list-item {{index==activeIndex?'active':''}}" bindtap="changeIndex" data-index="{{index}}" wx:for="{{menuList}}" wx:key="index">{{item}}</view>
    </view>
  </view>

  <!-- 未搜索前显示 -->
  <view class="search-box-content" wx:if="{{!hasSearched && !isLoading && examSshow}}">
    <view class="history-box" wx:if="{{historyList.length > 0}}">
      <view class="history-box-top">
        <view class="title">历史搜索</view>
        <image bindtap="deleteHistory" class="delete-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_delete_hui.png"></image>
      </view>
      <view class="history-list">
        <view class="history-list-item" bindtap="selectHistory" data-keyword="{{item}}" wx:for="{{historyList}}" wx:key="index">
          {{item}}
        </view>
      </view>
    </view>

    <view class="select-exams" wx:if="{{examSshow}}">
      <view class="select-exams-top">
        <image mode="widthFix" class="title-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_title.png"></image>
        <view class="more" bindtap="goMore">更多 <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"></image>
        </view>
      </view>
      <view class="exam-list">
        <view class="exam-list-item" bindtap="toJobDetail" data-keyword="{{item.keyword}}" wx:for="{{hotSearchList}}" wx:key="index">
          <image class="num" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_num_{{index + 1}}.png"></image>
          <view class="text text-ellipsis-1">{{item.title}}</view>
          <image wx:if="{{item.isNew}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_new_label.png" class="new" />
        </view>
      </view>
    </view>
  </view>

  <!-- 加载中状态 -->
  <view class="search-box-content" wx:if="{{isLoading}}">
    <view class="loading-box">
      <text>搜索中...</text>
    </view>
  </view>

  <!-- 搜索后有数据时展示 -->
  <view class="search-box-content" wx:if="{{hasSearched && !isLoading && (searchResults.length > 0)}}">
    <block wx:if="{{activeIndex==0}}">
      <view class="all-num">共找到<text class="num">{{noticeTotal}}</text>条相关公告</view>
      <view class="notice-list">
        <notice-card></notice-card>
      </view>
    </block>
    <block wx:elif="{{activeIndex==1}}">
      <view class="all-num">共找到<text class="num">{{jobTotal}}</text>条相关职位</view>
      <view class="notice-list">
        <job-list-card></job-list-card>
      </view>
    </block>
  </view>

  <!-- 搜索无数据时展示 -->
  <empty-default wx:if="{{hasSearched && !isLoading && searchResults.length === 0}}" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/default/search.png" text="暂未搜索到相关内容"></empty-default>
</view>
<modal-default show="{{tipShow}}" title="是否要清空搜索记录" bind:confirm="confirm" bind:cancel="cancel"></modal-default>