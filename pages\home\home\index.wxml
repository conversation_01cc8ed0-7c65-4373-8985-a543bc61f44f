<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>

<block wx:if="{{isRequest}}">
  <view class="page-container {{pageScrollDisabled?'scroll-disabled':''}} {{activeIndex==1?'no-bg':''}}" style="{{pageContainerStyle}}">
    <common-header id="commonHeader" show_white="{{show_white}}">
      <view slot="left" class="left">
        <view class="tab-list {{show_white?'bgf':''}}">
          <view class="tab-list-item {{index == activeIndex?'active':''}}" bindtap="changeTab" data-index="{{index}}" wx:for="{{tabList}}" wx:key="index">
            <image class="titie_img" style="width:{{index == activeIndex?item.select_width:item.width}}rpx" mode="widthFix" src="{{index == activeIndex?item.select_img:item.img}}"></image>
            <image class="line" wx:if="{{index == activeIndex &&index==0}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_tab_line.png"></image>
            <image class="line1" wx:elif="{{index == activeIndex &&index==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_tab_line_1.png"></image>
          </view>
        </view>
      </view>
      <view slot="right">
        <image class="search" bindtap="goSearch" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search_black.png"></image>
      </view>
    </common-header>
    <block wx:if="{{activeIndex==0}}">
      <view class="top-box" style="opacity: {{topBoxVisible ? 1 : 0}}; ">
        <view class="menu-box" wx:if="{{homeData.menu.length}}">
          <view class="menu-box-item" data-item="{{item.cmd_json}}" wx:for="{{homeData.menu}}" wx:key="index" bindtap="goMenuItem">
            <image class="img" src="{{item.img}}"></image>
            {{item.name}}
          </view>
        </view>
      </view>
      <view class="top-card-list" wx:if="{{homeData.ad.length>0}}" style="opacity: {{topBoxVisible ? 1 : 0}}; ">
        <block wx:for="{{homeData.ad}}" wx:key="index">
          <view class="top-card-item" bindtap="goMenu" data-item="{{item.cmd_json}}" wx:if="{{item.type=='article_list'}}" style="background: linear-gradient( 45deg, {{item.data.bg.start_color}} 0%, {{item.data.bg.end_color}} 100%);">
            <image class="bg-img" mode="widthFix" src="{{item.data.bg.right_icon}}"></image>
            <image class="label-img" wx:if="{{item.data.bg.top_tag_icon}}" src="{{item.data.bg.top_tag_icon}}"></image>
            <view class="pr-box">
              <view class="title text-ellipsis-1">{{item.data.title}}</view>
              <view class="position-text">
                <view class="text-item" wx:if="{{item.data.job_num>=0}}">共招<text class="num">{{item.data.job_num}}</text>人</view>
                <view class="text-item" wx:if="{{item.data.need_num>=0}}"><text class="num">{{item.data.need_num}}</text>个职位</view>
              </view>
              <view class="text-item bottom-text" wx:if="{{item.data.article_num>=0}}">共收录<text class="num">{{item.data.article_num}}</text>场考试</view>
            </view>
          </view>
          <view class="top-card-item" wx:elif="{{item.type=='choice_article'}}" bindtap="goMenu" data-item="{{item.cmd_json}}">
            <image class="bg-imgs" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_card_3.png"></image>
            <view class="swiper-box">
              <view class="title-box">
                <image class="title-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_select_exam.png"></image>
                <image class="arrow-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_yuan_right.png"></image>
              </view>
              <view class="content-box">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_right_jiao.png" class="jiao"></image>
                <block wx:if="{{item.data.list.length>1}}">
                  <swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" circular="{{true}}" interval="{{5000}}" bindchange="handleBannerSwiperChange">
                    <swiper-item wx:for="{{item.data.list}}" wx:key="index" catchtap="goNotice" data-item="{{item}}">
                      <view class="text-box">
                        <view class="title text-ellipsis-2">
                          <image class="label-imgs" wx:if="{{item.type=='article_list'}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_yuan_text_hui.png"></image>{{item.title}}
                        </view>
                        <view class="label" wx:if="{{item.need_num}}">招录<text class="num">{{item.need_num}}</text>人</view>
                      </view>
                    </swiper-item>
                  </swiper>
                  <view class="custom-indicator">
                    <view wx:for="{{item.data.list}}" wx:key="index" class="indicator-dot {{index === bannerSwiperIndex?'active':''}}"></view>
                  </view>
                </block>
                <block wx:else>
                  <view class="swiper" wx:for="{{item.data.list}}">
                    <view class="text-box">
                      <view class="title text-ellipsis-2">
                        <image class="label-imgs" wx:if="{{item.type=='article_list'}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_yuan_text_hui.png"></image>{{item.title}}
                      </view>
                      <view class="label" wx:if="{{item.need_num}}">招录<text class="num">{{item.need_num}}</text>人</view>
                    </view>
                  </view>
                </block>
              </view>
            </view>
          </view>
        </block>
      </view>
      <view class="subscribe-box" style="opacity: {{topBoxVisible ? 1 : 0}}; " catchtap="goSubscribe" wx:if="{{isSubscribe}}">
        <view class="close-box" catchtap="closeIsSubscribe">
          <image class="close" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_subscribe_close.png"></image>
        </view>
        <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_subscribe_banner.png"></image>
      </view>
    </block>
    <!-- 主要内容区域 -->
    <view class="main-content" wx:if="{{activeIndex==0}}">
      <view class="main-top {{showPopupFilterMenu || menuSticky ? 'bgf' : ''}}" wx:if="{{menuList.length>0}}" style="--header-height: {{headerHeight}}px">
        <!-- 使用业务菜单组件 -->
        <business-menu id="announcement-business-menu" isLogin="{{isLogin}}" menu-list="{{menuList}}" selectData="{{noticeSelectForTemplate}}" active-expanded="{{activeExpanded}}" tab-type="announcement" showPopupFilterMenu="{{showPopupFilterMenu}}" bind:menuClick="handleMenuClick" />

        <!-- 地区筛选列表 - 在business-menu组件平级位置 -->
        <view class="region-list-container" wx:if="{{showRegionList}}">
          <scroll-view scroll-x class="region-list-ul" show-scrollbar="{{false}}" enhanced>
            <block wx:if="{{isLogin}}">
              <view class="region-list-item {{filterUtils.isFilterItemOptionSelected(item.value, noticeSelectForTemplate.apply_region) ? 'active' : ''}}" wx:for="{{noticeMenuData.apply_region.data[0].list}}" wx:key="id" data-item="{{item}}" bindtap="handleRegionItemClick">
                {{item.name || item.text}}
              </view>
            </block>
            <block wx:else>
              <view class="region-list-item {{filterUtils.isFilterItemOptionSelected(item.value, noticeSelectForTemplate.apply_region) ? 'active' : ''}}" wx:for="{{cachedRegions}}" wx:key="id" data-item="{{item}}" bindtap="handleRegionItemClick">
                {{item.name || item.text}}
              </view>
            </block>
          </scroll-view>
          <view class="right-box" bindtap="handleAddRegionClick">
            <image class="add-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_region_add.png"></image>
          </view>
        </view>
      </view>
      <view class="content-box">
        <block wx:if="{{articleList.length>0}}">
          <notice-card list="{{articleList}}" style="opacity: {{topBoxVisible ? 1 : 0}}; "></notice-card>
        </block>
        <empty-default imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" wx:if="{{articleList.length === 0}}" text="暂未找到适合你的公告~"></empty-default>
      </view>
    </view>
    <view class="main-content mt0" wx:if="{{activeIndex==1}}">
      <view class="main-top pt0 {{showPopupFilterMenu || menuSticky ? 'bgf' : ''}}" style="--header-height: {{headerHeight}}px">
        <!-- 使用业务菜单组件 -->
        <business-menu id="news-business-menu" isLogin="{{isLogin}}" isExam hideRegionList="{{true}}" menu-list="{{examMenuList}}" selectData="{{examSelectForTemplate}}" active-expanded="{{activeExpanded}}" tab-type="news" showPopupFilterMenu="{{showPopupFilterMenu}}" bind:menuClick="handleNewsMenuClick" />
      </view>
      <view class="content-box">
        <view class="news-list" wx:if="{{newsArticleList.length>0}}">
          <block wx:for="{{newsArticleList}}" wx:key="index">
            <view class="news-list-item" wx:if="{{item.title}}" data-item="{{item}}" catch:tap="toNewsDetail">
              <view class="title text-ellipsis-2">{{item.title}}</view>
              <view class="time" wx:if="{{item.release_time}}">{{item.release_time}}</view>
            </view>
          </block>
        </view>
        <empty-default imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" wx:if="{{newsArticleList.length === 0}}" text="暂无更多考试动态~"></empty-default>
      </view>
    </view>
  </view>
</block>
<!-- 菜单筛选弹窗 - 使用 Slot 方式 -->
<popup-menu-filter show="{{showPopupFilterMenu}}" popup-top="{{overlayTop}}" bind:close="handlePopupClose" custom-class="global-positioned">
  <!-- 菜单筛选内容作为 slot 传入 -->
  <menu-filter-content menu-type="{{activeExpanded}}" popup-title="{{popupTitle}}" show-footer="{{showFooter}}" animation-show="{{showPopupFilterMenu}}" bind:autoClose="handlePopupClose" bind:close="handlePopupClose">

    <!-- 公告 -->
    <block wx:if="{{activeIndex == 0}}">
      <block wx:if="{{activeExpanded == 'exam_type'}}">
        <exam-content show="{{showPopupFilterMenu}}" exam-list="{{noticeMenuData.exam_type.data}}" isMultipleChoice="{{true}}" selected="{{noticeSelectForTemplate.exam_type}}" filterKey="exam_type" bind:confirm="handleNoticeMenuFilterConfirm" />
      </block>
      <block wx:if="{{activeExpanded == 'filter_list'}}">
        <view class="popu-content">
          <view class="popu-content-c">
            <group-list show="{{showPopupFilterMenu}}" dataList="{{noticeMenuData.filter_list.data}}" selected="{{noticeSelectForTemplate.filter_list}}" filterKey="filter_list" bind:reset="handleNoticeMenuFilterReset" bind:confirm="handleNoticeMenuFilterConfirm" />
          </view>
        </view>
      </block>
    </block>
    <block wx:elif="{{activeIndex === 1}}">
      <!-- 地区选择（公告Tab和考试动态Tab共用） -->
      <view class="popu-content" wx:if="{{activeExpanded == 'apply_region'}}">
        <region-select-box noPadding show="{{showPopupFilterMenu}}" filterKey="apply_region" isShowBg="{{false}}" popupHeight="50vh" selectedRegions="{{examSelectForTemplate.apply_region}}" inPopup="{{true}}" bind:confirmSelection="handleRegionConfirmSelection" />
      </view>
      <view class="popu-content" wx:if="{{activeExpanded == 'exam_type'}}">
        <exam-content show="{{showPopupFilterMenu}}" exam-list="{{examMenuData.exam_type.data}}" isMultipleChoice="{{true}}" selected="{{examSelectForTemplate.exam_type}}" filterKey="exam_type" bind:confirm="handleExamMenuFilterConfirm" />
      </view>
    </block>
  </menu-filter-content>
</popup-menu-filter>


<!-- 底部导航栏 -->
<home-tabbar active="home" />