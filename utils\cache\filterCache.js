import { setCache, getCache } from "./core/core"

/**
 * 设置筛选条件缓存
 * @param {Object} filterConditions - 筛选条件对象
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 */
export function setFilterConditionsCache(
  filterConditions,
  pageType = "announcement"
) {
  const cacheKey = `${pageType}_filter_conditions`
  return setCache(cacheKey, filterConditions)
}

/**
 * 获取筛选条件缓存
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 * @returns {Object} 筛选条件对象
 */
export function getFilterConditionsCache(pageType = "announcement") {
  const cacheKey = `${pageType}_filter_conditions`
  return getCache(cacheKey) || null
}

/**
 * 清除筛选条件缓存
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 */
export function clearFilterConditionsCache(pageType = "announcement") {
  const cacheKey = `${pageType}_filter_conditions`
  return setCache(cacheKey, null)
}

/**
 * 设置考试类型缓存
 * @param {Array} examTypes - 选中的考试类型数组
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 */
export function setSelectedExamTypesCache(
  examTypes,
  pageType = "announcement"
) {
  const cacheKey = `${pageType}_exam_types_selected`
  return setCache(cacheKey, examTypes)
}

/**
 * 获取考试类型缓存
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 * @returns {Array} 选中的考试类型数组
 */
export function getSelectedExamTypesCache(pageType = "announcement") {
  const cacheKey = `${pageType}_exam_types_selected`
  return getCache(cacheKey) || []
}

/**
 * 设置筛选选项缓存
 * @param {Object} filterOptions - 筛选选项对象
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 */
export function setSelectedFilterOptionsCache(
  filterOptions,
  pageType = "announcement"
) {
  const cacheKey = `${pageType}_filter_options_selected`
  return setCache(cacheKey, filterOptions)
}

/**
 * 获取筛选选项缓存
 * @param {string} pageType - 页面类型 ("announcement" | "news" | "collection" | "job")
 * @returns {Object} 筛选选项对象
 */
export function getSelectedFilterOptionsCache(pageType = "announcement") {
  const cacheKey = `${pageType}_filter_options_selected`
  return getCache(cacheKey) || null
}

/**
 * 设置选中状态缓存 (noticeSelectForTemplate)
 * @param {Object} noticeSelectForTemplate - 选中状态对象
 * @param {string} pageType - 页面类型 ("announcement" | "news")
 */
export function setNoticeSelectForTemplateCache(
  noticeSelectForTemplate,
  pageType = "announcement"
) {
  const cacheKey = `${pageType}_notice_select_for_template`
  return setCache(cacheKey, noticeSelectForTemplate)
}

/**
 * 获取选中状态缓存 (noticeSelectForTemplate)
 * @param {string} pageType - 页面类型 ("announcement" | "news")
 * @returns {Object} 选中状态对象
 */
export function getNoticeSelectForTemplateCache(pageType = "announcement") {
  const cacheKey = `${pageType}_notice_select_for_template`
  return getCache(cacheKey) || {}
}

/**
 * 清除选中状态缓存 (noticeSelectForTemplate)
 * @param {string} pageType - 页面类型 ("announcement" | "news")
 */
export function clearNoticeSelectForTemplateCache(pageType = "announcement") {
  const cacheKey = `${pageType}_notice_select_for_template`
  return setCache(cacheKey, {})
}

/**
 * 设置考试动态选中状态缓存 (examSelectForTemplate)
 * @param {Object} examSelectForTemplate - 考试动态选中状态对象
 * @param {string} pageType - 页面类型 ("news" | "exam")
 */
export function setExamSelectForTemplateCache(
  examSelectForTemplate,
  pageType = "news"
) {
  const cacheKey = `${pageType}_exam_select_for_template`
  return setCache(cacheKey, examSelectForTemplate)
}

/**
 * 获取考试动态选中状态缓存 (examSelectForTemplate)
 * @param {string} pageType - 页面类型 ("news" | "exam")
 * @returns {Object} 考试动态选中状态对象
 */
export function getExamSelectForTemplateCache(pageType = "news") {
  const cacheKey = `${pageType}_exam_select_for_template`
  return getCache(cacheKey) || {}
}

/**
 * 清除考试动态选中状态缓存 (examSelectForTemplate)
 * @param {string} pageType - 页面类型 ("news" | "exam")
 */
export function clearExamSelectForTemplateCache(pageType = "news") {
  const cacheKey = `${pageType}_exam_select_for_template`
  return setCache(cacheKey, {})
}

/**
 * 设置合辑选中状态缓存 (collectionSelectForTemplate)
 * @param {Object} collectionSelectForTemplate - 合辑选中状态对象
 * @param {string} pageType - 页面类型 ("collection")
 */
export function setCollectionSelectForTemplateCache(
  collectionSelectForTemplate
) {
  const cacheKey = "collection_select_for_template"
  return setCache(cacheKey, collectionSelectForTemplate)
}

/**
 * 获取合辑选中状态缓存 (collectionSelectForTemplate)
 * @param {string} pageType - 页面类型 ("collection")
 * @returns {Object} 合辑选中状态对象
 */
export function getCollectionSelectForTemplateCache() {
  const cacheKey = "collection_select_for_template"
  return getCache(cacheKey) || {}
}

/**
 * 清除合辑选中状态缓存 (collectionSelectForTemplate)
 * @param {string} pageType - 页面类型 ("collection")
 */
export function clearCollectionSelectForTemplateCache() {
  const cacheKey = "collection_select_for_template"
  return setCache(cacheKey, {})
}

/**
 * 设置职位选中状态缓存 (jobSelectForTemplate)
 * @param {Object} jobSelectForTemplate - 合辑选中状态对象
 * @param {string} pageType - 页面类型 ("job")
 */
export function setJobSelectForTemplateCache(jobSelectForTemplate) {
  const cacheKey = "job_select_for_template"
  return setCache(cacheKey, jobSelectForTemplate)
}

/**
 * 获取职位选中状态缓存 (jobSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 * @returns {Object} 职位选中状态对象
 */
export function getJobSelectForTemplateCache() {
  const cacheKey = "job_select_for_template"
  return getCache(cacheKey) || {}
}

/**
 * 清除职位选中状态缓存 (jobSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 */
export function clearJobSelectForTemplateCache() {
  const cacheKey = "job_select_for_template"
  return setCache(cacheKey, {})
}

/**
 * 设置职位筛选选中状态缓存 (jobFilterSelectForTemplate)
 * @param {Object} jobFilterSelectForTemplate - 职位筛选选中状态对象
 * @param {string} pageType - 页面类型 ("job")
 */
export function setJobFilterSelectForTemplateCache(jobFilterSelectForTemplate) {
  const cacheKey = "job_filter_select_for_template"
  return setCache(cacheKey, jobFilterSelectForTemplate)
}

/**
 * 获取职位筛选选中状态缓存 (jobFilterSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 * @returns {Object} 职位选中状态对象
 */
export function getJobFilterSelectForTemplateCache() {
  const cacheKey = "job_filter_select_for_template"
  return getCache(cacheKey) || {}
}

/**
 * 清除职位筛选选中状态缓存 (jobFilterSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 */
export function clearJobFilterSelectForTemplateCache() {
  const cacheKey = "job_filter_select_for_template"
  return setCache(cacheKey, {})
}

/**
 * 设置公告详情动态红点缓存
 *
 */
export function setOfficialNewsCache(officialNewsCache) {
  const cacheKey = "official_news"
  return setCache(cacheKey, officialNewsCache)
}

/**
 * 获取公告详情动态红点缓存
 *
 *
 */
export function getOfficialNewsCache() {
  const cacheKey = "official_news"
  return getCache(cacheKey) || {}
}

/**
 * 清除公告详情动态红点缓存
 *
 */
export function clearOfficialNewsCache() {
  const cacheKey = "official_news"
  return setCache(cacheKey, {})
}

/**
 * 设置职位详情筛选选中状态缓存 (jobDetailSelectForTemplate)
 * @param {Object} jobDetailSelectForTemplate - 职位详情筛选选中状态对象
 * @param {string} pageType - 页面类型 ("job")
 */
export function setJobDetailSelectForTemplateCache(jobDetailSelectForTemplate) {
  const cacheKey = "job_detail_select_for_template"
  return setCache(cacheKey, jobDetailSelectForTemplate)
}

/**
 * 获取职位详情筛选选中状态缓存 (jobDetailSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 * @returns {Object} 职位详情筛选选中状态对象
 */
export function getJobDetailSelectForTemplateCache() {
  const cacheKey = "job_detail_select_for_template"
  return getCache(cacheKey) || {}
}

/**
 * 清除职位详情筛选选中状态缓存 (jobDetailSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 */
export function clearJobDetailSelectForTemplateCache() {
  const cacheKey = "job_detail_select_for_template"
  return setCache(cacheKey, {})
}

/**
 * 设置职位详情考试弹窗筛选选中状态缓存 (jobDetailPopuSelectForTemplate)
 * @param {Object} jobDetailSelectForTemplate - 职位详情筛选选中状态对象
 * @param {string} pageType - 页面类型 ("job")
 */
export function setJobDetailPopuSelectForTemplateCache(
  jobDetailPopuSelectForTemplate
) {
  const cacheKey = "job_detail_popu_select_for_template"
  return setCache(cacheKey, jobDetailPopuSelectForTemplate)
}

/**
 * 获取职位详情考试弹窗筛选选中状态缓存 (jobDetailPopuSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 * @returns {Object} 职位详情筛选选中状态对象
 */
export function getJobDetailPopuSelectForTemplateCache() {
  const cacheKey = "job_detail_popu_select_for_template"
  return getCache(cacheKey) || {}
}

/**
 * 清除职位详情考试弹窗筛选选中状态缓存 (jobDetailSelectForTemplate)
 * @param {string} pageType - 页面类型 ("job")
 */
export function clearJobDetailPopuSelectForTemplateCache() {
  const cacheKey = "job_detail_popu_select_for_template"
  return setCache(cacheKey, {})
}
