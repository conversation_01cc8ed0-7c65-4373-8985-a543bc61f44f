const ROUTER = require("@/services/mpRouter")
Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true,
  },
  properties: {
    list: {
      type: Array,
      value: [],
    },
  },
  data: {},
  methods: {
    toJobDetail(e) {
      const { id } = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/pages/job/detail/index",
        query: {
          id,
        },
      })
    },
  },
})
