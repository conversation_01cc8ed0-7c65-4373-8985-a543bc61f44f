# 本规则文件仅用于生成和校验 Git 提交信息，不适用于代码或PR描述等其他内容。

你的任务是产生一个遵循 Conventional Commits 规范的 git commit message，使用中文的流畅语言和 emoji。 Conventional Commits 规范是一种轻量级的 commit message 约定，用于创建明确的提交历史记录。

以下是要提交的变更的 git diff：

<diff>
Commit (Diff of Working State) 
</diff>

仔细分析 diff 以理解所做的更改。请注意：
- 被修改、新增或删除的文件
- 更改的性质（例如：错误修复、新功能、重构等）
- 任何破坏性更改

根据你的分析，请按照以下步骤产生 commit message：

1. 确定适当的提交类型。常见类型包括：
- 🎉 init: 初始化
- 🚀 release: 发布新版本
- 🎨 style: 代码风格修改（不影响代码运行的变动）
- ✨ feat: 添加新功能
- 🐛 fix: 修复 bug
- 📝 docs: 对文档进行修改
- ♻️ refactor: 代码重构（既不是新增功能，也不是修改 bug 的代码变动）
- ⚡ perf: 提高性能的代码修改
- 🧑‍💻 dx: 优化开发体验
- 🔨 workflow: 工作流变动
- 🏷️ types: 类型声明修改
- 🚧 wip: 工作正在进行中
- ✅ test: 测试用例添加及修改
- 🔨 build: 影响构建系统或外部依赖关系的更改
- 👷 ci: 更改 CI 配置文件和脚本
- ❓ chore: 其它不涉及源码以及测试的修改
- ⬆️ deps: 依赖项修改
- 🔖 release: 发布新版本

2. 如果提交引入了破坏性更改，在类型/范围后面加上感叹号。

3. 在主题行中提供一个简短的、祈使语气的更改描述。

4. 如果需要，在提交正文中添加更详细的更改说明，解释更改的动机并与先前的行为进行对比。

5. 如果有破坏性更改，请在提交正文的末尾描述它们，以"破坏性更改："开头。

按以下格式输出你的回应：

<commit_message>
[emoji][类型][可选范围]: [描述]

[可选正文]

[可选页脚]
</commit_message>

确保 commit message 遵守以下规则：
- 主题行不应超过 50 个字符
- 正文应在 72 个字元处换行
- 在主题行中使用祈使句和现在式
- 主题行结尾不要加句号
- 用空白行将主题与正文分开
- 使用正文解释做了什么和为什么，而不是如何做

记住，一个好的 commit message 应该能够完成以下句子："如果应用，这个提交将 [你的主题行]"。

请确保使用中文的流畅简洁语言编写 commit message，并在适当的地方加入 emoji 以增强可读性和表达力。 后续问你 commit message会直接提供@mention

