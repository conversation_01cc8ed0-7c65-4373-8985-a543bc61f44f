<web-view wx:if="{{url}}" src="{{url}}"></web-view>
<view class="container-box {{ isNotSupportUrl?'gray-bg':''}}" wx:else>
  <block wx:if="{{ isNotSupportUrl }}">
    <view class="card-box">
      <image class="web-view-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/default/web-view-icon.png" mode="" />
      <view class="title-text">前往浏览器内打开该链接</view>
      <view class="gray-box">
        <view class="text-ellipsis-3">{{ isNotSupportUrl }}</view>
      </view>
      <view class="copy-btn" bindtap="copyUrl">复制链接</view>
    </view>
  </block>
  <block wx:if="{{!isNotSupportUrl && isUnViewText}}">
    <image class="image" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/default/default_no.png"></image>
    <view style="width: 100%; text-align: center;">
      <text class="text">该文件不可预览，转发后查看</text>
    </view>
    <view class="wei-btn" bindtap="forward">转发至微信</view>
  </block>
</view>