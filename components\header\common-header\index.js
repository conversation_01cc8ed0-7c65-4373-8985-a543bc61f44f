// components/home-header/index.js
const BASE_CACHE = require("@/utils/cache/baseCache")
const ROUTER = require("@/services/mpRouter")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    show_white: {
      type: Boolean,
      value: false,
    },
    isFixed: {
      type: Boolean,
      value: true,
    },
    title: {
      type: String,
      value: "",
    },
    show_bg: {
      type: String,
      value: "",
    },
    defaultBgColor: {
      type: String,
      value: "",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    cacheStting: {},
    show: false,
    campusShow: false,
    provinceShow: false,
    navigationBarHeight: "",
    tabList: ["公告", "考试动态"],
    activeIndex: 0,
    // 胶囊按钮信息
    menuButtonInfo: {},
    // 搜索按钮的右边距
    searchButtonPaddingRight: "32rpx",
  },
  lifetimes: {
    attached() {
      this.setData({ isLoad: true })
      this.getNavHeight()
      this.calculateSearchButtonPosition()
    },
    detached() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  pageLifetimes: {
    // 组件所在的页面被展示时执行
    show() {
      this.getNavHeight()
      this.calculateSearchButtonPosition()
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    changeTab(e) {
      console.log(e.currentTarget.dataset.index)
      const index = e.currentTarget.dataset.index
      this.setData({
        activeIndex: index,
      })
    },
    getNavHeight() {
      let menuInfo = wx.getMenuButtonBoundingClientRect()
      console.log("执行了没哦", menuInfo)
      this.setData({
        navigationBarHeight: menuInfo.top,
        menuButtonInfo: menuInfo,
      })
    },

    /**
     * 计算搜索按钮位置
     */
    calculateSearchButtonPosition() {
      try {
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
        const systemInfo = wx.getSystemInfoSync()

        // 获取胶囊按钮的位置信息
        const {
          width: menuWidth,
          right: menuRight,
          left: menuLeft,
        } = menuButtonInfo
        const { windowWidth } = systemInfo

        // 计算搜索按钮应该距离右边的距离
        // 搜索按钮放在胶囊左侧，保持16px间距
        const searchButtonRight = windowWidth - menuLeft + 12 // 16px间距

        // 转换为rpx单位 (750rpx = windowWidth)
        const paddingRightRpx = (searchButtonRight * 750) / windowWidth

        this.setData({
          menuButtonInfo,
          searchButtonPaddingRight: `${Math.ceil(paddingRightRpx)}rpx`,
        })

        console.log("胶囊按钮信息:", menuButtonInfo)
        console.log("计算的搜索按钮右边距:", `${Math.ceil(paddingRightRpx)}rpx`)
      } catch (error) {
        console.error("计算搜索按钮位置失败:", error)
        // 降级方案：使用默认值
        this.setData({
          searchButtonPaddingRight: "120rpx",
        })
      }
    },
  },
})
