<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>
<block wx:if="{{isPageLoadComplete}}">
  <block wx:if="{{pageType=='collection'}}">
    <!-- 专题合辑 -->
    <view class="page-container {{pageScrollDisabled?'scroll-disabled':''}}" style="background-image: url({{noticeData.detail.cover_img}});">
      <common-header id="commonHeader" show_white="{{show_white}}">
        <view slot="left" class="lefts">
          <image class="left-arrow" wx:if="{{show_white}}" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
          <image class="left-arrow" wx:else catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_left_white.png"></image>
        </view>
        <view slot="right" class="rights" wx:if="{{noticeData}}">
          <button-authorize isBindPhone="{{isLogin}}" bind:onAuthorize="changeCollect">
            <image class="collection-img" wx:if="{{noticeData.is_follow==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collect_select.png"></image>
            <block wx:else>
              <image class="collection-img" wx:if="{{!show_white}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_star_white.png"></image>
              <image class="collection-img" wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collect.png"></image>
            </block>
          </button-authorize>

        </view>
      </common-header>
      <view class="top-content">
        <view class="title">{{noticeData.detail.title}}</view>
        <view class="label">{{noticeData.detail.summary}}</view>
      </view>
      <view class="center-content">
        <view class="main-top {{showPopupFilterMenu || menuSticky ? 'bgf' : ''}}" style="--header-height: {{headerHeight}}px">
          <!-- 使用业务菜单组件 -->
          <business-menu id="announcement-business-menu" isLogin="{{isLogin}}" menu-list="{{menuList}}" selectData="{{collectionSelectForTemplate}}" active-expanded="{{activeExpanded}}" tab-type="collection" showPopupFilterMenu="{{showPopupFilterMenu}}" bind:menuClick="handleCollectionMenuClick" />
        </view>
        <view class="update-time-all" wx:if="{{noticeData.detail.total_num>0}}">
          共收录<text class="num">{{noticeData.detail.total_num}}</text>场考试<text class="time">更新时间：{{noticeData.detail.update_time}}</text>
        </view>
        <view class="list-content">
          <notice-card list="{{articleList}}"></notice-card>
          <empty-default wx:if="{{articleList.length === 0}}" text="暂无更多公告~"></empty-default>
        </view>
      </view>
    </view>
    <!-- 菜单筛选弹窗 -->
    <popup-menu-filter show="{{showPopupFilterMenu}}" popup-top="{{overlayTop}}" bind:close="handlePopupClose" custom-class="global-positioned">
      <!-- 菜单筛选内容作为 slot 传入 -->
      <menu-filter-content menu-type="{{activeExpanded}}" popup-title="{{popupTitle}}" show-footer="{{showFooter}}" animation-show="{{showPopupFilterMenu}}" bind:autoClose="handlePopupClose" bind:close="handlePopupClose">
        <!-- 排序选择 -->
        <view class="popu-content" wx:if="{{activeExpanded == 'sort_list'}}">
          <view class="popu-content-c">
            <sort-list show="{{showPopupFilterMenu}}" sortList="{{collectionMenuData.sort_list.data}}" isMultipleChoice="{{false}}" selected="{{collectionSelectForTemplate.sort_list}}" filterKey="sort_list" bind:confirm="handleNoticeMenuFilterConfirm"></sort-list>
          </view>
        </view>
        <block wx:if="{{activeExpanded == 'exam_type'}}">
          <exam-content show="{{showPopupFilterMenu}}" exam-list="{{collectionMenuData.exam_type.data}}" isMultipleChoice="{{true}}" selected="{{collectionSelectForTemplate.exam_type}}" filterKey="exam_type" bind:confirm="handleNoticeMenuFilterConfirm" />
        </block>
        <!-- 筛选选项 -->
        <view class="popu-content" wx:if="{{activeExpanded == 'filter_list'}}">
          <view class="popu-content-c">
            <group-list show="{{showPopupFilterMenu}}" dataList="{{collectionMenuData.filter_list.data}}" selected="{{collectionSelectForTemplate.filter_list}}" filterKey="filter_list" bind:reset="handleNoticeMenuFilterReset" bind:confirm="handleNoticeMenuFilterConfirm" />
          </view>
        </view>

        <!-- 地区选择 -->
        <view class="popu-content" wx:if="{{activeExpanded == 'region'}}">
          <region-select-box noPadding show="{{showPopupFilterMenu}}" filterKey="region" isShowBg="{{false}}" popupHeight="50vh" selectedRegions="{{collectionSelectForTemplate.region}}" inPopup="{{true}}" bind:confirmSelection="handleRegionConfirmSelection" />
        </view>
      </menu-filter-content>
    </popup-menu-filter>
  </block>
  <block wx:if="{{pageType=='detail'}}">
    <block wx:if="{{isRequest}}">
      <view class="page-container {{pageScrollDisabled ? 'scroll-disabled' : ''}}" style="{{pageContainerStyle}}">
        <common-header show_white="{{true}}" id="commonHeader">
          <view slot="left" class="lefts">
            <image class="left-arrow" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
          </view>
          <view slot="right" class="rights" wx:if="{{noticeData}}">
            <image class="collection-img" wx:if="{{noticeData.detail.parent_article_lists.length>0}}" bindtap="openPopu" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collection.png"></image>
            <button-authorize isBindPhone="{{isLogin}}" bind:onAuthorize="changeCollect">
              <image class="collection-img" wx:if="{{noticeData.is_follow==1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collect_select.png"></image>
              <image class="collection-img" wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_collect.png"></image>
            </button-authorize>
          </view>
        </common-header>
        <view class="main-content {{indexToTabKey[activeIndex]=='position'?'pt120':''}}">
          <view class="header-top">
            <view class="title">{{noticeData.detail.title}}</view>
            <view class="label"><text class="adress">{{noticeData.detail.region_province_name}}<text class="dians">·</text>{{noticeData.detail.exam_type_name}}</text><text class="time">{{noticeData.detail.release_time}}</text></view>
          </view>
          <view class="content-box-top" wx:if="{{tabList.length>1}}" style="--header-height: {{headerHeight}}px">
            <view class="tab-list">
              <view class="tab-list-item {{index == activeIndex?'active':''}}" bindtap="changeTab" data-item="{{item}}" data-index="{{index}}" wx:for="{{tabList}}" wx:key="index">{{item}}
                <image class="dian" wx:if="{{indexToTabKey[index]=='official'&&redDot}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_dian.png"></image>
              </view>
            </view>
            <view class="search-box" bindtap="goSearch">
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search.png"></image>
              <input disabled type="text" placeholder-style="color:rgba(194, 197, 204, 1)" placeholder="搜职位" />
            </view>
          </view>
          <!-- 公告详情 -->
          <block wx:if="{{indexToTabKey[activeIndex]=='detail'}}">
            <view class="content-box  {{tabList.length>1?'':'pt32'}}">
              <view class="content-center">
                <view class="notice-box" wx:if="{{noticeData.detail.timeline.length>0}}">
                  <view class="notice-box-top {{noticeData.detail.timeline.length>0?'':'bt_0'}}">
                    <view class="left">
                      <block wx:if="{{noticeData.detail.need_num>0 || noticeData.detail.job_num>0}}">
                        <text class="left-text" wx:if="{{noticeData.detail.need_num>=0}}">共招<text class="num">{{noticeData.detail.need_num}}</text>人</text>
                        <text class="left-text" wx:if="{{noticeData.detail.job_num>=0}}"><text class="num">{{noticeData.detail.job_num}}</text>个职位</text>
                      </block>
                      <text class="left-text" wx:else>招录信息待更新</text>
                    </view>
                    <view class="status" wx:if="{{noticeData.detail.apply_status.text}}" style="color:{{noticeData.detail.apply_status.color}}">{{noticeData.detail.apply_status.text}}</view>
                  </view>
                  <view class="time-line" wx:if="{{noticeData.detail.timeline.length>0}}">

                    <view class="time-line-item {{item.is_progressing?'active':''}}" wx:for="{{noticeData.detail.timeline}}" wx:key="index">
                      <block wx:if="{{item.type == 'written_exam_time'}}">
                        <view class="dian"></view>
                        <view class="text-word">{{item.title}}</view>
                        <view style="flex:1;">
                          <block wx:for="{{item.data}}" wx:key="secondIndex">
                            <view style="display: flex;margin-bottom: 5rpx;">
                              <view class="time" style="flex:none">{{item.time}}</view>
                              <view class="time" style="flex:none">{{item.text}}</view>
                            </view>
                          </block>
                        </view>
                      </block>
                      <block wx:else>
                        <view class="dian"></view>
                        <view class="text-word">{{item.title}}</view>
                        <view class="time">{{item.text}}</view>
                      </block>
                    </view>
                  </view>
                </view>
                <view class="pr-box" wx:if="{{noticeData.detail.content.body_content}}">
                  <view id="richTextContainer" class="rich-text-container {{isExpanded ? 'expanded' : 'collapsed'}}" style="max-height: {{isExpanded ? 'none' : maxHeight + 'px'}}; overflow: hidden;">
                    <!-- <rich-text id="richTextContent" class="richTextContent" nodes="{{noticeData.detail.content.body_content}}"></rich-text> -->
                    <mp-html id="richTextContent" class="richTextContent" tagStyle content="{{ noticeData.detail.content.body_content}}" />
                  </view>
                  <view class="bottom-pos" wx:if="{{showToggle}}">
                    <view class="pos-box">
                      <view class="gradient-height" wx:if="{{!isExpanded}}"></view>
                      <view class="see-all {{isExpanded ? '' : 'collapsed'}}" bindtap="toggleText">展开查看全文<image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_select.png"></image>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="see-address" bindtap="openExam" wx:if="{{ tabList.length>1&&noticeData.type=='article_list' && noticeData.detail.article_num>0}}">
                  点击查看各地区及单位考试列表
                  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_red.png"></image>
                </view>
                <view class="attachment-box" wx:if="{{noticeData.detail.content.attachment.length>0}}">
                  <view class="title">附件</view>
                  <view class="informaiton-list">
                    <view class="informaiton-list-item" wx:for="{{noticeData.detail.content.attachment}}" data-item="{{item}}" wx:key="index" catch:tap="tapAttachment">
                      <image class="type-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_information_link.png"></image>
                      <text class="text">{{item.name}}</text>
                      <image class="right-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"></image>
                    </view>
                  </view>
                </view>
                <view class="originally" wx:if="{{noticeData.detail.release_source}}">来源：<text class="title">{{noticeData.detail.release_source}}</text>
                  <view style="display: inline-block;" wx:if="{{noticeData.detail.source_url}}" data-item="{{noticeData.detail.source_url}}" catch:tap="copyUrl">
                    <image class="copy-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_copy_blue.png"></image>
                    <text class="copy-c">复制</text>
                  </view>
                </view>
              </view>
            </view>
            <!-- 热门问答 -->
            <view class="popular-answer " wx:if="{{noticeData.detail.hot_qna}}">
              <view class="item-title">备考热门问答</view>
              <view class="answer-list" wx:if="{{noticeData.detail.hot_qna.qna_list.length>0}}">
                <view class="answer-list-item" wx:for="{{noticeData.detail.hot_qna.qna_list}}">
                  <view class="ask-box">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_ask.png"></image>
                    <text class="text-t">{{item.q}}</text>
                  </view>
                  <view class="answer-box">
                    <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_answer.png"></image>
                    <text class="text-t">{{item.a}}</text>
                  </view>
                </view>
              </view>
              <view class="more-button" wx:if="{{noticeData.detail.hot_qna.consult}}" data-item="{{noticeData.detail.hot_qna.consult}}" catch:tap="tapCustomerService">{{noticeData.detail.hot_qna.consult.btn_title}}
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"></image>
              </view>
            </view>
            <!-- 工具 -->
            <view class="tool-box  {{(noticeData.detail.written_exam_prepare.length>0 || noticeData.detail.bottom_ad.length>0)?'bor_bb':''}}" wx:if="{{noticeData.detail.exam_tool.length>0}}">
              <view class="item-title">考试工具</view>
              <view class="tool-list">
                <view class="tool-list-item" wx:for="{{noticeData.detail.exam_tool}}" bindtap="tapCustomerService" data-item="{{item}}">
                  <image src="{{item.icon}}"></image>
                  {{item.title}}
                </view>
              </view>
            </view>
            <!-- 笔试备考 -->
            <view class="tool-box" wx:if="{{noticeData.detail.written_exam_prepare.length>0 || noticeData.detail.bottom_ad.length>0 }}">
              <view class="item-title">笔试备考</view>
              <view class="written-list">
                <view class="written-list-item" wx:for="{{noticeData.detail.written_exam_prepare}}" bindtap="tapCustomerService" data-item="{{item}}">
                  <view class="pr-box">
                    <text class="title">{{item.title}}</text>
                    <view class="label">{{item.desc}}</view>
                  </view>
                  <image class="img" src="{{item.icon}}"></image>
                </view>
              </view>
              <view class="bd-box" wx:if="{{noticeData.detail.bottom_ad[0].img}}">
                <image class="bd-img" bindtap="tapCustomerService" data-item="{{noticeData.detail.bottom_ad[0]}}" mode="widthFix" src="{{noticeData.detail.bottom_ad[0].img}}"></image>
              </view>
            </view>

            <!-- 声明 -->
            <view class="statement-box">
              声明：本站发布的招考资讯均来源于招录官方网站，由金标尺整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。
            </view>
          </block>
          <!-- 职位列表 -->
          <block wx:if="{{indexToTabKey[activeIndex]=='position'}}">
            <view wx:if="{{menuList.length || noticeData.detail.child_article_list.length }}" class="main-top select-box-content {{selectBoxSticky ? 'sticky-border' : ''}}" style="position: sticky; top: {{selectBoxTop}}px; z-index: 100; background-color: #fff;">
              <view wx:if="{{noticeData.detail.child_article_list.length>0}}" class="select-box {{showSelectBoxPopup?'active':''}}" bindtap="openSelectBoxPopup">
                <text class="title text-ellipsis-1">{{jobDetailSelectForTemplate.article_list[0].title ||noticeData.detail.title}}</text>
                <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_down.png"></image>
              </view>
              <view class="menu-box" style="padding-right: 85rpx;">
                <business-menu id="announcement-business-menu" isLogin="{{isLogin}}" menu-list="{{menuList}}" selectData="{{jobDetailSelectForTemplate}}" active-expanded="{{activeExpanded}}" tab-type="detail" showPopupFilterMenu="{{showPopupFilterMenu}}" bind:menuClick="handleMenuClick" />
                <view class="right-btn" bindtap="goSelect" wx:if="{{hasPageIcon}}">
                  <image class="img" wx:if="{{!filterUtils.isMenuItemSelected('filter_list', jobDetailSelectForTemplate)}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_filter.png"></image>
                  <image class="img" wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_filter_red.png"></image>
                </view>
              </view>
            </view>
            <block wx:if="{{jobList.length>0}}">
              <view class="p32">
                <view class="notes-box" wx:if="{{isShowResume}}" bindtap="goResume">
                  <image class="notes-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_note_bg.png"></image>
                  <van-circle style="height: 40px;" value="{{ noticeData.detail.complete_progress.progress }}" type="2d" layer-color="rgba(255, 106, 77, 0.15)" color="rgba(255, 106, 77, 1)" size="40">
                    <view class="circle-text"><text class="num">{{noticeData.detail.complete_progress.progress}}</text>%</view>
                  </van-circle>
                  <view class="content-text">
                    <view class="title">当前你的岗位匹配度较低</view>
                    <view class="label">完善你的简历以提升岗位匹配度</view>
                  </view>
                  <view class="improve-btn">去完善</view>
                  <view class="close-img-box">
                    <image class="close-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_close_hui.png"></image>
                  </view>
                </view>
                <position-card list="{{jobList}}"></position-card>
              </view>
            </block>
            <empty-default imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" wx:if="{{jobList.length === 0}}" text="本场考试暂无符合你筛选要求的职位~"></empty-default>
          </block>
          <!-- 官方动态 -->
          <block wx:if="{{indexToTabKey[activeIndex]=='official'}}">
            <view class="official-list" wx:if="{{officialList.length>0}}">
              <view class="official-list-item" catch:tap="toNewsDetail" wx:for="{{officialList}}" wx:key="id">
                <!-- <view class="new-label" wx:if="{{item.isNew}}">最新动态</view> -->
                <view class="time" wx:if="{{item.release_time_1}}">
                  <view class="day"><text class="num">{{item.release_time_1.day}}</text>日</view>
                  <view class="line"></view>
                  <view class="month"><text class="num">{{item.release_time_1.month}}</text>月</view>
                </view>
                <view class="title text-ellipsis-2">{{item.title}}</view>
                <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"></image>
              </view>
            </view>
          </block>
        </view>
        <view class="action-bar-box" wx:if="{{tabList.length>1}}">
          <view class="action-bar container flex-justify_between">
            <!-- 左侧按钮组 - 所有Tab共用 -->
            <view class="flex-c">
              <view class="bottom-item" bindtap="tapCustomerService" data-item="{{noticeData.detail.join_group}}" wx:if="{{noticeData.detail.join_group}}">
                <image src="{{noticeData.detail.join_group.icon}}"></image>
                {{noticeData.detail.join_group.btn_title}}
              </view>
              <view class="bottom-item" bindtap="openExam" wx:if="{{ tabList.length>1&&noticeData.type=='article_list' && noticeData.detail.article_num>0}}">
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_exam_list.png"></image>
                考试列表
                <view class="new-box" wx:if="{{noticeData.detail.article_num>0}}">{{noticeData.detail.article_num}}</view>
              </view>
            </view>

            <!-- 右侧按钮 - 根据当前Tab显示不同内容 -->
            <view wx:if="{{indexToTabKey[activeIndex]=='detail' && tabList.length>1}}" class="filter-button {{!noticeData.detail.join_group&&noticeData.type!='article_list'?'wp100':''}}" bindtap="changeActiveIndex" data-item="position">查看职位</view>

            <view wx:elif="{{indexToTabKey[activeIndex]=='position'}}" class="filter-button {{!noticeData.detail.join_group?'wp100':''}}" bindtap="changeActiveIndex" data-item="detail">查看公告</view>

            <view wx:elif="{{indexToTabKey[activeIndex]=='official'}}" class="filter-button {{!noticeData.detail.join_group&&noticeData.type!='article_list'?'wp100':''}}" bindtap="changeActiveIndex" data-item="detail">查看公告</view>
          </view>
        </view>
        <view class="entrance-box" wx:if="{{noticeData.detail.float_window_ad}}" catchtap="tapCustomerService" data-item="{{noticeData.detail.float_window_ad}}">
          <image mode="widthFix" src="{{noticeData.detail.float_window_ad.img}}"></image>
        </view>
        <van-popup show="{{ show }}" custom-style="height: 80vh" round z-index="999" position="bottom" bind:close="onClose">
          <view class="popu-box">
            <view class="popu-box-top">
              <view class="title">考试汇总：已收录<text class="num">{{examData.total_num}}</text>场</view>
              <image class="close" bindtap="onClose" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_close_hui.png"></image>
              <view class="popu-menu">
                <business-menu id="announcement-exam-menu" isLogin="{{isLogin}}" menu-list="{{examList}}" selectData="{{examPopuSelectForTemplate}}" active-expanded="{{activeExamExpanded}}" activeApplyExpanded="{{activeApplyExpanded}}" examPopu tab-type="details" showPopupFilterMenu="{{show}}" bind:menuClick="handleExamMenuClick" />
                <apply-list wx:if="{{showApplyStatus}}" show="{{show}}" list="{{examMenuData.apply_status.data}}" isMultipleChoice="{{true}}" selected="{{examPopuSelectForTemplate.apply_status}}" filterKey="apply_status" bind:confirm="handlePopuMenuFilterConfirm" />
              </view>
            </view>
            <view class="popu-box-content">
              <scroll-view class="position-card-list" scroll-y show-scrollbar="{{false}}" enhanced>
                <view class="p-32">
                  <view>
                    <view class="position-card" bindtap="goPosition" data-item="{{examData.article_list_data}}">
                      <view class="title">{{examData.article_list_data.title}}</view>
                      <view class="time" wx:if="{{examData.article_list_data.apply_start_time &&examData.article_list_data.apply_end_time}}">{{examData.article_list_data.apply_start_time}} 到 {{examData.article_list_data.apply_end_time}}</view>
                      <view class="bottom">
                        <view class="text-word">招录汇总：共招<text class="num">{{examData.article_list_data.need_num}}</text>人<text class="num ml16">{{examData.article_list_data.job_num}}</text>个职位</view>
                        <view class="all-btn">全部职位</view>
                      </view>
                    </view>
                  </view>
                  <view class="title-line">
                    <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_grid.png"></image>
                    <text class="text">以下为各地市或招聘单位发布公告</text>
                    <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_grid.png"></image>
                  </view>
                  <view class="card-list">
                    <view class="position-card" wx:for="{{examData.child_list}}" wx:key="index" bindtap="goPosition" data-item="{{item}}">
                      <view class="title">{{item.title}}</view>
                      <view class="time" wx:if="{{item.apply_start_time&&item.apply_end_time}}">{{item.apply_start_time}} 到 {{item.apply_end_time}}</view>
                      <view class="bottom">
                        <view class="flex-v">
                          <text class="status" style="color: {{item.apply_status.color}};">{{item.apply_status.text}}</text>
                          <view class="line">｜</view>
                          <view class="item-text mr16">共招<text class="num">{{item.need_num}}</text>人</view>
                          <view class="item-text"><text class="num">{{item.job_num}}</text>个职位</view>
                        </view>
                        <view class="all-btn">全部职位</view>
                      </view>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
        </van-popup>

        <van-popup show="{{ popuShow }}" round z-index="999" position="bottom" bind:close="onPopuClose">
          <van-picker value-key="title" toolbar-class="top-area" column-class="column-area" active-class="active-item" show-toolbar columns="{{ noticeData.detail.parent_article_lists }}" bind:cancel="onPopuClose" bind:confirm="onConfirm" />
        </van-popup>

        <!-- van-popup内的地区选择弹窗 -->
        <popup-menu-filter show="{{showExamRegionPopup}}" popup-top="{{examRegionPopupTop}}" bind:close="closeExamRegionPopup" custom-class="exam-region-popup-filter">
          <menu-filter-content menu-type="{{activeExamExpanded}}" animation-show="{{showExamRegionPopup}}" bind:close="closeExamRegionPopup">
            <!-- 地区选择内容 -->
            <view class="popup-body">
              <region-select-box noPadding show="{{showExamRegionPopup}}" filterKey="apply_region" isShowBg="{{false}}" popupHeight="40vh" selectedRegions="{{examPopuSelectForTemplate.apply_region}}" inPopup="{{true}}" bind:confirmSelection="handlePopuRegionSelection" />
            </view>
          </menu-filter-content>
        </popup-menu-filter>

        <!-- 菜单筛选弹窗 - 使用 Slot 方式 -->
        <popup-menu-filter show="{{showPopupFilterMenu}}" popup-top="{{overlayTop}}" bind:close="handlePopupClose" custom-class="global-positioned">
          <!-- 菜单筛选内容作为 slot 传入 -->
          <menu-filter-content menu-type="{{activeExpanded}}" show-footer=" {{showFooter}}" animation-show="{{showPopupFilterMenu}}" bind:autoClose="handlePopupClose" bind:close="handlePopupClose">
            <!-- 考试 -->
            <view class="popu-content" wx:if="{{activeExpanded == 'exam_type' }}">
              <scroll-view scroll-y show-scrollbar="{{false}}" enhanced>
                <exam-content show="{{showPopupFilterMenu}}" exam-list="{{detailMenuData.exam_type.data}}" isMultipleChoice="{{true}}" selected="{{jobDetailSelectForTemplate.exam_type}}" filterKey="exam_type" bind:confirm="handleJobMenuFilterConfirm" />
              </scroll-view>
            </view>
            <!--学历 -->
            <view class="popu-content" wx:if="{{activeExpanded == 'tmp_education' }}">
              <scroll-view scroll-y show-scrollbar="{{false}}" enhanced>
                <exam-content show="{{showPopupFilterMenu}}" exam-list="{{detailMenuData.tmp_education.data}}" isMultipleChoice="{{true}}" selected="{{jobDetailSelectForTemplate.tmp_education}}" filterKey="tmp_education" bind:confirm="handleJobMenuFilterConfirm" />
              </scroll-view>
            </view>
            <!-- 排序 -->
            <view class="popu-content" wx:if="{{activeExpanded == 'sort_list' }}">
              <scroll-view scroll-y class="popu-content-c" show-scrollbar="{{false}}" enhanced>
                <sort-list show="{{showPopupFilterMenu}}" sortList="{{detailMenuData.sort_list.data}}" isMultipleChoice="{{false}}" selected="{{jobDetailSelectForTemplate.sort_list}}" filterKey="sort_list" bind:confirm="handleJobMenuFilterConfirm"></sort-list>
              </scroll-view>
            </view>
            <!-- 地区选择 -->
            <view class="popu-content" wx:if="{{activeExpanded == 'apply_region'}}">
              <region-select-box noPadding show="{{showPopupFilterMenu}}" filterKey="apply_region" isShowBg="{{false}}" popupHeight="40vh" selectedRegions="{{jobDetailSelectForTemplate.apply_region}}" inPopup="{{true}}" bind:confirmSelection="handleRegionConfirmSelection" />
            </view>
            <!-- 试卷类别 -->
            <view class="popu-content" wx:if="{{activeExpanded == 'paper_type' }}">
              <scroll-view scroll-y show-scrollbar="{{false}}" enhanced>
                <category-list show="{{showPopupFilterMenu}}" list="{{detailMenuData.paper_type.data}}" isMultipleChoice="{{true}}" selected="{{jobDetailSelectForTemplate.paper_type}}" filterKey="paper_type" bind:confirm="handleJobMenuFilterConfirm" />
              </scroll-view>
            </view>
          </menu-filter-content>
        </popup-menu-filter>

        <!-- 选择公告弹窗 -->
        <popup-menu-filter show="{{showSelectBoxPopup}}" popup-top="{{selectBoxPopupTop}}" bind:close="closeSelectBoxPopup" custom-class="select-box-popup-filter">
          <menu-filter-content show-footer="{{false}}" animation-show="{{showSelectBoxPopup}}" bind:close="closeSelectBoxPopup">
            <!-- 自定义内容 -->
            <view class="popup-body">
              <scroll-view scroll-y class="select-list" show-scrollbar="{{false}}" enhanced>
                <notice-list show="{{showSelectBoxPopup}}" list="{{noticeData.detail.child_article_list}}" isMultipleChoice="{{false}}" selected="{{jobDetailSelectForTemplate.article_list}}" filterKey="article_list" bind:confirm="handleJobMenuFilterConfirm"></notice-list>
              </scroll-view>
            </view>
          </menu-filter-content>
        </popup-menu-filter>
        <major-selector-pro show="{{majorShow}}" selectedMajorIds="{{jobDetailSelectForTemplate.tmp_major.selectedMajorIds}}" educationId="{{jobDetailSelectForTemplate.tmp_major.educationId || educationId}}" bind:confirm="majorConfirm" bind:close="majorClose"></major-selector-pro>
      </view>
    </block>
  </block>
</block>
<loading-spinner wx:else />
<!-- 公众号关注弹窗 -->
<van-overlay show="{{ followShow }}" z-index="999" bind:click="handleFollowPopupCancel">
  <view class="wrapper">
    <view class="follow-popu">
      <view class="top-titlte">
        <image mode="widthFix" class="imgs" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_yuan_g.png"></image>
        关注成功
      </view>
      <view class="label-text">开启微信通知，考试日程贴心提醒</view>
      <view class="open" bindtap="handleFollowPopupConfirm">去开启</view>
      <view class="talk" bindtap="handleFollowPopupCancel">以后再说</view>
    </view>
  </view>
</van-overlay>