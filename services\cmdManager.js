const ROUTER = require("@/services/mpRouter")
export function toCmdUnitKey(cmd, data) {
  try {
    cmd = JSON.parse(cmd)
  } catch (error) {}
  const pathList = {
    CourseDetail: "/package-goods/course/detail/index",
    Advanced: "/package-goods/advclass/advclass-detail/index",
    QuestionSetDetail: "/package-goods/questionset/detail/index",
    CampsInterviewDetail: "/pages/face/detail/index",
    ActivityCustomer: "/pages/webview/activityCustomer/index",
    PintuanDetail: "/package-goods/groupon/detail/index",
    Web: "/pages/webview/web/index",
    FaceCollection: "/pages/face/collection/index",
    QuestionsetCollection: "/package-goods/questionset/collection/index",
    CorrectionNewsTags: "/package-essay/article/article-list/index",
    CorrectionTopic: "/package-essay/home/<USER>/index",
    CorrectionProject: "/package-essay/course/detail/index",
    CorrectionProject: "/package-essay/course/detail/index",
    NoticejobCollection: "/pages/notice/collection/index",
    SelectedExams: "/pages/notice/exam/index",
    ExamBigData: "/pages/special/big-data/index",
    InterviewScore: "/pages/special/interviewScore/index",
    NoticejobTool: "/pages/special/majorSelect/index",
  }
  const path = pathList[cmd.unitKey]
  if (cmd.unitKey === "ActivityCustomer") {
    ROUTER.navigateTo({ path: "/" + cmd.param.path })
    return
  }

  if (cmd.unitKey === "MiniProgram") {
    const path = cmd.param.path
    const params = this.createShareParams({ query: {} })
    console.log(params, "123123123")
    const newPath = ROUTER.mergeUrlParams(path, params.query)

    wx.navigateToMiniProgram({
      path: newPath,
      appId: cmd.param.appid,
      envVersion: "release",
      success(res) {
        console.log("跳转小程序成功！", res)
      },
      fail(err) {
        // wx.showToast({
        //   title: err.errMsg,
        //   icon: "none",
        // })
      },
    })
    return
  }
  if (cmd.unitKey === "CorrectionNewsTags") {
    ROUTER.navigateTo({ path, query: { ...cmd.param, title: data.name } })
    return
  }
  console.log(path, cmd)
  if (path) {
    ROUTER.navigateTo({ path, query: cmd.param })
    return
  }
}
