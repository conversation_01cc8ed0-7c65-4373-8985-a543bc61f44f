/**
 * 辅助函数，用于从本地存储中获取习题单数据。
 * 处理错误，并确保总是返回一个对象，即使本地存储为空或读取失败。
 *
 * @returns {Object} 返回包含所有习题单的对象，可能为空。
 */
function getStoredEstimateexamSheets() {
  try {
    return wx.getStorageSync("estimateexamSheets") || {}
  } catch (e) {
    console.log("获取存储的习题单记录失败:", e)
    return {}
  }
}

/**
 * 返回当前存储在本地存储中的所有习题单的完整数据结构。
 * 这可以用于调试、数据分析或前端显示等目的。
 *
 * @returns {Object} 包含所有习题单的对象。
 */
export function getAllEstimateexamSheets() {
  return getStoredEstimateexamSheets()
}

/**
 * 根据习题单的ID从本地存储中检索特定的习题单。
 * 记录成功或失败的信息。
 *
 * @param {number} qvid - 要检索的习题单的ID。
 * @returns {Object|null} 如果找到则返回习题单数据，未找到则返回null。
 */
export function getEstimateexamSheet(qvid) {
  const estimateexamSheets = getStoredEstimateexamSheets()
  if (Object.prototype.hasOwnProperty.call(estimateexamSheets, qvid)) {
    // console.log('获取习题单记录成功:', estimateexamSheets[qvid]);
    return estimateexamSheets[qvid]
  } else {
    // console.log('未找到习题单记录');
    return null
  }
}

/**
 * 更新本地存储中的现有习题单数据。
 * 如果习题单存在，则进行更新；否则，不采取任何操作。
 * 记录操作结果。
 *
 * @param {number} qvid - 要更新的习题单的ID。
 * @param {Object} updatedData - 习题单的新数据。
 */
export function updateEstimateexamSheet(qvid, updatedData) {
  let estimateexamSheets = getStoredEstimateexamSheets()
  if (qvid) {
    estimateexamSheets[qvid] = updatedData
    try {
      wx.setStorageSync("estimateexamSheets", estimateexamSheets)
      // console.log('习题单记录更新成功');
    } catch (e) {
      // console.log('更新习题单记录失败:', e);
    }
  }
}

/**
 * 根据ID从本地存储中删除一个习题单。
 * 如果习题单存在，则将其删除；否则，不采取任何操作。
 * 记录操作结果。
 *
 * @param {number} qvid - 要删除的习题单的ID。
 */
export function deleteEstimateexamSheet(qvid) {
  let estimateexamSheets = getStoredEstimateexamSheets()
  if (Object.prototype.hasOwnProperty.call(estimateexamSheets, qvid)) {
    delete estimateexamSheets[qvid]
    try {
      wx.setStorageSync("estimateexamSheets", estimateexamSheets)
      console.log("习题单记录删除成功")
    } catch (e) {
      console.log("删除习题单记录失败:", e)
    }
  }
}
