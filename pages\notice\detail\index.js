const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const PopupMenuFilterMixin = require("@/components/popup/popup-menu-filter/mixin")
const QuestionParseHtml = require("@/utils/QuestionParseHtml")
const noticeMixin = require("../mixins/noticeMixin")
const {
  getOfficialNewsCache,
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
} = require("@/utils/cache/filterCache")
const ScrollService = require("@/services/scrollService")
const APP = getApp()
let PAGE_OPTIONS = {} // 页面进入时参数

const pageConfig = Object.assign({}, PopupMenuFilterMixin, noticeMixin, {
  data: {
    show_white: true,
    isCollect: false,
    // 筛选菜单相关数据
    headerHeight: 100,
    menuSticky: false,
    menuOffsetTop: 0,
    // 当前菜单类型
    // 弹窗相关属性
    showFooter: false,

    pageScrollDisabled: false, // 页面滚动禁用状态
    currentSelectBoxTitle: "",
    // van-popup内地区菜单项的选中状态
    noticeData: null,

    jobDetailSelectForTemplate: {},
    detailMenuData: {},
    menuList: [],
    isPageLoadComplete: false, // 页面是否加载完成
    isShowResume: false,

    examPopuSelectForTemplate: {},
    examMenuData: {},
    showApplyStatus: false,
    isLogin: false,
    pageType: "detail",
    redDot: false,
    tagStyle: {
      table:
        "border-top: 1px solid gray; border-left: 1px solid gray; border-collapse:collapse",
      th: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
      td: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
    },
  },
  async onLoad(options) {
    console.log("页面参数:", options)
    this.scrollService = ScrollService
    PAGE_OPTIONS = options
    // 将 PAGE_OPTIONS 设置为页面实例属性
    this.PAGE_OPTIONS = options
    this.initPopupMenuFilterMixin()
    this.initNoticeMixin()
    this.syncApplyStatusState()
    if (options.activeIndex) {
      this.setData({
        activeIndex: options.activeIndex,
      })
    }
    await this.getNoticeDetail()
    this.setMenuStickyTop()
    this.updateJobSelectForTemplateFromCache()
    // 检测内容高度，以防数据发生变化
    if (this.data.noticeData && this.data.noticeData.detail) {
      // 只有在内容未展开过时才检测高度，避免已展开状态下滚动到页面顶部
      if (!this.data.isExpanded) {
        this.checkContentHeight()
      } else {
        console.log("内容已展开，跳过高度检测")
      }
    }
  },
  async onShow() {
    console.log("详情页显示，检查筛选条件变化")

    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }

    // 保存当前展开状态和按钮显示状态，避免被重置
    const currentExpandedState = this.data.isExpanded
    const currentShowToggle = this.data.showToggle

    // 保存当前状态用于对比
    const currentState = this.data.jobDetailSelectForTemplate
    const cachedState = getJobDetailSelectForTemplateCache()

    console.log("状态对比数据:", {
      currentState,
      cachedState,
      currentExpandedState,
      currentShowToggle,
    })

    // 检查是否有缓存数据且状态发生变化
    const hasCachedData = Object.keys(cachedState).length > 0
    const hasStateChanged =
      hasCachedData && !UTIL.isObjectEqual(currentState, cachedState)

    console.log("状态变化检查结果:", {
      hasCachedData,
      hasStateChanged,
    })

    // 获取最新的公告详情数据
    await this.getNoticeDetail()
    this.setMenuStickyTop()

    // 从缓存恢复筛选条件
    this.updateJobSelectForTemplateFromCache()

    // 如果当前是职位列表Tab且状态发生变化，重新请求数据
    if (this.shouldRefreshJobList(hasStateChanged)) {
      console.log("检测到职位筛选条件变化，重新请求数据")
      this.applyFilter()
      this.scrollToTop()
    } else {
      console.log("职位筛选条件未变化或不在职位Tab，保持当前数据")
    }

    // 在所有操作完成后，最后恢复展开状态和按钮状态
    setTimeout(() => {
      if (currentExpandedState) {
        console.log("延迟恢复之前的展开状态和按钮状态")
        this.setData({
          isExpanded: true,
          showToggle: currentShowToggle, // 恢复原来的按钮显示状态
        })
      }
    }, 100)
  },

  async getNoticeDetail() {
    const res = await UTIL.request(API.getArticleDetail, {
      id: PAGE_OPTIONS?.id,
    })
    if (res && res.error && res.error.code === 0 && res.data) {
      console.log(res, "公告详情")
      let resData = res.data
      if (resData?.detail?.content?.body_content) {
        resData.detail.content.body_content = QuestionParseHtml.processRichTextContent(
          resData.detail.content.body_content
        )
      }

      // 判断官方动态是否有更新
      let hasOfficialUpdate = false
      const noticeId = resData.id
      const latestReleaseTime = resData.detail?.notice_latest_release_time

      if (latestReleaseTime && noticeId) {
        // 从缓存获取点击时间记录
        const officialClickRecord = getOfficialNewsCache() || {}
        const lastClickTime = officialClickRecord[noticeId]

        if (lastClickTime) {
          // 有缓存：比较缓存时间和最新发布时间
          hasOfficialUpdate = lastClickTime < latestReleaseTime
        } else {
          // 没有缓存：比较当前时间和最新发布时间
          const currentTime = Date.now()
          hasOfficialUpdate = currentTime > latestReleaseTime
        }
      }

      this.setData({
        isLogin: APP.getIsLogin(),
        isRequest: true,
        noticeData: resData,
        isPageLoadComplete: true,
        redDot: hasOfficialUpdate, // 设置红点状态
        isShowResume:
          resData.detail?.complete_progress?.is_tips === 1 &&
          !APP.globalData.hasResume,
      })
      // 初始化动态菜单
      if (resData?.detail?.filter_menu.length > 0) {
        this.initDynamicMenu(resData.detail.filter_menu)
      }
      // 根据动态数据生成Tab列表
      this.generateDynamicTabs()

      // 重新检测内容高度（因为数据已更新）
      // 只有在内容未展开过时才检测高度，避免已展开状态下滚动到页面顶部
      if (!this.data.isExpanded) {
        this.checkContentHeight()
      } else {
        console.log("内容已展开，跳过高度检测")
      }
    } else {
      this.setData({
        isRequest: true,
      })
    }
  },
  handleRegionConfirmSelection(e) {
    const { filterKey, tempSelected } = e.detail
    console.log(filterKey, tempSelected)
    this.setData({
      [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
    this.applyFilter()
    this.hidePopupMenuFilter()
  },

  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.data.pageScrollDisabled) {
      return
    }

    // 检查 select-box 是否处于吸顶状态
    // selectBoxTop 是 select-box 开始吸顶的临界点
    const isSticky =
      e.scrollTop >= this.data.selectBoxTop - this.data.headerHeight

    this.scrollService.setScrollPosition(this.data.activeIndex, e.scrollTop)
    // 只有在状态发生变化时才更新
    if (isSticky !== this.data.selectBoxSticky) {
      this.setData({
        selectBoxSticky: isSticky,
      })
    }
  },
  onReachBottom() {
    if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
      // 检查是否还有更多数据且当前不在加载中
      if (this.data.hasMore && !this.data.isLoading) {
        // 页码+1
        const nextPage = this.data.page + 1
        this.setData({
          page: nextPage,
        })

        // 加载下一页数据
        const apiParams = this.buildApiParams(
          this.data.jobDetailSelectForTemplate
        )
        this.getJobList(apiParams, true)
      }
    }
  },
  // 获取分享参数
  getPageShareParams() {
    const share_info = this.data?.noticeData?.share_info || {}
    let shareTitle = share_info.title
    let shareImageUrl = share_info.image
    console.log(PAGE_OPTIONS.id, "--=---------------------------")
    const params = {
      title: shareTitle,
      imageUrl: shareImageUrl,
      path: "/pages/notice/detail/index",
      query: {
        id: PAGE_OPTIONS.id,
      },
    }
    return params
  },
  onShareAppMessage() {
    return APP.createShareParams(this.getPageShareParams())
  },
  // 分享朋友圈
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    const result = APP.createShareParams(params)
    result.query = ROUTER.convertPathQuery(result.query)
    return result
  },
})

// 创建页面实例
Page(pageConfig)
