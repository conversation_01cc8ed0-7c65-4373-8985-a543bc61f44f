import { setCache, getCache } from "./core/core"

const CACHE_NAME = "setting"

// 设置用户信息缓存
export function setSettingCache(data) {
  return setCache(CACHE_NAME, data)
}

// 获取用户信息缓存
export function getSettingCache() {
  return getCache(CACHE_NAME) || {}
}

// 设置考试信息
export function setSettingExamCache(data) {
  return setCache(`${CACHE_NAME}.exam`, data)
}

// 获取考试信息
export function getSettingExamCache() {
  return getCache(`${CACHE_NAME}.exam`)
}

// 获取考试科目信息
export function getSettingSubjectCache() {
  return getCache(`${CACHE_NAME}.subject`)
}

// 设置考试科目信息
export function setSettingSubject(data) {
  return setCache(`${CACHE_NAME}.subject`, data)
}

// 获取练习类型信息
export function getSettingPracticeTypeCache() {
  return getCache(`${CACHE_NAME}.practiceType`)
}

// 设置练习类型信息（1背题-练习 2做题-测验）
export function setSettingPracticeType(data) {
  return setCache(`${CACHE_NAME}.practiceType`, parseInt(data))
}

// 获取练习数量信息
export function getSettingPracticeNumberCache() {
  return getCache(`${CACHE_NAME}.practiceNumber`)
}

// 设置练习数量信息
export function setSettingPracticeNumber(data) {
  return setCache(`${CACHE_NAME}.practiceNumber`, parseInt(data))
}

// 拍照弹窗只弹一次缓存
export function setUserPhotoPopupShown(val = true) {
  return setCache(`${CACHE_NAME}.userPhotoPopupShown`, !!val)
}

export function getUserPhotoPopupShown() {
  return getCache(`${CACHE_NAME}.userPhotoPopupShown`, false)
}
