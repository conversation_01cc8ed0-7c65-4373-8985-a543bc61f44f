.notice-list-item {
  background: #ffffff;
  padding: 32rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  position: relative;
  display: flex;
  align-items: center;

  .select-img {
    width: 40rpx;
    height: 40rpx;
    margin-right: 32rpx;
    position: relative;
    z-index: 1;
  }

  .bg-style {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16rpx;
    z-index: 0;
  }

  .content-area {
    flex: 1;
    position: relative;
    z-index: 1;
  }

  &.over {
    .title {
      color: rgba(145, 148, 153, 1);
    }
  }
  .title {
    font-size: 32rpx;
    color: rgba(34, 36, 46, 1);
    line-height: 48rpx;
    font-weight: 600;
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;

    .status {
      font-size: 24rpx;
      color: rgba(19, 191, 128, 0.8);
      &.coming {
        color: rgba(68, 138, 255, 0.8);
      }
      &.over {
        color: rgba(255, 106, 77, 0.8);
      }
      &.end {
        color: rgba(145, 148, 153, 1);
      }
    }
    .line {
      font-size: 20rpx;
      color: rgba(194, 197, 204, 1);
      margin: 0 8rpx;
    }
    .item-text {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      .num {
        color: rgba(60, 61, 66, 1);
      }
    }
    .mr16 {
      margin-right: 16rpx;
    }
    .flex-v {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
    }

    .right-info {
      display: flex;
      align-items: center;
      .time {
        font-size: 24rpx;
        color: rgba(194, 197, 204, 1);
      }
      .item-text {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 1);
        .num {
          color: rgba(60, 61, 66, 1);
        }
      }
    }
  }
}
