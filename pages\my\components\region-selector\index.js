const APP = getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    title: {
      type: String,
      value: "请选择",
    },
    type: {
      type: String,
      value: "region", // region, date, gender, politics, nation
    },
    value: {
      type: Array,
      value: [], // 用于回显的索引数组，如[0,1,2]表示第1个省第2个市第3个区
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动态列数据
    columns: [],
    // 默认索引数组
    defaultIndex: [],
    // 字段名配置 - 动态设置
    fieldNames: {
      text: "text",
      value: "value",
    },

    // 中国省市区数据
    regionData: [],
  },

  lifetimes: {
    attached() {
      if (!this.data.initialized) {
        this.initColumns()
        this.setData({ initialized: true })
      }
    },
  },

  observers: {
    type: function (newType) {
      if (this.data.initialized) {
        this.initColumns()
      }
    },
    value: function (newValue) {
      // 当传入新的回显值时，重新初始化
      if (this.data.type && newValue && this.data.initialized) {
        this.initColumns()
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化列数据
     */
    initColumns() {
      const type = this.data.type

      switch (type) {
        case "region":
          this.initRegionColumns()
          break
        case "date":
          this.initDateColumns()
          break
        case "gender":
          this.initSingleColumn(this.getGenderData(), "single")
          break
        case "politics":
          this.initSingleColumn(this.getPoliticsData(), "single")
          break
        case "nation":
          this.initSingleColumn(this.getNationData(), "single")
          break
        default:
          break
      }
    },

    /**
     * 将服务器返回的地区数据转换为组件需要的格式
     */
    convertRegionData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map((item) => ({
        text: item.area_name,
        value: item.area_code,
        // 保留原始数据字段，用于构造value格式
        area_parent_id: item.area_parent_id,
        id: item.id,
        children: item.child ? this.convertRegionData(item.child) : [],
      }))
    },

    /**
     * 初始化地区列数据
     */
    initRegionColumns() {
      // 获取回显的地区编码
      const defaultValues = this.data.value || []

      // 获取服务器地区数据，如果没有则使用默认数据
      const serverRegionData = APP.globalData?.serverConfig?.regionData
      let provinces = []

      if (serverRegionData && Array.isArray(serverRegionData)) {
        provinces = this.convertRegionData(serverRegionData)
      } else {
        provinces = this.data.regionData
      }

      let provinceIndex = 0
      let cityIndex = 0
      let districtIndex = 0

      if (defaultValues.length > 0) {
        // 根据回显的编码找到对应的索引
        provinceIndex = defaultValues[0]
        if (provinceIndex === -1) provinceIndex = 0
      }

      const selectedProvince = provinces[provinceIndex] || provinces[0]
      const cities = selectedProvince?.children || []

      if (defaultValues.length > 1) {
        cityIndex = defaultValues[1]
        if (cityIndex === -1) cityIndex = 0
      }

      const selectedCity = cities[cityIndex] || cities[0]
      const districts = selectedCity?.children || []

      if (defaultValues.length > 2) {
        districtIndex = defaultValues[2]
        if (districtIndex === -1) districtIndex = 0
      }

      const columns = [
        {
          values: provinces,
          className: "column1",
        },
        {
          values: cities,
          className: "column2",
        },
        {
          values: districts,
          className: "column3",
        },
      ]

      // 设置默认索引数组
      const defaultIndex = [provinceIndex, cityIndex, districtIndex]

      this.setData({
        columns: columns,
        defaultIndex: defaultValues,
        fieldNames: { text: "text", value: "value" },
      })

      // 延迟调用setPickerDefaultIndex，确保数据设置完成
      setTimeout(() => {
        this.setPickerDefaultIndex()
        // 通知父组件地区选择器已经准备好了
        this.triggerEvent("ready", { type: "region" })
      }, 10)
    },

    /**
     * 初始化日期列数据
     */
    initDateColumns() {
      // 获取回显值
      const defaultIndexes = this.data.value || []

      // 获取今天的日期
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = today.getMonth() + 1
      const currentDay = today.getDate()

      // 生成年份数据(1950-今年) - 从小到大排序，越靠后的年份在越下面
      const years = []
      for (let i = 1950; i <= currentYear; i++) {
        years.push({ text: `${i}年`, value: i })
      }

      // 根据回显值设置默认索引，如果没有回显值则默认今天
      let yearIndex, monthIndex, dayIndex
      let selectedYear, selectedMonth

      if (defaultIndexes.length > 0) {
        // 有回显值，使用回显值
        yearIndex = defaultIndexes[0]
        selectedYear = years[yearIndex]?.value || currentYear
      } else {
        // 没有回显值，默认今天
        yearIndex = currentYear - 1950 // 今年的索引位置
        selectedYear = currentYear
      }

      // 生成月份数据 - 显示全部12个月
      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push({ text: `${i}月`, value: i })
      }

      // 设置月份索引
      if (defaultIndexes.length > 0) {
        monthIndex = defaultIndexes[1]
        selectedMonth = months[monthIndex]?.value || currentMonth
      } else {
        monthIndex = currentMonth - 1 // 月份索引从0开始
        selectedMonth = currentMonth
      }

      // 生成日期数据 - 显示1-31日，不做未来日期限制
      const days = []
      for (let i = 1; i <= 31; i++) {
        days.push({ text: `${i}日`, value: i })
      }

      // 设置日期索引
      if (defaultIndexes.length > 0) {
        dayIndex = defaultIndexes[2]
      } else {
        dayIndex = currentDay - 1 // 日期索引从0开始
      }

      // 确保日期索引不超出范围
      if (dayIndex >= days.length) {
        dayIndex = days.length - 1
      }

      const columns = [
        {
          values: years,
          className: "year-column",
        },
        {
          values: months,
          className: "month-column",
        },
        {
          values: days,
          className: "day-column",
        },
      ]

      // 设置默认索引数组
      const defaultIndex = [yearIndex, monthIndex, dayIndex]

      this.setData({
        columns: columns,
        defaultIndex: defaultIndex,
        fieldNames: { text: "text", value: "value" },
      })

      // 延迟调用setPickerDefaultIndex，确保数据设置完成
      setTimeout(() => {
        this.setPickerDefaultIndex()
        // 通知父组件日期选择器已经准备好了
        this.triggerEvent("ready", { type: "date" })
      }, 10)
    },

    /**
     * 初始化单列数据
     */
    initSingleColumn(data, dataType) {
      // 获取回显值
      const defaultIndexes = this.data.value || []

      // 根据回显值设置默认索引
      const defaultIndex = defaultIndexes.length > 0 ? defaultIndexes[0] : 0

      // 数据格式转换：将{id, name}格式转换为{text, value}格式
      let convertedData = data
      if (dataType === "single") {
        convertedData = data.map((item) => ({
          text: item.name,
          value: item.id,
        }))
      }

      const columns = [
        {
          values: convertedData,
          className: "single-column",
        },
      ]

      // 对于转换后的数据，统一使用{text, value}字段配置
      const fieldNames = { text: "text", value: "value" }

      this.setData({
        columns: columns,
        defaultIndex: [defaultIndex],
        fieldNames: fieldNames,
      })

      // 延迟调用setPickerDefaultIndex，确保数据设置完成
      setTimeout(() => {
        this.setPickerDefaultIndex()
        // 通知父组件单选选择器已经准备好了
        this.triggerEvent("ready", { type: "single" })
      }, 10)
    },

    /**
     * 获取性别数据
     */
    getGenderData() {
      return APP.globalData?.serverConfig?.gender_list || []
    },

    /**
     * 获取政治面貌数据
     */
    getPoliticsData() {
      return APP.globalData?.serverConfig?.politics_face_list || []
    },

    /**
     * 获取民族数据
     */
    getNationData() {
      return APP.globalData?.serverConfig?.nation_list || []
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      this.triggerEvent("close")
    },

    /**
     * 确认选择
     */
    onConfirm(e) {
      const { value, index } = e.detail
      const type = this.data.type

      let result = { type, value, index }

      if (type === "region") {
        if (!value || value.length !== 3) {
          wx.showToast({
            title: "请选择完整地区",
            icon: "none",
          })
          return
        }

        result.text = `${value[0].text} ${value[1].text} ${value[2].text}`
        result.index = index // 返回索引数组，用于回显

        // 统一返回地区编码
        result.value = [value[0].value, value[1].value, value[2].value]
      } else if (type === "date") {
        if (!value || value.length !== 3) {
          wx.showToast({
            title: "请选择完整日期",
            icon: "none",
          })
          return
        }

        // 检查是否选择了未来日期
        const selectedYear = value[0].value
        const selectedMonth = value[1].value
        const selectedDay = value[2].value
        const selectedDate = new Date(
          selectedYear,
          selectedMonth - 1,
          selectedDay
        )
        const today = new Date()

        // 将今天的时间设置为23:59:59，这样今天的日期就是有效的
        today.setHours(23, 59, 59, 999)

        if (selectedDate > today) {
          wx.showToast({
            title: "不能选择未来日期",
            icon: "none",
          })
          return
        }

        result.text = `${value[0].value}年${value[1].value}月${value[2].value}日`
        result.date = `${value[0].value}-${String(value[1].value).padStart(
          2,
          "0"
        )}-${String(value[2].value).padStart(2, "0")}`
        result.index = index // 返回索引数组
      } else {
        // 单列选择
        if (!value || value.length === 0) {
          wx.showToast({
            title: "请选择选项",
            icon: "none",
          })
          return
        }

        // 对于经过格式转换的数据，value[0]已经是{text, value}格式
        result.text = value[0].text
        result.value = value[0].value
        result.index = index // 返回索引数组
      }

      this.triggerEvent("confirm", result)
      this.triggerEvent("close")
    },

    /**
     * 选择器变化
     */
    onChange(e) {
      const { picker, value, index } = e.detail
      const type = this.data.type

      if (type === "region") {
        // 地区三级联动逻辑
        if (index === 0) {
          // 省份变化，更新市和区
          const selectedProvince = value[0]
          const cities = selectedProvince?.children || []
          const districts = cities.length > 0 ? cities[0].children || [] : []
          picker.setColumnValues(1, cities)
          picker.setColumnValues(2, districts)
        } else if (index === 1) {
          // 城市变化，更新区
          const selectedCity = value[1]
          const districts = selectedCity?.children || []
          picker.setColumnValues(2, districts)
        }
      } else if (type === "date") {
        // 日期选择器不需要联动，年月日各自独立
        // 这里不做任何处理，让用户自由选择年月日
      }
    },

    /**
     * 设置picker的默认值
     */
    setPickerDefaultIndex() {
      const defaultIndex = this.data.defaultIndex
      if (!defaultIndex || defaultIndex.length === 0) return

      setTimeout(() => {
        const picker = this.selectComponent("#universalPicker")
        if (picker && picker.setIndexes) {
          picker.setIndexes(defaultIndex)
        } else {
          console.log("picker实例不可用")
        }
      }, 50)
    },
  },
})
