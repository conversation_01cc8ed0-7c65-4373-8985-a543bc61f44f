<!--pages/select/select-job/index.wxml-->
<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>

<view class="main-content">
  <common-header show_white="{{show_white}}" title="职位筛选">
    <view slot="left" class="lefts">
      <image class="left-arrow" wx:if="{{show_white}}" catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/arrow_left_black.png"></image>
      <image class="left-arrow" wx:else catchtap="backPage" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_left_white.png"></image>
    </view>
  </common-header>

  <view class="select-box">
    <!-- 已选择的筛选条件 -->
    <view class="select-top" wx:if="{{positionSelectList.length > 0}}">
      <scroll-view scroll-x class="select-list" show-scrollbar="{{false}}" enhanced>
        <view class="select-list-item" wx:for="{{positionSelectList}}" wx:key="value" data-index="{{index}}" catchtap="removeSelectedFilter">
          {{item.short_name || item.name}}
          <image class="close" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_close_red.png"></image>
        </view>
      </scroll-view>
      <view class="clear-box" catchtap="clearAllSelected">
        <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_delete.png"></image>
        清空
      </view>
    </view>

    <!-- 主体内容 -->
    <view class="main-container">
      <!-- 左侧分类列表 -->
      <view class="category-list">
        <block wx:for="{{filterCategories}}" wx:key="id" wx:for-item="group">
          <!-- 一级分组标题 -->
          <view class="group-title">{{group.name}}</view>

          <!-- 二级分类列表 -->
          <block wx:for="{{group.children}}" wx:key="id" wx:for-item="category">
            <view class="category-item {{currentCategory && currentCategory.id === category.id ? 'active' : ''}}" data-category="{{category}}" bindtap="selectCategory">
              <view class="category-content">
                <text class="category-name">{{category.name}}</text>
              </view>
            </view>
          </block>
        </block>
      </view>

      <!-- 右侧筛选选项列表 -->
      <view class="options-container">
        <scroll-view scroll-y class="options-list" show-scrollbar="{{false}}" enhanced scroll-into-view="{{scrollIntoView}}" bindscroll="onRightScroll">
          <!-- 所有筛选选项按类别显示 -->
          <block wx:for="{{filterCategories}}" wx:key="id" wx:for-item="group" wx:if="{{!loading.options}}">
            <block wx:for="{{group.children}}" wx:key="id" wx:for-item="category">
              <view class="category-section {{category.id=='exam_type'?'wp100':''}}" id="category-{{category.id}}" wx:if="{{category.list}}">
                <!-- 类别标题 -->
                <view class="category-title">
                  {{category.name}}
                  <text class="multi-label" wx:if="{{category.is_radio === 0}}">（多选）</text>
                </view>

                <!-- 该类别下的选项 -->
                <view class="options-group">
                  <view class="option-item {{filterUtils.isFilterItemOptionSelected(option.value, positionSelectForTemplate[category.filter_key]) ? 'selected' : ''}}" wx:for="{{category.list}}" wx:for-item="option" wx:key="value" data-value="{{option.value}}" data-key="{{category.filter_key}}" bindtap="handleOptionClick">
                    <text class="option-text">{{option.name}}</text>
                    <image class="recommend" wx:if="{{option.is_recommend === 1}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_recommend.png"></image>
                  </view>
                </view>
              </view>
            </block>
          </block>
        </scroll-view>
      </view>
    </view>

    <!-- 底部确认按钮 -->
    <view class="action-bar-box">
      <block wx:if="{{type == 'detail'}}">
        <view class="action-bar container flex-justify_between">
          <view class="confirm-btn" catchtap="confirmFilters" wx:if="{{num>0}}">筛选条件下共{{num}}个岗位</view>
          <view class="confirm-btn" catchtap="confirmFilters" wx:else>未筛选出符合条件的岗位</view>
        </view>
      </block>
      <block wx:else>
        <view class="action-bar container flex-justify_between">
          <view class="confirm-btn" catchtap="confirmFilters">完成</view>
        </view>
      </block>
    </view>
  </view>
</view>

<qna-dialog bindcancel="confirmLeave" bindconfirm="saveAndLeave" show="{{showCacelGrouponDialog}}" title="你对当前页面修改的内容尚未保存，是否退出" cancel="直接退出" confirm="保存并退出"></qna-dialog>