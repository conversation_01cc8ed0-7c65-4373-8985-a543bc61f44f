<view class="job-card" bindtap="onCardClick">
  <view class="top">
    <view class="name">{{jobData.name}}</view>
    <view class="right-btn {{ jobData.is_follows == 1?'':'red'}}" catchtap="onFocusClick">
      <image wx:if="{{jobData.is_follows == 1}}" class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/focused.png" mode="" />
      <image wx:else class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png" mode="" />
      <view class="text">{{ jobData.is_follows == 1?'已关注':'关注'}}</view>
    </view>
  </view>
  <view class="data-area">
    <view class="list-item" wx:if="{{jobData.work_unit}}">
      <view class="title">招录地区：</view>
      <view class="content">{{jobData.work_unit}}</view>
    </view>
    <view class="list-item" wx:if="{{jobData.need_num && jobData.need_num > 0}}">
      <view class="title">招录人数：</view>
      <view class="content"><text class="bold">{{jobData.need_num}}</text>人</view>
    </view>
    <view class="list-item" wx:if="{{jobData.apply_num && jobData.apply_num > 0}}">
      <view class="title">报考人数：</view>
      <view class="content"><text class="bold">{{jobData.apply_num}}</text>人</view>
    </view>
    <view class="list-item" wx:if="{{jobData.approved_num && jobData.approved_num > 0}}">
      <view class="title">过审人数：</view>
      <view class="content"><text class="bold">{{jobData.approved_num}}</text>人</view>
    </view>
    <view class="list-item" wx:if="{{jobData.pay_num && jobData.pay_num > 0}}">
      <view class="title">缴费人数：</view>
      <view class="content"><text class="bold">{{jobData.pay_num}}</text>人</view>
    </view>
    <view class="list-item mb0" wx:if="{{jobData.competitive_rate}}">
      <view class="title">竞争比例：</view>
      <view class="red"><text class="bold">{{jobData.competitive_rate || '-'}}</text></view>
    </view>
  </view>
</view>