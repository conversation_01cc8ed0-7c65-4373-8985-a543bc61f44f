const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")

Page({
  data: {
    show_white: true,
    bgColor: "#fff",
    tipShow: false,
    value: "",
    menuList: ["公告", "职位"],
    activeIndex: 0,

    // 搜索状态
    hasSearched: false,
    isLoading: false,
    searchResults: [],
    noticeTotal: 0,
    jobTotal: 0,
    hasAnyResults: false,

    // 保存所有搜索结果
    allSearchResults: null,

    // 历史搜索记录
    historyList: [],

    // 热门搜索数据
    hotSearchList: [
      {
        title: "四川省宜宾市叙州区教育类事业单位…",
        keyword: "宜宾教育",
        isNew: true,
      },
      {
        title: "四川省宜宾市叙州区教育类事业单位…",
        keyword: "教育事业",
        isNew: true,
      },
      {
        title: "郑州市公安局公开招聘1060名",
        keyword: "郑州公安",
        isNew: false,
      },
      {
        title: "2025年宜昌市教育系统事业单",
        keyword: "宜昌教育",
        isNew: false,
      },
      {
        title: "2025年夏季如皋市部分事业单",
        keyword: "如皋事业",
        isNew: false,
      },
      {
        title: "陕西省各市中央特岗计划教师",
        keyword: "陕西特岗",
        isNew: false,
      },
      {
        title: "徽商银行2025年社会招聘公告",
        keyword: "徽商银行",
        isNew: false,
      },
      {
        title: "石家庄市市属国有企业2025年",
        keyword: "石家庄国企",
        isNew: false,
      },
      {
        title: "江苏省地质局所属事业单位2025年",
        keyword: "江苏地质",
        isNew: false,
      },
      {
        title: "广州市荔湾区社区专职工作人",
        keyword: "广州社区",
        isNew: false,
      },
    ],
    examSshow: true,
    placeholder: "搜索公告/职位关键词",
  },

  onLoad(options) {
    // 获取搜索关键词
    if (options.keyword) {
      this.setData({
        value: options.keyword,
      })
      this.performSearch()
    }
    if (options.placeholder) {
      this.setData({
        placeholder: options.placeholder,
      })
    }
    if (options.type) {
      this.setData({
        examSshow: false,
      })
    }

    // 加载历史搜索记录
    this.loadSearchHistory()
  },

  onShow() {},
  toJobDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  goMore() {
    ROUTER.navigateTo({
      path: "/pages/notice/exam/index",
    })
  },

  /**
   * 输入框输入事件
   */
  bindKeyInput(e) {
    this.setData({
      value: e.detail.value,
    })
  },

  /**
   * 清空输入框
   */
  clearInput() {
    this.setData({
      value: "",
      hasSearched: false,
      searchResults: [],
      bgColor: "#fff",
      hasAnyResults: false,
    })
  },

  /**
   * 切换搜索类型（公告/职位）
   */
  changeIndex(e) {
    const index = e.currentTarget.dataset.index

    this.setData({
      activeIndex: index,
    })

    // 如果已经搜索过，直接更新显示的搜索结果
    if (this.data.hasSearched && this.data.allSearchResults) {
      this.updateCurrentTabResults()
    }
  },

  /**
   * 更新当前tab的搜索结果显示
   */
  updateCurrentTabResults() {
    const { activeIndex, allSearchResults } = this.data

    if (!allSearchResults) {
      this.setData({
        searchResults: [],
        noticeTotal: 0,
        jobTotal: 0,
        bgColor: "#fff",
        hasAnyResults: false,
      })
      return
    }

    // 根据当前tab获取对应的搜索结果
    const currentResults =
      activeIndex === 0
        ? allSearchResults.notices.list || []
        : allSearchResults.jobs.list || []

    // 判断是否公告和职位都有数据
    const noticeHasData =
      allSearchResults.notices.list && allSearchResults.notices.list.length > 0
    const jobHasData =
      allSearchResults.jobs.list && allSearchResults.jobs.list.length > 0
    const hasAnyResults = noticeHasData && jobHasData // 只有当两个都有数据时才显示tab

    // 更新显示数据
    this.setData({
      searchResults: currentResults,
      noticeTotal: allSearchResults.notices.total || 0,
      jobTotal: allSearchResults.jobs.total || 0,
      hasAnyResults: hasAnyResults,
      // 根据搜索结果是否为空设置背景色
      bgColor: currentResults.length > 0 ? "rgba(245, 246, 247, 1)" : "#fff",
    })
  },

  /**
   * 执行搜索
   */
  performSearch() {
    const keyword = this.data.value.trim()
    if (!keyword) {
      wx.showToast({
        title: "请输入搜索关键词",
        icon: "none",
      })
      return
    }

    // 保存搜索记录
    this.saveSearchHistory(keyword)

    // 设置加载状态
    this.setData({
      isLoading: true,
      hasSearched: true,
    })

    // 模拟API请求 - 实际项目中替换为真实API
    this.mockSearchAPI(keyword)
  },

  /**
   * 模拟搜索API请求
   * 实际项目中应该替换为真实的API调用
   */
  mockSearchAPI(keyword) {
    // 模拟网络延迟
    setTimeout(() => {
      // 同时生成公告和职位的搜索结果
      const noticeResults = this.generateMockResults(keyword, 0)
      const jobResults = this.generateMockResults(keyword, 1)

      const allResults = {
        notices: noticeResults,
        jobs: jobResults,
      }

      // 保存所有结果
      this.setData({
        allSearchResults: allResults,
      })

      // 更新当前tab的显示
      this.updateCurrentTabResults()

      this.setData({
        isLoading: false,
      })
    }, 800)
  },

  /**
   * 生成模拟搜索结果
   */
  generateMockResults(keyword, type) {
    const hasResults = Math.random() > 0.2 // 80%概率有结果

    if (!hasResults) {
      return { list: [], total: 0 }
    }

    const total = Math.floor(Math.random() * 500) + 10
    const mockList = []

    // 生成10条模拟数据
    for (let i = 0; i < 10; i++) {
      if (type === 0) {
        // 公告数据
        mockList.push({
          id: i + 1,
          title: `${keyword}相关公告 - 招聘简章${i + 1}`,
          status:
            i % 4 === 0
              ? "正在报名"
              : i % 4 === 1
              ? "即将报名"
              : i % 4 === 2
              ? "即将截止"
              : "报名结束",
          recruitNum: Math.floor(Math.random() * 100) + 1,
          positionNum: Math.floor(Math.random() * 20) + 1,
          suitableNum: Math.floor(Math.random() * 10) + 1,
          date: "2025.06.02",
        })
      } else {
        // 职位数据
        mockList.push({
          id: i + 1,
          title: `${keyword}相关职位 - 工程管理岗${i + 1}`,
          recruitNum: Math.floor(Math.random() * 10) + 1,
          unit: `重庆市${keyword}相关单位${i + 1}`,
          type: i % 2 === 0 ? "announcement" : "position",
        })
      }
    }

    return { list: mockList, total }
  },

  /**
   * 实际的搜索API调用 - 可根据项目需要取消注释并修改
   */
  // async callSearchAPI(keyword, type) {
  //   try {
  //     const url = type === 0
  //       ? `${API_BASE_URL}api/search/notices`
  //       : `${API_BASE_URL}api/search/jobs`
  //
  //     const response = await request(url, {
  //       keyword,
  //       page: 1,
  //       pageSize: 20
  //     }, 'GET')
  //
  //     if (response.code === 200) {
  //       return {
  //         list: response.data.list || [],
  //         total: response.data.total || 0
  //       }
  //     } else {
  //       throw new Error(response.message || '搜索失败')
  //     }
  //   } catch (error) {
  //     console.error('搜索API调用失败:', error)
  //     wx.showToast({
  //       title: '搜索失败，请重试',
  //       icon: 'none'
  //     })
  //     return { list: [], total: 0 }
  //   }
  // },

  /**
   * 选择历史搜索
   */
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      value: keyword,
    })
    this.performSearch()
  },

  /**
   * 选择热门搜索
   */
  selectHotSearch(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      value: keyword,
    })
    this.performSearch()
  },

  /**
   * 加载搜索历史记录
   */
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync("searchHistory") || []
      this.setData({
        historyList: history.slice(0, 8), // 最多显示8条历史记录
      })
    } catch (error) {
      console.error("加载搜索历史失败:", error)
    }
  },

  /**
   * 保存搜索历史记录
   */
  saveSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync("searchHistory") || []

      // 移除重复项
      history = history.filter((item) => item !== keyword)

      // 添加到开头
      history.unshift(keyword)

      // 限制最多保存20条
      history = history.slice(0, 20)

      wx.setStorageSync("searchHistory", history)

      // 更新显示的历史记录
      this.setData({
        historyList: history.slice(0, 8),
      })
    } catch (error) {
      console.error("保存搜索历史失败:", error)
    }
  },

  /**
   * 删除搜索历史
   */
  deleteHistory() {
    this.setData({
      tipShow: true,
    })
  },

  /**
   * 确认删除搜索历史
   */
  confirm() {
    try {
      wx.removeStorageSync("searchHistory")
      this.setData({
        tipShow: false,
        historyList: [],
      })
      wx.showToast({
        title: "已清空搜索记录",
        icon: "success",
      })
    } catch (error) {
      console.error("清空搜索历史失败:", error)
      wx.showToast({
        title: "清空失败，请重试",
        icon: "none",
      })
    }
  },

  /**
   * 取消删除
   */
  cancel() {
    this.setData({
      tipShow: false,
    })
  },

  /**
   * 返回上一页
   */
  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      return
    }
    wx.navigateBack({
      delta: 1,
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    if (this.data.hasSearched && this.data.value.trim()) {
      this.performSearch()
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    // TODO: 实现分页加载更多数据
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: "职位搜索",
      path: `/pages/search/search-all/index`,
    }
  },
})
