Component({
  properties: {
    border: {
      type: Boolean,
      value: true,
    },
    zIndex: {
      type: Number,
      value: 100,
    },
    topStyle: {
      type: String,
      value: "border", // 可选值: 'border', 'shadow', 'none'
    },
  },
  data: {
    height: 0,
  },
  lifetimes: {
    ready() {
      this.setHeight()
    },
  },
  methods: {
    setHeight: function () {
      var _this = this
      wx.nextTick(function () {
        let query = wx.createSelectorQuery().in(_this)
        query.select(".vans-tabbar").boundingClientRect()
        query.exec(function (res) {
          _this.setData({ height: res[0].height })
        })
      })
    },
  },
})
