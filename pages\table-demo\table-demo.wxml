<view class="table-scroll-view-container">
  <!-- 外层竖向滚动容器 -->
  <scroll-view class="vertical-scroll-container" scroll-x="{{true}}" scroll-y="{{true}}">

    <view class="box" style="position: sticky;left: 0;top:0;height:264rpx ;z-index: 1000;background: #fff;width: max-content ">
      <view class="fixed-columns-area">
        <!-- 遍历所有表格的固定列 -->
        <view class="filter-fixed-section" style="width: {{firstColumnWidth}}rpx">隐藏相同项</view>
      </view>
      <view class="horizontal-scroll-area">
        <!-- 筛选固定行 -->
        <view class="filter-scroll-section">
          <view class="data-row">
            <view wx:for="{{tableList[0].rows[0]}}" wx:key="index" class="data-cell" style="width: {{columnWidth}}rpx; min-height: {{rowHeight}}rpx;">
              {{item}}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="box" style="width: max-content">
      <!-- 左侧固定首列区域 -->
      <view class="fixed-columns-area">
        <!-- 遍历所有表格的固定列 -->
        <view wx:for="{{tableList}}" wx:key="index" class="table-fixed-section" style="width: {{firstColumnWidth}}rpx;top: 260rpx;">
          <!-- 吸顶区域 -->
          <view class="sticky-header header-placeholder">
            <view class="sticky-content">考试信息</view>
          </view>
          <!-- 固定首列数据 -->
          <view wx:for="{{item.rows}}" wx:for-index="rowIndex" wx:key="rowIndex" class="first-column-cell" style="height: {{firstColumnHeights[index] && firstColumnHeights[index][rowIndex] ? firstColumnHeights[index][rowIndex] + 'px' : 'auto'}};">
            {{item[0]}}
          </view>
        </view>

        <view class="table-fixed-section" style="width: {{firstColumnWidth}}rpx;top: 260rpx;">
          <!-- 吸顶区域 -->
          <view class="sticky-header header-placeholder">
            <view class="sticky-content" style="height:  auto;background: #fff;z-index: 100;">
              <view style="background: #fff;">
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
                <view>阿斯顿发了撒地方阿斯顿发大萨达发</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧横向滚动区域 -->
      <view class="horizontal-scroll-area">
        <view class="horizontal-scroll-content">
          <block wx:for="{{tableList}}" wx:for-index="tableIndex" wx:key="id">
            <view class="header-placeholder"></view>
            <!-- 遍历所有表格的可滚动部分 -->
            <view class="table-scroll-section">
              <view wx:for="{{item.rows}}" wx:for-index="rowIndex" wx:for-item="row" wx:key="rowIndex" class="data-row" style="min-height: {{rowHeight}}rpx;">
                <view wx:for="{{row}}" wx:for-item="cell" wx:key="index" class="data-cell {{index===0?'first-cell':''}}" style="width: {{columnWidth}}rpx; min-height: {{rowHeight}}rpx;">
                  {{cell}}
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
  </scroll-view>


</view>