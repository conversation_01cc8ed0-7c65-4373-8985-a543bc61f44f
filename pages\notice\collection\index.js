const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const PopupMenuFilterMixin = require("@/components/popup/popup-menu-filter/mixin")
const { handleMultiSelect } = require("@/services/selectionService")
const noticeMixin = require("../mixins/noticeMixin")
const QuestionParseHtml = require("@/utils/QuestionParseHtml")
const {
  setCollectionSelectForTemplateCache,
  getCollectionSelectForTemplateCache,
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  getOfficialNewsCache,
} = require("@/utils/cache/filterCache")
let PAGE_OPTIONS = {} // 页面进入时参数
/**
 * 专题合辑页面
 * 集成筛选菜单功能
 * @mixes PopupMenuFilterMixin 弹窗筛选混合方法
 */

const pageConfig = Object.assign({}, PopupMenuFilterMixin, noticeMixin, {
  data: {
    show_white: false,
    isCollect: false,

    // 筛选菜单相关数据
    showPopupFilterMenu: false,
    headerHeight: 100,
    menuSticky: false,
    menuOffsetTop: 0,

    // 页面滚动控制
    pageScrollDisabled: false,

    menuList: [],
    collectionSelectForTemplate: {},
    collectionMenuData: {},
    articleList: [], // 文章列表
    show_white: false,
    isCollect: false,
    jobDetailSelectForTemplate: {},
    detailMenuData: {},
    activeExpanded: "",
    pageType: "",
    isLogin: false,
    redDot: false,
    tagStyle: {
      table:
        "border-top: 1px solid gray; border-left: 1px solid gray; border-collapse:collapse",
      th: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
      td: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
    },
    isPageLoadComplete: false,
  },

  async onLoad(options) {
    PAGE_OPTIONS = options
    // 初始化弹窗管理功能
    // 将 PAGE_OPTIONS 设置为页面实例属性
    this.PAGE_OPTIONS = options
    this.initPopupMenuFilterMixin()
    this.initNoticeMixin()
    this.syncApplyStatusState()
    await this.getNoticeDetail()
    // 从缓存恢复筛选条件
    if (this.data.pageType == "collection") {
      await this.restoreFilterFromCache()
      this.setCollectionMenuStickyTop()
      this.applyFilter()
    } else {
      // 页面渲染完成后获取导航栏高度并设置sticky的top值
      this.setMenuStickyTop()
      await this.updateJobSelectForTemplateFromCache()
    }
  },
  async onShow() {
    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }

    if (this.data.pageType == "detail") {
      console.log("合辑详情页显示，检查职位筛选条件变化")

      // 保存当前展开状态和按钮显示状态，避免被重置
      const currentExpandedState = this.data.isExpanded
      const currentShowToggle = this.data.showToggle

      // 保存当前状态用于对比
      const currentState = this.data.jobDetailSelectForTemplate
      const cachedState = getJobDetailSelectForTemplateCache()

      console.log("状态对比数据:", {
        currentState,
        cachedState,
        currentExpandedState,
        currentShowToggle
      })

      // 检查是否有缓存数据且状态发生变化
      const hasCachedData = Object.keys(cachedState).length > 0
      const hasStateChanged =
        hasCachedData && !UTIL.isObjectEqual(currentState, cachedState)

      console.log("状态变化检查结果:", {
        hasCachedData,
        hasStateChanged,
      })
      
      // 获取最新的公告详情数据
      await this.getNoticeDetail()
      
      // 从缓存恢复筛选条件
      this.updateJobSelectForTemplateFromCache()

      // 如果当前是职位列表Tab且状态发生变化，重新请求数据
      if (this.shouldRefreshJobList(hasStateChanged)) {
        console.log("检测到职位筛选条件变化，重新请求职位数据")
        this.applyFilter()
        this.scrollToTop()
      } else {
        console.log("职位筛选条件未变化或不在职位Tab，保持当前数据")
      }

      // 在所有操作完成后，最后恢复展开状态和按钮状态
      setTimeout(() => {
        if (currentExpandedState) {
          console.log("延迟恢复之前的展开状态和按钮状态")
          this.setData({
            isExpanded: true,
            showToggle: currentShowToggle // 恢复原来的按钮显示状态
          })
        }
      }, 100)
    }

    // 初始化胶囊按钮颜色
    if (this.data.show_white) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  // 设置菜单sticky的top值
  setCollectionMenuStickyTop() {
    const query = wx.createSelectorQuery()
    query
      .select("#commonHeader")
      .boundingClientRect((headerRect) => {
        let headerHeight = 100 // 默认高度
        console.log("获取到的header信息:", headerRect)

        if (headerRect) {
          headerHeight = headerRect.height
          console.log("成功获取导航栏高度:", headerHeight)
        } else {
          console.log("无法获取导航栏高度，使用降级方案")
          // 降级方案：通过系统信息计算
          const systemInfo = wx.getSystemInfoSync()
          const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
          headerHeight = menuButtonInfo.bottom
          console.log("降级方案计算高度:", headerHeight)
        }

        // 获取菜单容器的位置信息
        const menuQuery = wx.createSelectorQuery()
        menuQuery.select(".main-top").boundingClientRect()
        menuQuery.selectViewport().scrollOffset()
        menuQuery.exec((res) => {
          const menuRect = res[0]
          const scrollOffset = res[1]

          let menuOffsetTop = 0
          if (menuRect) {
            // 计算菜单距离页面顶部的距离
            menuOffsetTop = menuRect.top + scrollOffset.scrollTop
          }

          // 设置数据
          this.setData({
            headerHeight: headerHeight,
            menuOffsetTop: menuOffsetTop,
          })

          console.log("导航栏高度:", headerHeight)
          console.log("菜单初始位置:", menuOffsetTop)
        })
      })
      .exec()
  },

  async getArticleChildList(param, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.isLoading ||
      (!isLoadMore && !this.data.hasMore && this.data.page > 1)
    ) {
      return
    }

    const params = {
      page: this.data.page,
      article_id: PAGE_OPTIONS.id,
      ...param,
    }

    this.setData({
      isLoading: true,
    })

    try {
      const res = await UTIL.request(API.getArticleChildList, params)
      if (res && res.error && res.error.code === 0 && res.data) {
        const newList = res?.data?.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedArticleList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedArticleList = [...this.data.articleList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedArticleList = newList
        }

        this.setData({
          articleList: updatedArticleList,
          hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          isLoading: false,
        })

        console.log(res, "文章列表数据")
        console.log(
          this.data.collectionMenuData,
          this.data.menuList,
          this.data.collectionSelectForTemplate,
          "--------------------------------------222"
        )
      } else {
        this.setData({
          isLoading: false,
          hasMore: false,
        })
      }
    } catch (error) {
      console.error("获取文章列表失败:", error)
      this.setData({
        isLoading: false,
      })
    }
  },
  async getNoticeDetail() {
    try {
      const res = await UTIL.request(API.getArticleDetail, {
        id: PAGE_OPTIONS?.id,
      })
      if (res && res.error && res.error.code === 0 && res.data) {
        console.log(res, "公告详情")
        let resData = res.data
        let pageType = ""
        if (resData?.detail?.content?.body_content) {
          // 处理富文本内容，主要是给table外层包装div容器
          resData.detail.content.body_content = QuestionParseHtml.processRichTextContent(
            resData.detail.content.body_content
          )
        }
        // console.log()
        if (res.data.type == "article_list" && res.data.show_type == "list") {
          pageType = "collection"
        } else {
          pageType = "detail"
        }
        // 判断官方动态是否有更新
        let hasOfficialUpdate = false
        const noticeId = resData.id
        const latestReleaseTime = resData.detail?.notice_latest_release_time

        if (latestReleaseTime && noticeId) {
          // 从缓存获取点击时间记录
          const officialClickRecord = getOfficialNewsCache() || {}
          const lastClickTime = officialClickRecord[noticeId]

          if (lastClickTime) {
            // 有缓存：比较缓存时间和最新发布时间
            hasOfficialUpdate = lastClickTime < latestReleaseTime
          } else {
            // 没有缓存：比较当前时间和最新发布时间
            const currentTime = Date.now()
            hasOfficialUpdate = currentTime > latestReleaseTime
          }
        }

        this.setData({
          isRequest: true,
          noticeData: resData,
          pageType,
          isPageLoadComplete: true,
          redDot: hasOfficialUpdate, // 设置红点状态
          isShowResume:
            resData.detail?.complete_progress?.is_tips === 1 &&
            !APP.globalData.hasResume,
          isLogin: APP.getIsLogin(),
        })
        if (res.data?.detail?.filter_menu?.length) {
          this.initDynamicMenu(res.data.detail.filter_menu)
        }
        if ((pageType = "detail")) {
          // 根据动态数据生成Tab列表
          this.generateDynamicTabs()
          // 重新检测内容高度（因为数据已更新）
          // 只有在内容未展开过时才检测高度，避免已展开状态下滚动到页面顶部
          if (!this.data.isExpanded) {
            this.checkContentHeight()
          } else {
            console.log('内容已展开，跳过高度检测')
          }
        }
      } else {
        this.setData({
          isRequest: true,
        })
      }
    } catch (error) {
      console.error("获取公告详情失败:", error)
    }
  },

  handleNoticeMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`collectionSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
    this.hidePopupMenuFilter()
    this.applyFilter()
  },

  /**
   * 处理菜单项点击事件
   * @param {Object} e 事件对象
   */
  handleCollectionMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showPopupFilterMenu, collectionSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key

    const currentMenuSelected = collectionSelectForTemplate[filterKey]

    if (type !== "apply_region") {
      this.setData({
        showRegionList: false,
      })
    }

    if (type === "check" || type === "apply_region") {
      this.hidePopupMenuFilter()
    } else if (
      showPopupFilterMenu === true &&
      this.data.activeExpanded === filterKey
    ) {
      this.hidePopupMenuFilter()
    } else {
      this.showPopupMenuFilter()
    }

    this.setData({
      activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
    if (filterKey === "apply_region") {
      // 切换地区列表显示状态
      this.setData({
        showRegionList: !this.data.showRegionList,
      })
      return
    }

    // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "check") {
      this.setData({
        [`collectionSelectForTemplate.${filterKey}`]: handleMultiSelect(
          currentMenuSelected || [],
          1
        ),
      })
      setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
      this.applyFilter()
      return
    }
  },

  handleRegionConfirmSelection(e) {
    const { filterKey, tempSelected } = e.detail
    console.log(filterKey, tempSelected)
    if (this.data.pageType == "collection") {
      this.setData({
        [`collectionSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
    } else {
      this.setData({
        [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
    }
    this.applyFilter()
    this.hidePopupMenuFilter()
  },
  /**
   * 从缓存恢复筛选条件
   */
  async restoreFilterFromCache() {
    await this.updateNoticeSelectForTemplateFromCache()
  },
  /**
   * 从缓存恢复公告选中状态 (noticeSelectForTemplate)
   */
  updateNoticeSelectForTemplateFromCache() {
    const collectionSelectForTemplate = this.data.collectionSelectForTemplate
    const cacheCollectionSelectForTemplate = getCollectionSelectForTemplateCache()
    for (const key in collectionSelectForTemplate) {
      if (cacheCollectionSelectForTemplate[key]) {
        collectionSelectForTemplate[key] = cacheCollectionSelectForTemplate[key]
      }
    }
    this.setData({
      collectionSelectForTemplate,
    })
  },
  async getArticleNoticeList() {
    let requestParams = {
      id: PAGE_OPTIONS.id,
    }
    const res = await UTIL.request(API.getArticleNoticeList, requestParams)
    if (res && res.error && res.error.code === 0 && res.data) {
      const resData = res.data
      this.setData({
        officialList: resData.list,
      })
      console.log(resData, "------------")
    }
  },

  onPageScroll(e) {
    console.log(e, "触发滚动了")
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }
    console.log("dayiasdasd asd")

    const scrollTop = e.scrollTop
    const { headerHeight } = this.data

    // 处理头部背景色变化
    if (scrollTop > 0) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
        // 设置胶囊按钮为黑色（适用于白色背景）
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
        // 设置胶囊按钮为白色（适用于深色背景）
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      }
    }

    // 检测菜单是否吸顶
    // 当滚动距离大于等于菜单到顶部的距离减去导航栏高度时，菜单开始吸顶
    const { menuOffsetTop } = this.data
    const isMenuSticky =
      menuOffsetTop > 0 && scrollTop >= menuOffsetTop - headerHeight

    if (isMenuSticky !== this.data.menuSticky) {
      this.setData({
        menuSticky: isMenuSticky,
      })
    }
  },

  onReachBottom() {
    console.log("触底了")
    if (this.data.pageType == "collection") {
      if (this.data.hasMore && !this.data.isLoading) {
        // 页码+1
        const nextPage = this.data.page + 1
        this.setData({
          page: nextPage,
        })

        // 加载下一页数据
        const apiParams = this.buildApiParams(
          this.data.collectionSelectForTemplate
        )
        this.getArticleChildList(apiParams, true)
      }
    } else {
      if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
        // 检查是否还有更多数据且当前不在加载中
        if (this.data.hasMore && !this.data.isLoading) {
          // 页码+1
          const nextPage = this.data.page + 1
          this.setData({
            page: nextPage,
          })

          // 加载下一页数据
          const apiParams = this.buildApiParams(
            this.data.jobDetailSelectForTemplate
          )
          this.getJobList(apiParams, true)
        }
      }
    }
  },
  // 获取分享参数
  getPageShareParams() {
    const share_info = this.data?.noticeData?.share_info || {}
    let shareTitle = share_info.title
    let shareImageUrl = share_info.image
    console.log(PAGE_OPTIONS.id, "--=---------------------------")
    const params = {
      title: shareTitle,
      imageUrl: shareImageUrl,
      path: "/pages/notice/collection/index",
      query: {
        id: PAGE_OPTIONS.id,
      },
    }
    return params
  },
  onShareAppMessage() {
    return APP.createShareParams(this.getPageShareParams())
  },
  // 分享朋友圈
  onShareTimeline() {
    const params = this.getPageShareParams()
    delete params.imageUrl
    const result = APP.createShareParams(params)
    result.query = ROUTER.convertPathQuery(result.query)
    return result
  },
})

Page(pageConfig)
