const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    dataItem: {
      type: Object,
      value: {},
    },
    hideDelete: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 选择卡片
    onSelectCard() {
      const { dataItem } = this.properties

      // 如果是禁用状态，不允许点击
      if (dataItem.isDisabled) {
        return
      }

      // 触发自定义事件，通知父组件
      this.triggerEvent("select", {
        id: dataItem.id,
        isChecked: !dataItem.isChecked,
      })
    },

    // 删除卡片
    onDeleteCard() {
      const { dataItem } = this.properties
      // 触发自定义事件，通知父组件
      this.triggerEvent("delete", {
        id: dataItem.id,
      })
    },
  },
})
