Component({
  properties: {
    percentage: {
      type: Number,
      value: 0,
      observer: "updateProgress",
    },
  },
  data: {
    // We will pass the calculated angle to CSS
    progressAngle: 0,
  },
  methods: {
    updateProgress(newPercentage) {
      const newNum = newPercentage + 100
      const angle = (newNum / 100) * 180
      this.setData({
        progressAngle: angle,
      })
    },
  },
  attached() {
    this.updateProgress(this.properties.percentage)
  },
})
