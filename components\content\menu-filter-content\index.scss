/* 菜单筛选内容组件样式（重构版） */

/* 弹窗主体 */
.popup-main {
  position: relative;
  background: #ffffff;
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
  // box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  /* 移除过渡动画 */
  /* transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); */

  /* 弹窗头部 */
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 24rpx 32rpx;
    border-bottom: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);
    background: #ffffff;
    position: sticky;
    top: 0;
    z-index: 10;

    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      color: rgba(34, 36, 46, 1);
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .close-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  /* 弹窗内容区域 */
  .popup-body {
    padding: 32rpx;
    max-height: 60vh;
    overflow-y: auto;
    /* 确保内部滚动正常工作 */
    -webkit-overflow-scrolling: touch;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */

    &::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }
  }

  /* 弹窗底部 */
  .popup-footer {
    padding: 24rpx 32rpx 32rpx 32rpx;
    border-top: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);
    background: #ffffff;
    position: sticky;
    bottom: 0;
    z-index: 10;

    .btn-group {
      display: flex;
      gap: 16rpx;

      .btn {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12rpx;
        font-size: 28rpx;
        font-weight: 500;
        /* 移除过渡动画 */
        /* transition: all 0.3s ease; */
        cursor: pointer;

        &.btn-reset {
          background: rgba(247, 248, 250, 1);
          border: 1rpx solid rgba(235, 236, 240, 1);
          transform: rotateZ(360deg);
          color: rgba(60, 61, 66, 1);

          &:active {
            background: rgba(235, 236, 240, 1);
          }
        }

        &.btn-confirm {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: #ffffff;

          &:active {
            /* 移除缩放和透明度动画 */
            /* transform: scale(0.98); */
            /* opacity: 0.9; */
          }
        }
      }
    }
  }
}

/* 动画效果 - 移除所有动画过渡 */
.animation-slide-down {
  /* 移除动画，直接显示 */
  /* transform: translateY(-100%); */

  &.show {
    /* transform: translateY(0); */
  }
}

.animation-fade {
  /* 移除动画，直接显示 */
  /* opacity: 0; */

  &.show {
    /* opacity: 1; */
  }
}

.animation-scale {
  /* 移除动画，直接显示 */
  /* transform: scale(0.8); */
  /* opacity: 0; */

  &.show {
    /* transform: scale(1); */
    /* opacity: 1; */
  }
}

.menu-filter-content {
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: rgba(60, 61, 66, 1);
    margin-bottom: 24rpx;
  }

  /* 选项区域 */
  .option-section {
    .option-list {
      .option-item {
        padding: 24rpx 32rpx;
        background: rgba(247, 248, 250, 1);
        border-radius: 12rpx;
        margin-bottom: 16rpx;
        font-size: 28rpx;
        color: rgba(60, 61, 66, 1);
        /* 移除过渡动画 */
        /* transition: all 0.3s ease; */
        position: relative;
        cursor: pointer;

        &:last-child {
          margin-bottom: 0;
        }

        &:active {
          background: rgba(230, 0, 3, 0.1);
          color: rgba(230, 0, 3, 1);
        }

        &.selected {
          background: rgba(230, 0, 3, 0.1);
          color: rgba(230, 0, 3, 1);
          border: 1rpx solid rgba(230, 0, 3, 0.3);
          transform: rotateZ(360deg);
          &::after {
            content: "✓";
            position: absolute;
            right: 24rpx;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24rpx;
            color: rgba(230, 0, 3, 1);
            font-weight: 600;
          }
        }
      }
    }
  }

  /* 筛选区域 */
  .filter-section {
    .filter-group {
      .filter-item {
        margin-bottom: 32rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .filter-label {
          font-size: 28rpx;
          font-weight: 500;
          color: rgba(60, 61, 66, 1);
          margin-bottom: 16rpx;
        }

        .filter-options {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .filter-tag {
            padding: 16rpx 24rpx;
            background: rgba(247, 248, 250, 1);
            border: 1rpx solid rgba(235, 236, 240, 1);
            transform: rotateZ(360deg);
            border-radius: 8rpx;
            font-size: 24rpx;
            color: rgba(60, 61, 66, 1);
            /* 移除过渡动画 */
            /* transition: all 0.3s ease; */
            cursor: pointer;

            &:active {
              /* 移除缩放动画 */
              /* transform: scale(0.95); */
            }

            &.selected {
              background: rgba(230, 0, 3, 0.1);
              border-color: rgba(230, 0, 3, 1);
              color: rgba(230, 0, 3, 1);
            }
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .popup-main {
    .popup-body {
      max-height: 50vh;
      padding: 24rpx;
    }
  }

  .menu-filter-content {
    .filter-section {
      .filter-group {
        .filter-item {
          .filter-options {
            .filter-tag {
              flex: 1;
              min-width: 0;
              text-align: center;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 600rpx) {
  .popup-main {
    .popup-body {
      max-height: 45vh;
      padding: 16rpx;
    }
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .popup-main {
    background: #1a1a1a;

    .popup-header,
    .popup-footer {
      background: #1a1a1a;
      border-color: #333333;

      .header-title {
        color: #ffffff;
      }
    }

    .btn-reset {
      background: #333333 !important;
      border-color: #555555 !important;
      color: #ffffff !important;
    }
  }

  .menu-filter-content {
    .section-title {
      color: #ffffff;
    }

    .option-section {
      .option-list {
        .option-item {
          background: #333333;
          color: #ffffff;

          &:active,
          &.selected {
            background: rgba(230, 0, 3, 0.2);
            color: rgba(255, 100, 100, 1);
          }
        }
      }
    }

    .filter-section {
      .filter-group {
        .filter-item {
          .filter-label {
            color: #ffffff;
          }

          .filter-options {
            .filter-tag {
              background: #333333;
              border-color: #555555;
              color: #ffffff;

              &.selected {
                background: rgba(230, 0, 3, 0.2);
                border-color: rgba(255, 100, 100, 1);
                color: rgba(255, 100, 100, 1);
              }
            }
          }
        }
      }
    }
  }
}
