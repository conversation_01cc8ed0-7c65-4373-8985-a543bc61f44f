// pages/login/index.js
const APP = getApp()
Component({
  properties: {
    isBindPhone: <PERSON><PERSON><PERSON>,
    key: {
      type: String,
      optionalTypes: [Object],
    },
    attach: Object,
    payload: null, // 附加参数，默认没有
  },

  methods: {
    // 手机号授权
    getPhoneNumber: async function (e) {
      let { encryptedData, iv } = {
        ...e.detail,
      }
      const result = await APP.userLogin({
        encryptedData,
        iv,
        attach: this.data.attach,
      })
      if (!result) {
        wx.showToast({
          title: "请登录",
          icon: "none",
        })
        return false
      }
      // 上报数据
      try {
        wx.reportEvent("event_user_regiester", {
          options: JSON.stringify(APP.globalData.launchOptions),
        })
      } catch (error) {
        wx.reportEvent("event_user_regiester", {
          options: APP.globalData.launchOptions,
        })
      }

      if (this.data.payload) {
        this.triggerEvent("onAuthorize", {
          payload: this.data.payload,
          userInfo: result,
        })
        return
      }
      this.triggerEvent("onAuthorize", this.data.key ? this.data.key : result)
    },
    tapPhoneButton() {
      if (this.data.payload) {
        this.triggerEvent("onAuthorize", {
          payload: this.data.payload,
          userInfo: APP.globalData.userInfo,
        })
        return
      }

      this.triggerEvent(
        "onAuthorize",
        this.data.key ? this.data.key : APP.globalData.userInfo
      )
    },
  },
})
