const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isFormComplete: false, // 表单是否完整
    detailData: {},
    educationList: [], // 教育经历列表
    isComplete: false,
    pageOverflow: "visible",
  },

  /**
   * 获取简历详情
   */
  async getInfo() {
    try {
      const res = await UTIL.request(API.getResumeInfo)
      if (res && res.error?.code === 0) {
        this.setData({
          detailData: res.data,
          educationList: res.data.info.education_record_list || [],
          isComplete: true,
        })
        // 获取数据后立即检查表单完整性
        setTimeout(() => {
          this.checkFormComplete()
        }, 100)
      }
    } catch (error) {
      console.error("获取简历详情失败:", error)
      wx.showToast({
        title: "获取简历详情失败",
        icon: "none",
      })
    }
  },
  onLoad() {
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (this.data.isComplete) {
      // 如果已经初始化完成，重新获取数据
      this.getInfo()
    } else {
      // 如果还未初始化完成，延迟检查
      setTimeout(() => {
        this.checkFormComplete()
      }, 200)
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 检查表单完整性，只校验报考地区和教育经历两个必填项
   */
  checkFormComplete() {
    console.log("开始检查表单完整性...")

    let isComplete = true
    let checkResults = {
      region: false,
      education: false,
    }

    // 1. 校验报考地区
    const baseInfoCard = this.selectComponent("#base-info-card")
    if (baseInfoCard && baseInfoCard.data.formList) {
      const regionItem = baseInfoCard.data.formList.find(
        (item) => item.field === "region"
      )
      if (regionItem && regionItem.value && regionItem.value.length > 0) {
        checkResults.region = true
      }
    }

    // 2. 校验教育经历（只要 educationList.length > 0 即可）
    const educationList = this.data.educationList || []
    if (educationList.length > 0) {
      checkResults.education = true
    }

    // 两个必填项都满足才算完整
    isComplete = checkResults.region && checkResults.education

    console.log("表单完整性检查结果:", {
      region: checkResults.region,
      education: checkResults.education,
      educationListLength: educationList.length,
      isComplete,
    })

    this.setData({
      isFormComplete: isComplete,
    })
  },

  /**
   * 基本信息数据变化监听
   */
  onBaseInfoDataChange(e) {
    console.log("基本信息数据变化:", e.detail)
    this.checkFormComplete()
  },

  /**
   * 教育经历数据变化监听
   */
  onEducationDataChange(e) {
    console.log("教育经历数据变化:", e.detail)
    // 更新父组件的教育经历数据
    if (e.detail && e.detail.educationList) {
      this.setData({
        educationList: e.detail.educationList,
      })
    }
    if (e.detail && e.detail.graduateStatus) {
      this.setData({
        ["detailData.info.fresh_graduate.value"]: e.detail.graduateStatus,
      })
    }
    this.checkFormComplete()
  },

  /**
   * 工作经历数据变化监听
   */
  onWorkInfoDataChange(e) {
    console.log("工作经历数据变化:", e.detail)
    this.checkFormComplete()
  },

  /**
   * 保存简历
   */
  async onAddJobs() {
    if (!this.data.isFormComplete) {
      wx.showToast({
        title: "请完善必填信息",
        icon: "none",
      })
      return
    }
    // 收集所有卡片信息
    const baseInfoCard = this.selectComponent("#base-info-card")
    const educationCard = this.selectComponent("#education-card")
    const workInfoCard = this.selectComponent("#work-info-card")
    const baseInfo = baseInfoCard ? baseInfoCard.data.formList : []
    const educationList = educationCard ? educationCard.data.educationList : []
    const graduateStatus = educationCard
      ? educationCard.data.graduateStatus
      : ""
    const workInfo = workInfoCard ? workInfoCard.data.formList : []
    console.log(baseInfo, "1111")
    const param = {
      region_list: baseInfo[0].value,
      birth_date: baseInfo[1].value,
      gender: baseInfo[2].value,
      politics_face: baseInfo[3].value,
      // nation_id: baseInfo[4].value,
      // domicile_province: baseInfo[5].value[0],
      // domicile_city: baseInfo[5].value[1],
      // domicile_district: baseInfo[5].value[2],
      fresh_graduate: graduateStatus,
      service_project: workInfo[0].value,
      work_years: workInfo[1].value,
    }
    const res = await UTIL.request(API.saveResume, param)
    if (res) {
      wx.showToast({
        title: "保存成功",
        icon: "none",
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 跳转到添加教育经历页面
   */
  onAddEducation() {
    wx.navigateTo({
      url: "/pages/my/addEducation/index",
    })
  },

  /**
   * 编辑教育经历
   */
  onEditEducation(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/my/addEducation/index?id=${id}`,
    })
  },
  onDialogStateChange(e) {
    this.setData({
      pageOverflow: e.detail.show ? "hidden" : "visible",
    })
  },
})
