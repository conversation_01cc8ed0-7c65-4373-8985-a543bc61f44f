// 菜单处理工具函数

/**
 * 处理单个菜单项
 * @param {Object} menuItem 菜单项数据
 * @returns {Object} 处理后的菜单项
 */
function processMenuItem(menuItem) {
  // 深拷贝整个菜单项，保留所有原始属性
  const processedMenuItem = JSON.parse(JSON.stringify(menuItem))
  
  // 确保关键属性存在
  processedMenuItem.key = processedMenuItem.filter_key || processedMenuItem.key
  processedMenuItem.selected = false
  
  // 根据type类型初始化相关数据
  if (processedMenuItem.type === "dialog_grid" && processedMenuItem.data && processedMenuItem.data.length > 0) {
    // 处理dialog_grid类型的数据
    if (processedMenuItem.data.length === 1) {
      // 单个数据组，类似考试类型的展现方式
      processedMenuItem.examData = processedMenuItem.data[0].list || []
    } else {
      // 多个数据组，类似筛选的展现方式
      processedMenuItem.filterData = processedMenuItem.data
    }
  }

  return processedMenuItem
}

/**
 * 处理菜单列表，返回二维数组格式的 menuList
 * @param {Array} serverMenuList 服务器返回的菜单列表
 * @returns {Array} 处理后的 menuList（二维数组）
 */
function processMenuList(serverMenuList) {
  if (!Array.isArray(serverMenuList) || serverMenuList.length === 0) return []

  // 检查是否有is_fixed为true的菜单项
  const hasFixedMenus = serverMenuList.some(
    (menuItem) => menuItem.is_fixed === true
  )

  if (hasFixedMenus) {
    // 分离固定菜单和非固定菜单
    const fixedMenus = serverMenuList.filter(
      (menuItem) => menuItem.is_fixed === true
    )
    const nonFixedMenus = serverMenuList.filter(
      (menuItem) => menuItem.is_fixed === false
    )

    // 处理非固定菜单（左侧滚动部分）
    const dynamicLeftMenuList = nonFixedMenus.map(processMenuItem)
    // 处理固定菜单（右侧固定部分）
    const dynamicRightMenuList = fixedMenus.map(processMenuItem)

    // 返回新的二维数组格式：[[leftMenus], [rightMenus]]
    return [dynamicLeftMenuList, dynamicRightMenuList]
  } else {
    // 所有菜单都是is_fixed为false，使用单行布局：[[allMenus]]
    const dynamicMenuList = serverMenuList.map(processMenuItem)
    return [dynamicMenuList]
  }
}

module.exports = {
  processMenuList,
}
