<wxs module="utils" src="./index.wxs"></wxs>
<view class="big-data" bindtap="onPageClick" data-path="root" wx:if="{{isComplete}}">
  <!-- 固定头部导航栏 -->
  <view class="fixed-header {{showFixedHeader ? 'show' : 'hide'}}" style="padding-top: {{statusBarHeight}}px;">
    <view class="header-content">
      <view class="header-left" catch:tap="onBackClick">
        <view class="back-icon"></view>
      </view>
      <view class="header-title">进面分数线</view>
      <view class="header-right"></view>
    </view>
  </view>

  <image class="back-btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data-top_back.png" mode="" catch:tap="onBackClick" />
  <view class="top-bg">
    <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/interview_bg.png" mode="" />
    <view class="top-area">
      <view class="title">进面分数线</view>
      <view class="select-one">
        <view class="select-item" bindtap="onExamTypeClick">
          <view class="text">{{selectedExamTypeText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onRegionClick">
          <view class="text">{{selectedRegionText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
      </view>
      <view class="select-item w100" bindtap="onSpecificExamClick">
        <view class="text">{{project_name}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
      </view>
    </view>
  </view>
  <view class="main-content">
    <!-- Tab导航区域 -->
    <view wx:if="{{articleList && articleList.length > 1}}" class="tab-container sticky-tab-container {{ isTabSticky?'no-radius':''}}" style="top: {{ statusBarHeight + 44 }}px;">
      <scroll-view class="tab-scroll" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" enhanced="{{true}}">
        <view class="tab-list">
          <view wx:for="{{articleList}}" wx:key="index" class="tab-item {{currentTab === index ? 'active' : ''}}" data-index="{{index}}" data-id="{{item.id}}" bindtap="onTabClick" id="tab-{{index}}">
            {{item.title}}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 内容区域 -->
    <view class="content-container {{articleList && articleList.length > 1 ?'': 'no-tab-list'}}">
      <view class="content-area">
        <!-- 招聘公告标题 -->
        <view class="announcement-title" catch:tap="goDetail">
          <text class="text">{{detailData.title}}</text>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png" mode="" />
        </view>

        <view class="data-flex-box" wx:if="{{detailData.min_score_info.min_min_score}}">
          <view class="flex-item">
            <view class="num">{{ utils.formatNumber(detailData.min_score_info.min_min_score) || "-" }}</view>
            <view class="text">最低进面分数线</view>
          </view>
          <view class="flex-item">
            <view class="num cred">{{ utils.formatNumber(detailData.min_score_info.max_min_score) || "-" }}</view>
            <view class="text">最高进面分数线</view>
          </view>
          <view class="flex-item">
            <view class="num corange">{{ utils.formatNumber(detailData.min_score_info.avg_min_score) || "-" }}</view>
            <view class="text">平均进面分数线</view>
          </view>
        </view>

        <view class="data-desc {{dataDescExpanded ? 'expanded' : 'collapsed'}}">
          <view class="data-desc-wrapper">
            <text class="data-desc-text">数据说明：{{ detailData.desc }}</text>
            <view wx:if="{{dataDescExpanded}}" class="expand-btn inline" bindtap="onToggleDataDesc">
              <text class="blue-text">收起</text>
              <image class="arrow-icon rotated" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
            </view>
          </view>
          <view wx:if="{{!dataDescExpanded}}" class="expand-btn" bindtap="onToggleDataDesc">
            <text class="blue-text">展开</text>
            <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
          </view>
        </view>
      </view>
      <view class="zhanwei-box"></view>

      <!-- 各地域报名数据 -->
      <view class="area-box" wx:if="{{detailData.region_min_score_list && detailData.region_min_score_list.length > 1}}">
        <view class="title">各地域进面数据</view>
        <view catch:tap>
          <score-all-echarts id="score-bar-id-multiple" scoreData="{{detailData.region_min_score_list}}" chartType="interview-score"></score-all-echarts>
        </view>
      </view>
      <view class="zhanwei-box" wx:if="{{detailData.region_min_score_list && detailData.region_min_score_list.length > 1}}"></view>

      <view class="position-table-section" wx:if="{{currentTableData.length }}">
        <!-- 标题区域 -->
        <view class="position-header">
          <view class="position-title">职位进面分数查询</view>
          <view class="position-location" bindtap="onPositionLocationClick" wx:if="{{detailData.region_min_score_list && detailData.region_min_score_list.length > 1}}">
            <text class="location-text">{{region_name}}</text>
            <image class="location-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png" mode="" />
          </view>
        </view>

        <!-- Tab导航 -->
        <view class="position-tab-container">
          <view class="position-tab-list">
            <view class="position-tab-item {{currentMainTab === 'desc' ? 'active' : ''}}" data-index="desc" bindtap="onMainTabClick">
              高分TOP10
            </view>
            <view class="position-tab-item {{currentMainTab === 'asc' ? 'active' : ''}}" data-index="asc" bindtap="onMainTabClick">
              低分TOP10
            </view>
          </view>
        </view>

        <!-- 表格1内容 -->
        <view class="position-table-content">
          <!-- 表格头部 -->
          <view class="position-table-header">
            <view class="header-item header-rank"></view>
            <view class="header-item header-position">
              <text>职位</text>
              <text>名称</text>
            </view>
            <view class="header-item header-unit">
              <text>招考</text>
              <text>单位</text>
            </view>
            <view class="header-item header-recruit">
              <text>最低进</text>
              <text>面分</text>
            </view>
          </view>

          <!-- 表格数据 -->
          <view class="position-table-body">
            <view wx:for="{{currentTableData}}" wx:key="index" class="position-table-row">
              <view class="table-item table-rank">
                <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
              </view>
              <view class="table-item table-position" bind:tap="goJobDetail" data-id="{{item.id}}">
                <text class="position-title">{{item.name}}</text>
              </view>
              <view class="table-item table-unit">
                <text class="unit-title">{{item.work_unit}}</text>
              </view>
              <view class="table-item table-recruit">
                <text class="recruit-num">{{ utils.formatNumber(item.min_score) || "-" }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="zhanwei-box" wx:if="{{currentTableData.length }}"></view>

      <view class="job-search" id="job-search-section">
        <view class="title">职位进面分数查询</view>
        <view class="search-box">
          <image class="bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_bg.png" mode="" />
          <!-- <view class="top-area">
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_top_icon.png" mode="" />
        <view class="text">2025年重庆市公务员</view>
      </view> -->
          <view class="white-box">
            <view class="select-item" bindtap="onPositionRegionClick">
              <view class="left">
                <view class="sp5">
                  <text>地</text>
                  <text>区：</text>
                </view>
                <view>{{positionRegionSelector.displayText}}</view>
              </view>
              <image wx:if="{{positionRegionSelector.editable}}" class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
              <image wx:else class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select_disable.png" mode="" />
            </view>
            <view class="select-item" bindtap="onSearchUnitClick">
              <view class="left">
                <view class="sp5">
                  用人单位：
                </view>
                <view>{{selectedSearchUnitKey || '全部单位'}}</view>
              </view>
              <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
            </view>
            <view class="select-item input-item">
              <view class="left">
                <view class="sp5">
                  <text>职</text>
                  <text>位：</text>
                </view>
                <input class="position-input" type="text" placeholder="请输入职位名称或代码" value="{{positionInput}}" bindinput="onPositionInput" placeholder-class="input-placeholder" />
              </view>
            </view>
            <view class="button-row">
              <view class="clear-btn" bindtap="onClearClick">清除</view>
              <view class="search-btn" bind:tap="onSearchClick">
                <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_search_icon.png" mode="" />
                <text>查询</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 查询结果表格区域 -->
      <view class="search-result-table" wx:if="{{jobList.length}}">
        <view class="table-header">
          <view class="header-item header-position">职位名称</view>
          <view class="header-item header-unit">工作单位</view>
          <view class="header-item header-ratio">竞争比数据</view>
        </view>
        <view class="table-body">
          <view wx:for="{{jobList}}" wx:key="index" class="table-row">
            <view class="table-item table-position" bind:tap="goJobDetail" data-id="{{item.id}}">
              <text class="position-name">{{item.name}}</text>
            </view>
            <view class="table-item table-unit">
              <text class="unit-name">{{item.work_unit}}</text>
            </view>
            <view class="table-item table-ratio">
              <text class="ratio-value">{{utils.formatNumber(item.min_score)}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<loading-spinner wx:else />


<!-- 底部操作区域 -->
<!-- <tabbar-box>
    <view class="bottom-box">
      <view class="left">
        <view class="icon-item" bind:tap="goJobs">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_service.png" mode="" />
          <view class="icon-text">选岗咨询</view>
        </view>
        <view class="icon-item" bind:tap="checkNews">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/news.png" mode="" />
          <view class="icon-text">查看公告</view>
        </view>
      </view>
      <view class="right-btn" bind:tap="goWeb">岗位报考查询</view>
    </view>
  </tabbar-box> -->

<!-- 下拉框弹窗 -->
<van-popup show="{{ optPopShow }}" round position="bottom" bind:close="OptClose">
  <van-picker id="myPicker" show-toolbar title="{{ optTitle }}" columns="{{ optList }}" value-key="key" columns-field-names="{{ {text: 'name', value: 'key'} }}" default-index="{{ defaultIndex }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="OptClose" bind:confirm="onConfirm" />
</van-popup>

<!-- 岗位报考查询地区选择器 -->
<van-popup show="{{ positionRegionSelector.show }}" round position="bottom" z-index="9999" bind:close="onPositionRegionSelectorClose">
  <van-picker id="positionRegionPicker" show-toolbar title="{{ positionRegionSelector.title }}" columns="{{ positionRegionSelector.columns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onPositionRegionSelectorClose" bind:confirm="onPositionRegionSelectorConfirm" bind:change="onPositionRegionSelectorChange" />
</van-popup>

<!-- 底部导航栏 -->
<!-- <home-tabbar active="my" /> -->