/* 菜单筛选弹窗组件样式（简化版） */

/* 遮罩层 */
.overlay-mask {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2rpx);
  z-index: 9998;
  opacity: 0;
  /* 移除过渡动画 */
  /* transition: opacity 0.3s ease; */

  &.show {
    opacity: 1;
  }
}

/* 弹窗容器 */
.popup-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9999;
  opacity: 0;
  /* 移除过渡动画 */
  /* transition: opacity 0.3s ease; */

  &.show {
    opacity: 1;
  }
}
