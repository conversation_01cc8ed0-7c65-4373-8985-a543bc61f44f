/**
 * 判断数组中是否包含指定值
 * @param {Array} arr 数组
 * @param {Number} value 要查找的值
 * @returns {Boolean} 是否包含
 */
function isActive(arr, value) {
  if (!arr || arr.length === 0) {
    return false
  }

  for (var i = 0; i < arr.length; i++) {
    if (arr[i] === value) {
      return true
    }
  }
  return false
}
function getCardClass(index) {
  var str = "orange-card"
  switch (index) {
    case 1:
      str = "blue-card"
      break
    case 2:
      str = "light-blue-card"
      break
    case 3:
      str = "cyan-card"
      break
  }
  return str
}
function formattedNumber(num) {
  if (num) {
    return Number(num).toLocaleString()
  } else {
    return ""
  }
}

function getMainTypeText(type) {
  if (type == "pay_num") {
    return "缴费"
  }
  if (type == "approved_num") {
    return "过审"
  }
  return "报名"
}

module.exports = {
  isActive: isActive,
  getCardClass: getCardClass,
  formattedNumber: formattedNumber,
  getMainTypeText: getMainTypeText,
}
