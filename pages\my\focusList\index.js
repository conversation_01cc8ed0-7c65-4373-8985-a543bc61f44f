const ROUTER = require("@/services/mpRouter")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeIndex: 1,
    isSort: false, // 是否为排序模式
    isEdit: false, // 是否为编辑模式
    modalShow: false,
    hasSortChanged: false, // 是否有排序变化
    originalDataList: [], // 保存原始数据，用于取消时还原
    isAllSelected: false, // 是否全选
    selectedNotices: [], // 选中的公告ID列表
    // 公告数据列表
    noticeList: [
      {
        id: 1,
        title:
          "重庆两江新区人才发展集团有限公司派往重庆两江新区金山社区卫生服务中心招聘简章",
        status: "正在报名",
        statusType: "active", // active, coming, over, end
        totalCount: 12,
        positionCount: 4,
        suitableCount: 4,
        date: "2025.06.02",
        type: "withSuitable", // withSuitable, withDate, simple
        isSelected: false,
        hasBackground: true, // 是否有背景样式
      },
      {
        id: 2,
        title: "2025年事业单位考试合集",
        status: "即将报名",
        statusType: "coming",
        totalCount: 12,
        positionCount: 4,
        suitableCount: 4,
        type: "withSuitable",
        isSelected: false,
        hasBackground: false,
      },
      {
        id: 3,
        title: "2025年事业单位考试合集",
        status: "",
        statusType: "simple",
        date: "2025.06.02",
        type: "simple",
        isSelected: false,
        hasBackground: false,
      },
      {
        id: 4,
        title: "【招考】2025年二季度巫山县事业单位公开选调2人",
        status: "即将截止",
        statusType: "over",
        totalCount: 12,
        positionCount: 4,
        date: "2025.06.02",
        type: "withDate",
        isSelected: false,
        hasBackground: false,
      },
      {
        id: 5,
        title: "重庆大学自动化学院科研团队劳务派遣工作人员招聘启事",
        status: "报名结束",
        statusType: "end",
        totalCount: 12,
        positionCount: 4,
        date: "2025.06.02",
        type: "withDate",
        isSelected: false,
        hasBackground: false,
      },
    ],
    tabList: [
      {
        title: "公告",
        id: 1,
      },
      {
        title: "职位",
        id: 2,
      },
    ],
    dataList: [
      {
        titleText: "重庆大学自动化学院科研团队劳务派遣工作人员招聘启事",
        list: [
          {
            id: 1,
            title: "工程监督管理岗",
            status: "正在报名",
            applyCount: "24",
            recruitCount: "2",
            unit: "重庆市长寿区凤城街道办事处",
            announcement: "2025年陕西省事业单位联考公告汇总联考公…",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_announcement.png",
          },
          {
            id: 2,
            title: "招商引资岗",
            status: "即将截止",
            applyCount: "156",
            recruitCount: "1",
            unit: "重庆市长寿区发展改革委员会",
            announcement: "2025年重庆市长寿区事业单位公开招聘工作人员公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position.png",
          },
          {
            id: 3,
            title: "综合管理岗",
            status: "报名结束",
            applyCount: "89",
            recruitCount: "3",
            unit: "重庆市长寿区教育委员会",
            announcement: "2025年重庆市长寿区教育系统事业单位招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_announcement.png",
          },
          {
            id: 4,
            title: "财务管理岗",
            status: "正在报名",
            applyCount: "67",
            recruitCount: "1",
            unit: "重庆市长寿区财政局",
            announcement: "2025年重庆市长寿区财政系统招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position.png",
          },
          {
            id: 5,
            title: "信息技术岗",
            status: "即将截止",
            applyCount: "234",
            recruitCount: "2",
            unit: "重庆市长寿区大数据发展局",
            announcement: "2025年重庆市长寿区数字化建设人才招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_announcement.png",
          },
          {
            id: 6,
            title: "行政执法岗",
            status: "报名结束",
            applyCount: "445",
            recruitCount: "5",
            unit: "重庆市长寿区城市管理局",
            announcement: "2025年重庆市长寿区城管系统招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position.png",
          },
        ],
      },
      {
        titleText:
          "石阡县人民政府办公室关于印发石阡县2025事业单位公开招聘工作人员简章的通知公开招聘工作人员简章的通知公开招聘工作…",
        list: [
          {
            id: 7,
            title: "文秘岗",
            status: "正在报名",
            applyCount: "78",
            recruitCount: "2",
            unit: "石阡县人民政府办公室",
            announcement: "石阡县2025事业单位公开招聘工作人员简章",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_announcement.png",
          },
          {
            id: 8,
            title: "会计岗",
            status: "即将截止",
            applyCount: "123",
            recruitCount: "1",
            unit: "石阡县财政局",
            announcement: "石阡县财政系统2025年度招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position.png",
          },
          {
            id: 9,
            title: "教师岗",
            status: "报名结束",
            applyCount: "567",
            recruitCount: "10",
            unit: "石阡县教育局",
            announcement: "石阡县2025年中小学教师招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_announcement.png",
          },
          {
            id: 10,
            title: "医护岗",
            status: "正在报名",
            applyCount: "234",
            recruitCount: "8",
            unit: "石阡县人民医院",
            announcement: "石阡县2025年医疗卫生人才招聘公告",
            imgUrl:
              "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position.png",
          },
        ],
      },
    ],
    // 弹窗相关
    showOptionsPopup: false, // 显示操作选项弹窗
    showTagPopup: false, // 显示添加标签弹窗
    currentJobInfo: null, // 当前操作的职位信息
    // 一级弹窗选项数据
    optionsData: [
      { name: "添加标签", value: "addTag" },
      { name: "加入对比", value: "addToCompare" },
      { name: "取消关注", value: "unfollow" },
    ],
    // 二级弹窗选项数据
    tagOptionsData: [
      { name: "加入报考岗位", value: "addToApplication" },
      { name: "加入意向岗位", value: "addToIntention" },
    ],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options
    this.setData({
      activeIndex: id || 1,
    })
  },

  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      activeIndex: index,
    })
    // let activeIndex = this.data.tabList.findIndex(
    //   (item) => item.id == this.data.activeIndex
    // )
    // if (!this.data.tabList[activeIndex].dataList.length) {
    //   this.getList(1)
    // }
  },

  cancelOprate() {
    if (this.data.activeIndex == 1) {
      this.setData({
        isEdit: false,
        isAllSelected: false,
        selectedNotices: [],
      })
      // 重置所有公告的选中状态
      this.resetNoticeSelection()
    } else {
      // 还原到原始数据
      this.setData({
        isSort: false,
        hasSortChanged: false,
        dataList: JSON.parse(JSON.stringify(this.data.originalDataList)), // 深拷贝还原数据
      })
      // wx.showToast({
      //   title: "已取消排序",
      //   icon: "none",
      // })
    }
  },

  // 重置公告选中状态
  resetNoticeSelection() {
    const noticeList = this.data.noticeList.map((notice) => ({
      ...notice,
      isSelected: false,
    }))
    this.setData({
      noticeList,
      selectedNotices: [],
      isAllSelected: false,
    })
  },

  // 处理公告选中
  onNoticeSelect(e) {
    const { noticeId } = e.detail
    const noticeList = [...this.data.noticeList]
    const selectedNotices = [...this.data.selectedNotices]

    const noticeIndex = noticeList.findIndex((notice) => notice.id === noticeId)
    if (noticeIndex !== -1) {
      noticeList[noticeIndex].isSelected = !noticeList[noticeIndex].isSelected

      if (noticeList[noticeIndex].isSelected) {
        selectedNotices.push(noticeId)
      } else {
        const selectedIndex = selectedNotices.indexOf(noticeId)
        if (selectedIndex > -1) {
          selectedNotices.splice(selectedIndex, 1)
        }
      }

      // 检查是否全选
      const isAllSelected = noticeList.every((notice) => notice.isSelected)

      this.setData({
        noticeList,
        selectedNotices,
        isAllSelected,
      })
    }
  },

  // 处理全选
  onToggleSelectAll() {
    const isAllSelected = !this.data.isAllSelected
    const noticeList = this.data.noticeList.map((notice) => ({
      ...notice,
      isSelected: isAllSelected,
    }))
    const selectedNotices = isAllSelected
      ? noticeList.map((notice) => notice.id)
      : []

    this.setData({
      noticeList,
      selectedNotices,
      isAllSelected,
    })
  },
  cancelDelete() {
    this.setData({
      modalShow: false,
    })
  },

  // 移除关注
  removeSelectedNotices() {
    const { selectedNotices } = this.data
    const noticeList = this.data.noticeList.filter(
      (notice) => !selectedNotices.includes(notice.id)
    )
    this.setData({
      noticeList,
      selectedNotices: [],
      isAllSelected: false,
      isEdit: false,
      modalShow: false,
    })
    wx.showToast({
      title: "移除成功",
      icon: "none",
    })
  },

  // 操作按钮点击（编辑/排序/保存）
  oprate() {
    if (this.data.activeIndex == 1) {
      // 公告tab的编辑功能
      if (!this.data.isEdit) {
        this.setData({
          isEdit: true,
        })
      } else {
        const { selectedNotices } = this.data
        if (selectedNotices.length === 0) {
          return
        }
        this.setData({
          modalShow: true,
        })
      }
    } else {
      if (!this.data.isSort) {
        // 开始排序时，保存原始数据的备份
        this.setData({
          isSort: true,
          hasSortChanged: false,
          originalDataList: JSON.parse(JSON.stringify(this.data.dataList)), // 深拷贝保存原始数据
        })
      } else {
        // 保存排序
        this.saveSort()
      }
    }
  },

  // 保存排序
  saveSort() {
    if (!this.data.hasSortChanged) {
      return
    }
    this.setData({
      isSort: false,
      hasSortChanged: false,
      originalDataList: [], // 清空原始数据备份
    })
    wx.showToast({
      title: "排序已保存",
      icon: "none",
    })
  },

  // 处理职位卡片的三个点点击事件
  onJobOptionsClick(e) {
    const { jobId, jobTitle } = e.detail
    this.setData({
      currentJobInfo: {
        jobId: jobId,
        jobTitle: jobTitle,
      },
      showOptionsPopup: true,
    })
  },

  // 处理排序 - 向上移动
  onMoveUp(e) {
    const { groupIndex, jobIndex } = e.detail
    if (jobIndex > 0) {
      const dataList = [...this.data.dataList]
      const targetGroup = dataList[groupIndex]
      const jobList = [...targetGroup.list]

      // 交换位置
      ;[jobList[jobIndex], jobList[jobIndex - 1]] = [
        jobList[jobIndex - 1],
        jobList[jobIndex],
      ]

      targetGroup.list = jobList
      this.setData({
        dataList: dataList,
        hasSortChanged: true, // 标记已有排序变化
      })

      console.log("hasSortChanged设为true")
    }
  },

  // 处理排序 - 向下移动
  onMoveDown(e) {
    const { groupIndex, jobIndex } = e.detail
    const targetGroup = this.data.dataList[groupIndex]
    if (jobIndex < targetGroup.list.length - 1) {
      const dataList = [...this.data.dataList]
      const targetGroupCopy = dataList[groupIndex]
      const jobList = [...targetGroupCopy.list]
      // 交换位置
      ;[jobList[jobIndex], jobList[jobIndex + 1]] = [
        jobList[jobIndex + 1],
        jobList[jobIndex],
      ]

      targetGroupCopy.list = jobList
      this.setData({
        dataList: dataList,
        hasSortChanged: true, // 标记已有排序变化
      })
    }
  },

  // 处理一级弹窗选择事件
  onOptionsSelect(e) {
    const { value } = e.detail
    this.setData({
      showOptionsPopup: false,
    })

    switch (value) {
      case "addTag":
        this.setData({
          showTagPopup: true,
        })
        break
      case "addToCompare":
        wx.showToast({
          title: "已加入对比",
          icon: "success",
        })
        break
      case "unfollow":
        wx.showModal({
          title: "确认取消关注",
          content: `确定要取消关注"${this.data.currentJobInfo?.jobTitle}"吗？`,
          success: (res) => {
            if (res.confirm) {
              wx.showToast({
                title: "已取消关注",
                icon: "success",
              })
            }
          },
        })
        break
    }
  },

  // 处理二级弹窗选择事件
  onTagSelect(e) {
    const { value } = e.detail
    this.setData({
      showTagPopup: false,
    })

    let toastTitle = ""
    switch (value) {
      case "addToApplication":
        toastTitle = "已加入报考岗位"
        break
      case "addToIntention":
        toastTitle = "已加入意向岗位"
        break
    }

    if (toastTitle) {
      wx.showToast({
        title: toastTitle,
        icon: "success",
      })
    }
  },

  // 关闭操作选项弹窗
  closeOptionsPopup() {
    this.setData({
      showOptionsPopup: false,
    })
  },

  // 关闭添加标签弹窗
  closeTagPopup() {
    this.setData({
      showTagPopup: false,
    })
  },

  goComparison() {
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
