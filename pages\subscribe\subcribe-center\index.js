const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/mpRouter")
Page({
  data: {
    subscribedData: null,
    selectList: [],
    isPageRequest: false,
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    await this.getSubscribedData()
  },
  async onShow() {
    if (!this.data.isPageRequest) {
      return
    }
    await this.getSubscribedData()
    if (this.data.subscribedData.is_subscribe == 1) {
      ROUTER.redirectTo({
        path: "/pages/subscribe/list",
        query: {},
      })
    }
  },
  async getSubscribedData() {
    const res = await UTIL.request(API.getSubscribedData)
    if (res && res.error && res.error.code === 0 && res.data) {
      this.setData({
        selectList: res.data.list,
        subscribedData: res.data,
        isPageRequest: true,
      })
    }
  },

  tapMenuItem() {
    const cmd = this.data.subscribedData.subscribe_icons?.cmd_json
    console.log(cmd)
    APP.toCmdUnitKey(cmd)
  },

  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },
  onReachBottom() {},
})
