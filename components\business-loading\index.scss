.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.spinner {
  width: 75rpx;
  height: 75rpx;
  border: 8rpx solid #f3f3f3;
  border-top: 8rpx solid #e60003;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
