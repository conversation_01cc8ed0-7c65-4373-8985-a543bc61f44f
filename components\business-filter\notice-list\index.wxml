<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>

<view wx:for="{{list}}" wx:key="id" class="select-item {{filterUtils.isSelectOptionSelected(item.id, tempSelected)? 'selected' : ''}}" bindtap="handleExamSelect" data-item="{{item}}">
  <text class="item-title text-ellipsis-1">{{item.title}}</text>
  <image wx:if="{{filterUtils.isSelectOptionSelected(item.id, tempSelected)}}" class="check-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_yuan_g.png"></image>
</view>