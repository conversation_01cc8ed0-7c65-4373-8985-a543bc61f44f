page {
  box-sizing: border-box;
  background: #fff;
  background-repeat: no-repeat;
  background-size: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100vh;
}

.lefts {
  display: flex;
  align-items: center;
  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.main-content {
  display: flex;
  flex: 1;
  min-height: 0;
  flex-direction: column;
  padding-top: 140rpx;
  // padding: 0 64rpx;
  .title {
    font-size: 36rpx;
    color: #3c3d42;
    text-align: center;
    width: 400rpx;
    margin: 0 auto;
  }

  .tip-xin-list {
    .tip-title {
      font-size: 28rpx;
      margin-bottom: 24rpx;
      color: #666666;
    }
    .tip-xin {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #3c3d42;
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .img {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
    }
  }
  .tip-box {
    background: #f7f8fa;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-top: 80rpx;
  }
  .content-flex {
    padding: 0 64rpx;
    flex: 1;
    min-height: 0;
  }
}

.imgs {
  width: 240rpx;
  height: 240rpx;
  margin: 0 auto;
  display: block;
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;
  // border-top: 1rpx solid rgba(235, 236, 240, 1);
}

.action-bar {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.confirm-btn {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 80rpx;
}
