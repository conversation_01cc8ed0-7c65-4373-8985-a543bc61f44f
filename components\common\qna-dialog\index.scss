.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 600rpx;
  // height: 336rpx;
  box-sizing: border-box;
  border-radius: 15rpx;
  background-color: #fff;
  .title {
    font-size: 32rpx;
    color: #22242e;
    text-align: center;
    font-weight: 500;
    padding: 90rpx 64rpx;
  }
  .bottom-box {
    display: flex;
    align-items: center;
    border-top: 0.5px solid #ebecf0;
    .item {
      font-size: 32rpx;
      padding: 32rpx 0;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-child {
        border-right: 0.5px solid #ebecf0;
      }
      &:last-child {
        color: #d62828;
      }
    }
  }
}

.title-more {
  padding: 0 60rpx;
  padding-bottom: 60rpx;
  .title {
    padding-bottom: 0 !important;
  }
  .sub-title {
    font-size: 28rpx;
    text-align: center;
    margin-top: 30rpx;
    color: rgba(102, 102, 102, 1);
  }
}
