<navigation-bar back="{{true}}" background="#ffffff">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">我的关注</view>
  </view>
</navigation-bar>
<view class="focus-list">
  <view class="tab-list {{ tabList.length < 3?'pd-style':''}}">
    <view wx:for="{{tabList}}" wx:key="index" class="tab-list-item {{ activeIndex == item.id?'active':'' }}" bind:tap="changeIndex" data-index="{{ item.id }}">
      <text class="text">{{item.title}}</text>
    </view>
  </view>

  <scroll-view scroll-y="{{true}}" class="scroll-list">
    <view class="list-area" wx:if="{{(activeIndex == 1 && noticeList.length) || (activeIndex == 2 && dataList.length)}}">
      <!-- 公告列表 -->
      <view class="job-list mt32" wx:if="{{activeIndex == 1}}">
        <notice-foucus-card wx:for="{{noticeList}}" wx:key="id" wx:for-item="notice" wx:for-index="noticeIndex" isEdit="{{isEdit}}" noticeData="{{notice}}" bind:selectNotice="onNoticeSelect" />
      </view>

      <!-- 职位列表 -->
      <view wx:elif="{{activeIndex == 2}}" class="job-list-area">
        <view class="list-item" wx:for="{{ dataList }}" wx:key="index" wx:for-item="group" wx:for-index="groupIndex">
          <view class="title-text">{{group.titleText}}</view>
          <view class="job-list">
            <job-foucus-card wx:for="{{group.list}}" wx:key="id" wx:for-item="job" wx:for-index="jobIndex" isSort="{{ isSort }}" jobData="{{ job }}" groupIndex="{{ groupIndex }}" jobIndex="{{ jobIndex }}" topCanMove="{{ isSort && jobIndex > 0 }}" bottomCanMove="{{ isSort && jobIndex < group.list.length - 1 }}" bind:optionsClick="onJobOptionsClick" bind:moveUp="onMoveUp" bind:moveDown="onMoveDown" />
          </view>
        </view>
      </view>
    </view>
    <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" text="暂无数据"></empty-default>
  </scroll-view>

  <!-- 底部操作栏 -->
  <tabbar-box>
    <block wx:if="{{activeIndex == 1}}">
      <block wx:if="{{!isEdit}}">
        <view class="bottom-btn" bindtap="oprate">编辑</view>
      </block>
      <block wx:else>
        <view class="edit-area">
          <view class="left" bind:tap="onToggleSelectAll">
            <image class="icon" wx:if="{{isAllSelected}}" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_notice_selected.png" mode="" />
            <image class="icon" wx:else src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/my_notice_select.png" mode="" />
            <view class="text">全选</view>
          </view>
          <view class="right">
            <view class="right-btn cancel-btn" bind:tap="cancelOprate">取消</view>
            <view class="right-btn {{selectedNotices.length > 0 ? 'active' : 'disabled'}}" bind:tap="oprate">移除关注{{selectedNotices.length > 0 ? '(' + selectedNotices.length + ')' : ''}}</view>
          </view>
        </view>
      </block>
    </block>
    <block wx:else>
      <block wx:if="{{!isSort}}">
        <view class="bottom-btn" bindtap="oprate">排序</view>
      </block>
      <block wx:else>
        <view class="sort-area">
          <view class="btn cancel-btn" bind:tap="cancelOprate">取消</view>
          <view class="btn {{hasSortChanged ? 'active' : 'disabled'}}" bindtap="oprate">保存</view>
        </view>
      </block>
    </block>
  </tabbar-box>

  <!-- 悬浮小窗口 -->

  <view wx:if="{{ activeIndex == 2 && !isSort}}" class="compare-ball" catch:tap="goComparison">
    <view class="img-area">
      <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/pk.png" mode="" />
      <view class="message">3</view>
    </view>
    <view class="text">对比</view>
  </view>
</view>

<!-- 一级弹窗：操作选项 -->
<action-selector visible="{{showOptionsPopup}}" options="{{optionsData}}" bind:select="onOptionsSelect" bind:close="closeOptionsPopup" bind:cancel="closeOptionsPopup" />

<!-- 二级弹窗：添加标签 -->
<action-selector visible="{{showTagPopup}}" options="{{tagOptionsData}}" bind:select="onTagSelect" bind:close="closeTagPopup" bind:cancel="closeTagPopup" />


<modal-default show="{{modalShow}}" title="是否要移除该职位" bind:confirm="removeSelectedNotices" bind:cancel="cancelDelete"></modal-default>