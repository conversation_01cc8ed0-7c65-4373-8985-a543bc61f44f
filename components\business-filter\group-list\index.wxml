<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>
<!-- 公告Tab的筛选 -->
<view class="group-list" wx:for="{{dataList}}" wx:key="index" wx:for-index="groupIndex">
  <view class="title">
    {{item.title}}
    <text class="label-text" wx:if="{{item.is_radio===2}}">多选</text>
  </view>
  <view class="group-list-item">
    <view class="group-item {{filterUtils.isFilterItemOptionSelected(citem.value, tempSelected[item.filter_key]) ? 'active' : ''}}" wx:for="{{item.list}}" wx:key="value" wx:for-index="cindex" wx:for-item="citem" bindtap="handleOptionSelect" data-value="{{citem.value}}" data-key="{{item.filter_key}}">
      {{citem.name}}
    </view>
  </view>
</view>
<popu-bottom bind:reset="handleReset" bind:confirm="handleConfirm" />